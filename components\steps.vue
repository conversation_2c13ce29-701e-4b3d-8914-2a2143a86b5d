<template>
	<view class="steps">
		<u-steps :current="currentStep" activeColor="#1386fb">
			<u-steps-item title="方案选择" @click="nav1"></u-steps-item>
			<u-steps-item title="营业执照" @click="nav2"></u-steps-item>
			<u-steps-item title="法人身份证" @click="nav3"></u-steps-item>
			<u-steps-item title="店铺场景" @click="nav4"></u-steps-item>
			<u-steps-item title="结算卡" @click="nav5"></u-steps-item>
		</u-steps>
	</view>
</template>

<script setup lang="ts">
import { defineProps, ref, onMounted } from 'vue';

defineProps({
	currentStep: {
		type: Number,
		default: 0
	}
});

const statusBarHeight = ref(0);
/** 跳转的页面字段 */
const page = ref(0);
/** 获取手机顶部状态栏高度 */
function otgetSystemInfo() {
	uni.getSystemInfo({
		success: function (res) {
			console.log(res);
			statusBarHeight.value = res.statusBarHeight;
		}
	});
}

function nav1() {
	if (page.value >= 1) {
		uni.redirectTo({
			url: '/pages/solutionSelection/solutionSelection'
		});
	}
}
function nav2() {
	if (page.value >= 2) {
		uni.redirectTo({
			url: '/pages/identityCard/identityCard'
		});
	}
}
function nav3() {
	if (page.value >= 3) {
		uni.redirectTo({
			url: '/pages/corporateIdentityCard/corporateIdentityCard'
		});
	}
}
function nav4() {
	if (page.value >= 4) {
		uni.redirectTo({
			url: '/pages/shopScene/shopScene'
		});
	}
}
function nav5() {
	if (page.value >= 5) {
		uni.redirectTo({
			url: '/pages/debitCard/debitCard'
		});
	}
}

onMounted(() => {
	otgetSystemInfo();
	// 获取本地保存的 save_page 值
	const savedPage = uni.getStorageSync('save_page') || 0;
	page.value = savedPage;
});
</script>

<style lang="scss" scoped>
::v-deep .u-text__value {
	font-size: 24rpx !important;
}
::v-deep .u-steps--row {
	padding-top: 45rpx;
	height: 120rpx;
}
.steps {
	width: 100%;
	background-color: #ffffff;
	z-index: 10;
	position: fixed;
	top: var(statusBarHeight) + 'px';
}
</style>
