<template>
	<view class="page-my-container">
		<!-- 导航栏 -->
		<navbar title="" leftIcon=" " :isAutoBack="false" :height="0" />
		<view class="page-my-top">
			<view class="flex-sty2" style="padding-top: 64rpx;">
				<view class="img-box">
					<image v-if="userStore.userInfo.salesman_avatar" class="img1" :src="baseURL + userStore.userInfo.salesman_avatar" mode=""></image>
					<image v-else class="img1" :src="noImg" mode=""></image>
				</view>
				<view class="my-txt1">{{userStore.userInfo.name}}</view>
				<image v-if="userStore.userInfo.salesman_position == '业务经理'" style="width: 104rpx;height: 40rpx;"
					src="@/static/images/ywjl.png" mode=""></image>
				<image v-if="userStore.userInfo.salesman_position == '业务主管'" style="width: 104rpx;height: 40rpx;"
					src="@/static/images/ywzg.png" mode=""></image>
				<image v-if="userStore.userInfo.salesman_position == '业务专员'" style="width: 104rpx;height: 40rpx;"
					src="@/static/images/ywzy.png" mode=""></image>
				<image v-if="userStore.userInfo.salesman_position == '业务总监'" style="width: 104rpx;height: 40rpx;"
					src="@/static/images/ywzj.png" mode=""></image>
			</view>
			<view class="page-my-t-b">
				<view class="page-my-t-b-item flex-sty1">
					<view class="my-txt2">月均开户数</view>
					<view class="my-txt3">{{userStore.userInfo.avg_month_merchant}}</view>
				</view>
				<view class="page-my-t-b-item flex-sty1">
					<view class="my-txt2">总计开户数</view>
					<view class="my-txt3">{{userStore.userInfo.count_merchant}}</view>
				</view>
			</view>
		</view>

		<view class="page-my-bot">
			<view class="page-my-bot-b">
				<view class="page-list-item flex-sty">
					<view class="flex-sty2">
						<image style="width: 44rpx;height: 44rpx;" src="@/static/images/icon3.png" mode=""></image>
						<view class="my-txt4">所属团队</view>
					</view>
					<view class="my-txt5">{{userStore.userInfo.team_name}}</view>
				</view>
				<view class="page-list-item flex-sty" @click="jumpTo('/pages/my/editingInfo')">
					<view class="flex-sty2">
						<image style="width: 44rpx;height: 44rpx;" src="@/static/images/icon4.png" mode=""></image>
						<view class="my-txt4">编辑资料</view>
					</view>
					<uni-icons type="right" size="20" color="#A0A8B0"></uni-icons>
				</view>
				<view class="page-list-item flex-sty" @click="jumpTo('/pages/my/account')">
					<view class="flex-sty2">
						<image style="width: 44rpx;height: 44rpx;" src="@/static/images/icon5.png" mode=""></image>
						<view class="my-txt4">账号与安全</view>
					</view>
					<uni-icons type="right" size="20" color="#A0A8B0"></uni-icons>
				</view>
				<view class="page-list-item flex-sty border-none" @click="jumpTo('/pages/my/about')">
					<view class="flex-sty2">
						<image style="width: 44rpx;height: 44rpx;" src="@/static/images/icon6.png" mode=""></image>
						<view class="my-txt4">关于</view>
					</view>
					<uni-icons type="right" size="20" color="#A0A8B0"></uni-icons>
				</view>
			</view>
		</view>
		<view class="btn-box">
			<view class="btn1" @click="backHandel">退出登录</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, reactive, computed, onMounted,inject } from 'vue';
	import useUserStore from '@/store/user';
	import noImg from '@/static/images/noAvatar.png'
	
	const baseURL = inject('baseURL');
	const userStore = useUserStore();

	/** 页面跳转 */
	function jumpTo(url) {
		uni.navigateTo({
			url: url
		});
	}

	/** 退出登录 */
	function backHandel() {
		uni.utils.toast('退出登录成功')
		setTimeout(function () {
			// 清除用户pinia里的数据
			userStore.token = null
			userStore.userInfo = {}
			/** 清除本地token*/
			uni.removeStorageSync('token');
			uni.reLaunch({
				url: '/pages/login/index'
			});
		}, 1000);
	}

	onMounted(() => {
		userStore.otgetUserInfoApi()
	})
</script>

<style lang="scss" scoped>
	.flex-sty {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.flex-sty1 {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.flex-sty2 {
		display: flex;
		align-items: center;
	}

	.page-my-container {
		width: 100%;
		height: 100%;

		.page-my-top {
			width: 100%;
			height: 548rpx;
			box-sizing: border-box;
			padding: 0 32rpx;
			background-image: url('../../static/images/bg3.png');
			background-repeat: no-repeat;
			background-position: center center;

			.img-box {
				width: 95rpx;
				height: 95rpx;
				background: #fff;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;

				.img1 {
					width: 87rpx;
					height: 87rpx;
					border-radius: 50%;
				}
			}

			.my-txt1 {
				margin-left: 20rpx;
				margin-right: 8rpx;
				max-width: 160rpx;
				height: 56rpx;
				font-weight: 600;
				font-size: 40rpx;
				color: #FFFFFF;
				line-height: 56rpx;
				font-style: normal;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			.page-my-t-b {
				margin-top: 36rpx;
				width: 100%;
				height: 168rpx;
				border-radius: 24rpx;
				box-sizing: border-box;
				padding: 24rpx;
				background-color: #3296FC;
				display: flex;

				.page-my-t-b-item {
					width: 50%;
				}

				.my-txt2 {
					height: 40rpx;
					font-weight: 600;
					font-size: 24rpx;
					color: #FFFFFF;
					line-height: 40rpx;
					font-style: normal;
				}

				.my-txt3 {
					width: 100%;
					height: 56rpx;
					font-weight: 600;
					font-size: 40rpx;
					color: #FFFFFF;
					line-height: 56rpx;
					font-style: normal;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
		}

		.page-my-bot {
			position: absolute;
			top: 488rpx;
			/* #ifdef H5 */
			top: 430rpx;
			/* #endif */
			left: 0;
			right: 0;
			width: 100%;
			height: 800rpx;
			background: #F0F2F5;
			border-radius: 24rpx 24rpx 0rpx 0rpx;
			box-sizing: border-box;
			padding: 40rpx 32rpx;

			.page-my-bot-b {
				width: 100%;
				height: 416rpx;
				background: #FFFFFF;
				box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
				border-radius: 16rpx;
				border: 1rpx solid #EDF0F5;
				box-sizing: border-box;
				padding: 32rpx;

				.page-list-item {
					width: 100%;
					height: 92rpx;
					line-height: 92rpx;
					border-bottom: 1rpx solid #DEE2E8;
				}

				.border-none {
					border-bottom: none;
				}

				.my-txt4 {
					margin-left: 16rpx;
					font-weight: 400;
					font-size: 28rpx;
					color: $uni-main-color;
					font-style: normal;
				}

				.my-txt5 {
					width: 50%;
					font-weight: 400;
					font-size: 28rpx;
					color: $uni-secondary-color;
					text-align: right;
					font-style: normal;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
		}

		.btn-box {
			position: absolute;
			left: 0;
			bottom: 0;
			width: 100%;
			height: 120rpx;
			box-sizing: border-box;
			padding: 0 32rpx;
			background-color: #F0F2F5;

			.btn1 {
				width: 100%;
				height: 88rpx;
				line-height: 88rpx;
				background: #DEE2E8;
				border-radius: 8rpx;
				text-align: center;
				font-weight: 600;
				font-size: 26rpx;
				color: #575E65;
				font-style: normal;
			}
		}
	}
</style>