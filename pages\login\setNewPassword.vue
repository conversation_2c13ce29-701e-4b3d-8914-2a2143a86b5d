<template>
	<view class="page-login-container">
		<!-- 导航栏 -->
		<navbar title="" />
		<view class="login-txt1 margin-sty">设置新密码</view>
		<view class="login-txt3 margin-sty">新密码</view>
		<up-input placeholder="请输入新密码" :password="showNewPassword" v-model="new_password" border="none">
			<template #suffix>
				<up-icon @click="showNewPassword = !showNewPassword" :name="showNewPassword?'eye-off':'eye-fill'" color="#9DA5AD" size="24"></up-icon>
			</template>
		</up-input>
		<view class="login-txt3 margin-sty1">确认密码</view>
		<up-input placeholder="请再次输入新密码" :password="showConPassword" v-model="confirm_password" border="none">
			<template #suffix>
				<up-icon @click="showConPassword = !showConPassword" :name="showConPassword?'eye-off':'eye-fill'" color="#9DA5AD" size="24"></up-icon>
			</template>
		</up-input>
		<view class="page-btn-box">
			<view class="btn-box" :class="showLoginBtn?'btn-box1':'btn-box'" @click="okHandel">确认并登录</view>
			<view class="login-txt7 margin-sty1">登录即代表您已阅读并同意<text class="login-txt8" @click="jumpTo('/pages/my/userAgreement')">《用户协议》</text>和
			<text class="login-txt8" @click="jumpTo('/pages/my/privacyPolicy')">《隐私政策》</text></view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, computed } from 'vue'
	import navbar from '@/components/navbar/navbar.vue'
	import useUserStore from '@/store/user'
	import { onLoad } from '@dcloudio/uni-app'

	const userStore = useUserStore()
	const phone = ref('')
	const tmpd = ref('')
	const new_password = ref('')
	const confirm_password = ref('')
	const showNewPassword = ref(true)
	const showConPassword = ref(true)

	let showLoginBtn = computed(() => {
		if (new_password.value && confirm_password.value) {
			return true
		} else {
			return false
		}
	})

	//确认按钮
	function okHandel() {
		if (showLoginBtn.value) {
			if (new_password.value !== confirm_password.value) return uni.utils.toast('两次密码输入不一致');
			const data = {
				phone: phone.value,
				tmpd: tmpd.value,
				new_password: new_password.value,
				confirm_password: confirm_password.value,
			}
			// 从pinia调用login函数传递账号密码
			userStore.otforgetPasswordApi(data)
		}
	}

	/** 页面跳转 */
	function jumpTo(url) {
		uni.navigateTo({
			url: url
		});
	}
	
	onLoad((options) => {
		if (options.phone) {
			phone.value = options.phone
		}
		if (options.tmpd) {
			tmpd.value = options.tmpd
		}
	});
</script>

<style lang="scss" scoped>
	::v-deep .u-input {
		height: 96rpx !important;
		background: #F0F2F5 !important;
		border-radius: 16rpx !important;
		box-sizing: border-box !important;
		padding: 0 32rpx !important;
	}
	
	::v-deep .uni-input-input {
		font-weight: 500 !important;
		font-size: 32rpx !important;
		color: #18191A !important;
	}
	
	::v-deep .uni-input-placeholder {
		font-weight: 400 !important;
		font-size: 32rpx !important;
		color: #9DA5AD !important;
	}

	.margin-sty {
		margin-top: 48rpx;
	}
	
	.margin-sty1{
		margin-top: 32rpx;
	}

	.page-login-container {
		width: 100%;
		height: 100%;
		background: #F0F2F5;
		color: #18191A;
		font-style: normal;
		box-sizing: border-box;
		padding: 0 32rpx;

		.login-txt1 {
			font-weight: 500;
			font-size: 44rpx;
			color: #18191A;
			line-height: 60rpx;
			text-align: center;
		}

		.login-txt2 {
			margin-top: 16rpx;
			font-size: 28rpx;
			line-height: 44rpx;
		}

		.login-txt3 {
			margin-bottom: 16rpx;
			font-weight: 600;
			font-size: 28rpx;
			color: #18191A;
			line-height: 44rpx;
		}

		.login-txt4 {
			font-weight: 600;
			font-size: 28rpx;
			color: #3B8FFF;
		}

		.login-txt5 {
			font-weight: 500;
			font-size: 28rpx;
			color: #8B99AA;
		}

		.login-txt6 {
			color: #3B8FFF;
		}

		.login-txt7 {
			width: 100%;
			font-weight: 400;
			font-size: 24rpx;
			color: #9DA5AD;
			line-height: 40rpx;
			text-align: center;
		}

		.login-txt8 {
			font-weight: 500;
			font-size: 24rpx;
			color: #3B8FFF;
			line-height: 40rpx;
		}
		
		.page-btn-box{
			width: 100%;
			height: 160rpx;
			position: fixed;
			left: 0;
			bottom: 48rpx;
			box-sizing: border-box;
			padding: 0 32rpx;
			
			.btn-box {
				width: 100%;
				height: 88rpx;
				line-height: 88rpx;
				background: #DEE2E8;
				border-radius: 8rpx;
				text-align: center;
				font-weight: 500;
				font-size: 26rpx;
				color: #9DA5AD;
			}
			
			.btn-box1 {
				background: #1386FB;
				color: #FFFFFF;
			}
		}
	}
</style>