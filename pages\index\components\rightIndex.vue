<template>
	<view class="container-box">
		<view class="container-top">
			<view class="txt1">团队业绩排名</view>
			<view class="box-con">
				<view class="" v-for="item in selected" :key="item">
					<view class="box-con-item" @click="selectedTabFn(item)" :class="selectedTab == item.type?'active':''">
						{{item.name}}
					</view>
				</view>
			</view>
			<view class="flex-sty">
				<view class="txt2">姓名</view>
				<view class="txt2">开户数</view>
			</view>
			<view class="box1-con" v-for="(item,index) in props.teamList" :key="item">
				<view class="flex-sty">
					<image v-if="index == 0" style="width: 32rpx;height: 38rpx;" src="@/static/images/first.png" mode=""></image>
					<image v-if="index == 1" style="width: 32rpx;height: 38rpx;" src="@/static/images/second.png" mode=""></image>
					<image v-if="index == 2" style="width: 32rpx;height: 38rpx;" src="@/static/images/third.png" mode=""></image>
					<image v-if="item.salesman_avatar" style="width: 48rpx;height: 48rpx;border-radius: 50%;margin-left: 32rpx;margin-right: 8rpx;"
						:src="baseURL + item.salesman_avatar" mode=""></image>
					<image v-else style="width: 48rpx;height: 48rpx;border-radius: 50%;margin-left: 32rpx;margin-right: 8rpx;"
						:src="noImg" mode=""></image>
					<view class="txt3">{{item.name}}</view>
				</view>
				<view class="txt4">{{item.count_merchant}}</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, reactive, computed, onMounted,inject } from 'vue';
	import noImg from '@/static/images/noAvatar.png'
	
	const props = defineProps({
		teamList: {
			type: Array,
			default: [{salesman_avatar: null , name: null, count_merchant: null }]
		}
	})

	const emit = defineEmits(['getType'])

	const baseURL = inject('baseURL');

	const selected = reactive([
		{ name: '今日', type: 'today' },
		{ name: '本周', type: 'week' },
		{ name: '本月', type: 'month' },
	]);

	/** 选中的标签 */
	const selectedTab = ref('today');
	const selectedTabFn = (item : any) => {
		selectedTab.value = item.type; // 更新选中的标签
		emit('getType', item.type)
	};

	let list = ref([{ name: '百度', num: 2 }, { name: '百度1', num: 2 }, { name: '百度2', num: 2 }])

	onMounted(() => {
		/** 默认选中第一个 */
		selectedTabFn(selected[0])
	})
</script>

<style lang="scss" scoped>
	.flex-sty {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.flex-sty1 {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.active {
		color: $u-primary !important;
		background: #FFFFFF;
	}

	.container-box {
		width: 49%;
		height: 444rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
		border-radius: 16rpx;
		border: 1rpx solid #EDF0F5;
		box-sizing: border-box;
		padding: 22rpx;

		.txt1 {
			height: 44rpx;
			font-weight: 600;
			font-size: 28rpx;
			color: #18191A;
			line-height: 44rpx;
			font-style: normal;
		}

		.txt2 {
			margin-top: 16rpx;
			height: 40rpx;
			font-weight: 400;
			font-size: 22rpx;
			color: #9DA5AD;
			line-height: 40rpx;
			font-style: normal;
		}

		.txt3 {
			width: 90rpx;
			height: 32rpx;
			font-weight: 500;
			font-size: 24rpx;
			color: #575E65;
			line-height: 32rpx;
			font-style: normal;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}

		.txt4 {
			width: 60rpx;
			height: 32rpx;
			font-weight: 500;
			font-size: 24rpx;
			color: #1386FB;
			line-height: 32rpx;
			text-align: right;
			font-style: normal;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}

		.box-con {
			margin-top: 16rpx;
			width: 100%;
			height: 48rpx;
			background: #F0F2F5;
			border-radius: 4rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			box-sizing: border-box;
			padding: 4rpx;

			.box-con-item {
				width: 84rpx;
				height: 40rpx;
				line-height: 40rpx;
				border-radius: 2rpx;
				font-weight: 500;
				font-size: 22rpx;
				color: $uni-secondary-color;
				text-align: center;
				font-style: normal;
			}
		}

		.box1-con {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 80rpx;
			box-sizing: border-box;
			border-top: 1rpx solid #DEE2E8;
			padding: 16rpx 8rpx;
		}
	}
</style>