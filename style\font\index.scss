@font-face {
  font-family: '阿里妈妈方圆体 VF Regular';
  src: url('@/style/font/alimama/AlimamaFangYuanTiVF-Thin.woff2') format('woff2'), url('@/style/font/alimama/AlimamaFangYuanTiVF-Thin.woff') format('woff');
  font-display: swap;
}

@font-face {
  font-family: "customicons"; /* Project id 2878519 */
  src:url('@/style/font/customicons.ttf') format('truetype');
}

.customicons {
  font-family: "customicons" !important;
}

.youxi:before {
  content: "\e60e";
}

.wenjian:before {
  content: "\e60f";
}

.zhuanfa:before {
  content: "\e610";
}