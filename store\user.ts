// 实现用户相关的数据的持久化
import { defineStore } from 'pinia';
import { ref } from 'vue';
import { loginApi, getUserInfoApi,forgetPasswordApi } from '@/api/user';
//1.定义Store
const useUserStore = defineStore(
	'user',
	() => {
		//定义state 即 ref

		// 用户token
		const token = ref<any>('');
		// 用户信息
		const userInfo = ref<any>({});

		async function login(params : any) {
			// 发送登录请求
			const res : any = await loginApi(params);
			// 判断状态码如果是200就跳转首页并存token
			if (res.code == 200) {
				uni.utils.toast(res.msg)
				// 存取token
				token.value = res.data.salesman_token;
				/** 把token存储到本地*/
				uni.setStorageSync('token', res.data.salesman_token);
				// 获取存取用户信息
				otgetUserInfoApi().then(()=>{
					// 跳转页面
					uni.reLaunch({
						url: '/pages/index/index'
					});
				})
			} else {
				uni.utils.toast(res.msg)
			}
		}
		
		async function otforgetPasswordApi(params : any) {
			// 发送登录请求
			const res : any = await forgetPasswordApi(params);
			// 判断状态码如果是200就跳转首页并存token
			if (res.code == 200) {
				uni.utils.toast(res.msg)
				// 存取token
				token.value = res.data.salesman_token;
				/** 把token存储到本地*/
				uni.setStorageSync('token', res.data.salesman_token);
				// 获取存取用户信息
				otgetUserInfoApi().then(()=>{
					// 跳转页面
					uni.reLaunch({
						url: '/pages/index/index'
					});
				})
			} else {
				uni.utils.toast(res.msg)
			}
		}

		/** 获取用户信息 */
		async function otgetUserInfoApi() {
			const res : any = await getUserInfoApi()
			if (res.code == 200) {
				userInfo.value = res.data
			} else {
				uni.utils.toast(res.msg)
			}
		}

		//一定不要忘了 暴露出去
		return { token, userInfo, login, otgetUserInfoApi,otforgetPasswordApi };
	},
	{
		persist: {
			paths: ['token', 'userInfo'] //配置指定保存数据的字段
		}
	}
);
//2.默认导出
export default useUserStore;