<template>
	<view class="page-pwd-container">
		<!-- 导航栏 -->
		<navbar title="设置密码" />
		<up-form class="pwd-form1" labelPosition="top" :model="baseFormData" :rules="rules" ref="baseForm" labelWidth="100" errorType="toast">
			<up-form-item label="旧密码" prop="old_password">
				<up-input v-model="baseFormData.old_password" placeholder="请输入旧密码" border="none"
					:password="showPassword1"></up-input>
				<template #right>
					<uni-icons @click="changePassword1" color="#9DA5AD;" :type="showPassword1 ? 'eye-slash' : 'eye'"
						size="22"></uni-icons>
				</template>
			</up-form-item>
			<up-form-item label="新密码" prop="new_password">
				<up-input v-model="baseFormData.new_password" placeholder="请输入新密码" border="none"
					:password="showPassword2"></up-input>
				<template #right>
					<uni-icons @click="changePassword2" color="#9DA5AD;" :type="showPassword2 ? 'eye-slash' : 'eye'"
						size="22"></uni-icons>
				</template>
			</up-form-item>
			<up-form-item label="确认密码" prop="confirm_password" scroll-into-view="confirm-password">
				<up-input v-model="baseFormData.confirm_password" placeholder="请输入确认密码" border="none"
					:password="showPassword3"></up-input>
				<template #right>
					<uni-icons @click="changePassword3" color="#9DA5AD;" :type="showPassword3 ? 'eye-slash' : 'eye'"
						size="22"></uni-icons>
				</template>
			</up-form-item>
		</up-form>
		
		<view class="btn-box" scroll-into-view="confirm-password">
			<view v-if="showLoginBtn" class="btn1 btn2" @click="formSubmit">完成</view>
			<view v-else class="btn1">完成</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, reactive, computed, watch } from 'vue';
	import navbar from '@/components/navbar/navbar.vue';
	import { updatePwApi } from '@/api/user';

	const showPassword1 = ref(true)
	const showPassword2 = ref(true)
	const showPassword3 = ref(true)

	const baseFormData = ref({
		old_password: '',
		new_password: '',
		confirm_password: ''
	});

	const rules = reactive({
		old_password: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
		new_password: [{ required: true, message: '请输入新密码', trigger: 'blur' }],
		confirm_password: [{ required: true, message: '请输入确认密码', trigger: 'blur' }],
	})

	let showLoginBtn = computed(() => {
		if (baseFormData.value.old_password && baseFormData.value.new_password && baseFormData.value.confirm_password) {
			return true
		} else {
			return false
		}
	})

	function changePassword1() {
		showPassword1.value = !showPassword1.value
	}

	function changePassword2() {
		showPassword2.value = !showPassword2.value
	}

	function changePassword3() {
		showPassword3.value = !showPassword3.value
	}

	// 表单引用
	const baseForm = ref(null);
	/** 完成按钮 */
	function formSubmit() {
		baseForm.value.validate().then((valid : any) => {
			if (valid) {
				otupdatePwApi()
			} else {

			}
		})
	}
	
	/** 修改密码接口 */
	async function otupdatePwApi(){
		const res = await updatePwApi(baseFormData.value)
			if (res.code == 200) {
				uni.utils.toast(res.msg)
				baseForm.value.resetFields()
				setTimeout(() => {
					uni.redirectTo({
						url: '/pages/my/account'
					})
				}, 1000)
			} else {
				uni.utils.toast(res.msg)
			}
	}
</script>

<style lang="scss" scoped>
	::v-deep .u-form-item__body__left {
		padding: 10rpx 0;
		font-weight: 600;
		font-size: 28rpx;
		color: $uni-main-color;
		font-style: normal;
	}

	::v-deep .u-form-item__body__right {
		padding: 20rpx;
		background: #FFFFFF;
		border-radius: 16rpx;
		font-weight: 500;
		font-size: 32rpx !important;
		color: $uni-main-color;
	}

	::v-deep .uni-input-input {
		font-weight: 500;
		font-size: 32rpx !important;
		color: $uni-main-color;
	}

	::v-deep .uni-input-placeholde {
		color: $uni-secondary-color;
	}

	.page-pwd-container {
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		padding: 32rpx;
		
		.pwd-form1{
			margin-bottom: 200rpx;
		}

		.btn-box {
			position: fixed;
			left: 0;
			bottom: 0;
			width: 100%;
			height: 188rpx;
			box-sizing: border-box;
			padding: 0 32rpx;
			background-color: #f6f8fc;

			.btn1 {
				width: 100%;
				height: 88rpx;
				line-height: 88rpx;
				background: #B3D8FF;
				border-radius: 8rpx;
				font-weight: 500;
				font-size: 26rpx;
				color: #FFFFFF;
				text-align: center;
				font-style: normal;
			}

			.btn2 {
				background-color: $uni-primary;
			}
		}
	}
</style>