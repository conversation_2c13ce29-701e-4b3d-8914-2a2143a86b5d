/** 提交结算卡 */
export const submitDebitCard = (data: any) => uni.$http.post('tenant_salesman.Incoming/perfectMerchantSettlement', data);

// /** 开户支行 */
// export const getBankBranch = (data: any,city: any,bank: any) => uni.$http.get(`tenant_salesman.Incoming/getCorporbankOption?address=${data}&city=${city}&bank=${bank}`);

/** 开户行 */
export const getBankBranch = (keywords: any) => uni.$http.get(`tenant_salesman.incoming/getcorporbanktrunkname?keywords=${keywords}`);

/** 获取省市区 */
export const getProvince = () => uni.$http.get(`tenant_salesman.incoming/getcorporbankprovincecity`);

/** 获取开户支行 */
export const getBranch = (bank_name: any, province_name: any, city_name: any, keywords: any, page: any, limit: any) =>
	uni.$http.get(
		`tenant_salesman.incoming/getcorporbankbranchname?bank_name=${bank_name}&province_name=${province_name}&city_name=${city_name}&keywords=${keywords}&page=${page}&limit=${limit}`
	);
