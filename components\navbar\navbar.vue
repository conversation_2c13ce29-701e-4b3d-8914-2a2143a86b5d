<template>
  <view>
    <up-navbar
      :height="props.height"
      :titleStyle="{ color: props.titleColor }"
      :title="props.title"
      :leftIcon="props.leftIcon"
      :leftIconColor="props.titleColor"
      :autoBack="props.isAutoBack"
      :bgColor="backGroundColor"
      :border="false"
			:placeholder="true"
      @rightClick="rightClick"
    ></up-navbar>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps({
  title: {
    type: String,
    default: '团队'
  },
  leftIcon: {
    type: String,
    default: 'arrow-left'
  },
  isAutoBack: {
    type: Boolean,
    default: true
  },
  titleColor: {
    type: String,
    default: '#000'
  },
  backGroundColor: {
    type: String,
    default: '#F0F2F5'
  },
  height: {
    type: Number,
    default: 44
  }
});

// 定义方法
const rightClick = () => {
  console.log('rightClick');
};

const leftClick = () => {
  console.log('leftClick');
};
</script>

<style scoped lang="scss"></style>
