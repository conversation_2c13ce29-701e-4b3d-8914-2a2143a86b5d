<template>
	<view class="">
		<up-popup :show="props.show" mode="center" :round="10" closeable @close="close" :closeOnClickOverlay="false">
		  <view class="popup1">
				<view class="txt1">绑定设备</view>
				<view class="txt2 margin-sty">请输入收款码ID、设备SN</view>
				<view class="txt2">或扫码以完成绑定</view>
				<view class="margin-sty1">
					<up-input v-model="code" placeholder="请输入收款ID或设备SN" border="surround">
						<template #suffix>
							<image @click="scanHandel" src="@/static/images/saoyisao.png" mode="aspectFit" style="width: 44rpx; height: 44rpx"></image>
						</template>
					</up-input>
				</view>
		    <view class="btn1 margin-sty1" :class="code ? '' : 'btn2'" @click="okHandel">确定</view>
		  </view>
		</up-popup>
	</view>
</template>

<script setup>
 /**
  * 绑定设备弹窗 
  */
 import { ref,watch } from 'vue'
 import qrcode from '@/utils/qrcode.js';
 import useUserStore from '@/store/user';
 const emit = defineEmits(['close','okHandel'])
 
 const userStore = useUserStore()
 
 const props = defineProps({
 	show: {
 		type: Boolean,
 		default: false
 	}
 })
 
 const code = ref('')
 
 watch(() => props.show, (val) => {
	if (val) {
		code.value = ''
	}
 })
 
 function close(){
	 emit('close')
 }
 
 function okHandel(){
	 emit('okHandel',code.value)
 }
 
 /** 扫一扫按钮 */
 function scanHandel() {
   /*#ifdef APP-PLUS*/
   uni.scanCode({
     success: async function (res) {
 			 if (res.result.startsWith(`https://dev.server.keyupay.com/`) || res.result.startsWith(`https://server.keyupay.com/`)) {
 				 res.result = res.result + '&m=1';
 				 await request(res.result);
 			 }else{
 				 code.value = res.result
 			 }
     }
   });
   /*#endif*/
 
   /*#ifdef H5*/
   scanCode();
   /*#endif*/
 }
 /** h5的扫码 */
 function scanCode() {
   // 调用uni提供的调用相机api
   uni.chooseImage({
     sizeType: ['original'],
     sourceType: ['camera'],
     count: 1,
     success: function (res) {
       const tempFilePaths = res.tempFilePaths[0]; // 获取到二维码图片的链接
       qrcode.decode(tempFilePaths); // 解析二维码图片
       qrcode.callback = async function (res1) {
         // 解析成功
         if (res1 == 'error decoding QR Code') {
           uni.utils.toast('识别二维码失败，请重新上传');
         }else if (res1.startsWith(`https://dev.server.keyupay.com/`) || res1.startsWith(`https://server.keyupay.com/`)) {
 					// 解析成功返回二维码链接
 					res1 = res1 + '&m=1';
 					await request(res1)
 				} else {
           // 解析成功返回二维码链接
 					code.value = res1;
         }
       };
     },
     fail: () => {
       uni.utils.toast('识别二维码失败，请重新上传');
     }
   });
 }
 
 /** 请求接口 */
 function request(url) {
   uni.request({
     url: url,
     header: {
       token: userStore.token || ''
     },
     success: (res) => {
       if (res.data.code == 200) {
         code.value = res.data.data.code_data;
       } else {
         uni.utils.toast(res.data.msg);
       }
     }
   });
 }
 
</script>

<style lang="scss" scoped>
	::v-deep .u-input {
	  height: 96rpx;
	  box-sizing: border-box;
	  background: #F0F2F5;
	  border-radius: 8rpx;
	  border: none;
	}
	
	::v-deep .uni-input-placeholder{
		font-weight: 400;
		font-size: 26rpx;
		color: #9DA5AD;
	}
	
	::v-deep .u-icon__icon{
		color: #9DA5AD !important;
	}
	
	.margin-sty{
		margin-top: 16rpx;
	}
	
	.margin-sty1{
		margin-top: 32rpx;
	}
	
	.popup1 {
	  width: 582rpx;
	  height: 464rpx;
	  background: #ffffff;
	  box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75,80,85,0.04);
	  border-radius: 24rpx;
	  box-sizing: border-box;
	  padding: 40rpx 32rpx 32rpx 32rpx;
	
	  .txt1 {
	    font-weight: 600;
	    font-size: 34rpx;
	    color: #575E65;
	    line-height: 48rpx;
			text-align: center;
	  }
		
		.txt2{
			font-weight: 400;
			font-size: 24rpx;
			color: #9DA5AD;
			line-height: 40rpx;
			text-align: center;
		}
	
	  .btn1 {
	    width: 100%;
	    height: 88rpx;
	    line-height: 88rpx;
	    background: #1386FB;
	    text-align: center;
	    border-radius: 8rpx;
	    font-weight: 600;
	    font-size: 26rpx;
	    color: #ffffff;
	    font-style: normal;
	  }
		.btn2{
			background: #7ABCFF;
		}
	}
</style>