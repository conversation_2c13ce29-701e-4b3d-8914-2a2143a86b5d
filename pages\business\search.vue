<template>
	<view class="container">
		<!-- 导航栏 -->
		<navbar title="搜索" backGroundColor="#f0f2f5" />

		<!-- 搜索区域 -->
		<view class="search-section">
			<view class="search-box">
				<image src="@/static/images/search-icon.png" class="search-icon" mode="aspectFit"></image>
				<input type="text" class="search-input" v-model="searchText" placeholder="输入商家名称或联系人姓名电话"
					placeholder-class="placeholder" focus @confirm="handleSearch" />
				<image v-if="searchText" src="@/static/images/clear-icon.png" class="clear-icon" @click="clearSearch"
					mode="aspectFit"></image>
			</view>
			<text class="search-btn" @click="handleSearch">搜索</text>
		</view>

		<!-- 搜索历史 -->
		<view class="history-section" v-if="searchHistory.length > 0">
			<view class="history-header">
				<text class="title">搜索历史</text>
				<view class="clear-history" @click="clearHistory">
					<text>清空记录</text>
					<image src="@/static/images/delete-icon.png" class="delete-icon" mode="aspectFit"></image>
				</view>
			</view>

			<!-- 历史记录标签 -->
			<view class="history-tags">
				<view class="tag" v-for="(item, index) in searchHistory" :key="index" @click="useHistory(item)">
					<text>{{ item }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import navbar from '@/components/navbar/navbar.vue'

const searchText = ref('')
const searchHistory = ref([])

// 获取本地存储的搜索历史
onMounted(() => {
	const history = uni.getStorageSync('searchHistory')
	if (history) {
		searchHistory.value = JSON.parse(history)
	}
})

// 清空搜索框
function clearSearch () {
	searchText.value = ''
}

// 执行搜索
function handleSearch () {
	if (!searchText.value.trim()) return
	// 添加到搜索历史
	if (!searchHistory.value.includes(searchText.value)) {
		searchHistory.value.unshift(searchText.value)
		// 只保留最近10条记录
		if (searchHistory.value.length > 10) {
			searchHistory.value.pop()
		}
		// 保存到本地存储
		uni.setStorageSync('searchHistory', JSON.stringify(searchHistory.value))
	}
	// 跳转到搜索结果页
	uni.navigateTo({
		url: `/pages/business/searchResult?keywords=${encodeURIComponent(searchText.value)}`
	})
}

// 清空历史记录
function clearHistory () {
	searchHistory.value = []
	uni.removeStorageSync('searchHistory')
}

// 使用历史记录
function useHistory (text) {
	searchText.value = text
	handleSearch()
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: #F0F2F5;
	padding: 32rpx;
	box-sizing: border-box;
}

.search-section {
	display: flex;
	align-items: center;

	.search-box {
		flex: 1;
		display: flex;
		align-items: center;
		background: #FFFFFF;
		border-radius: 16rpx;
		padding: 16rpx 24rpx;
		margin-right: 24rpx;

		.search-icon {
			width: 40rpx;
			height: 40rpx;
			margin-right: 16rpx;
		}

		.search-input {
			flex: 1;
			font-size: 28rpx;
			color: #18191A;
			font-weight: 600;
		}

		.clear-icon {
			width: 40rpx;
			height: 40rpx;
		}

		.placeholder {
			color: #979B9F;
			font-weight: 400;
		}
	}

	.search-btn {
		font-size: 28rpx;
		color: #575E65;
		line-height: 40rpx;
		font-weight: 600;
	}
}

.history-section {
	margin-top: 48rpx;

	.history-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;

		.title {
			font-size: 30rpx;
			color: #18191A;
			font-weight: 600;
			line-height: 44rpx;
			letter-spacing: 0.5px;
		}

		.clear-history {
			display: flex;
			align-items: center;
			color: #979B9F;
			font-size: 26rpx;

			.delete-icon {
				width: 40rpx;
				height: 40rpx;
				margin-left: 8rpx;
			}
		}
	}

	.history-tags {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;

		.tag {
			background: #FFFFFF;
			border-radius: 8rpx;
			padding: 12rpx 24rpx;
			font-size: 26rpx;
			color: #575E65;
			line-height: 40rpx;
		}
	}
}
</style>