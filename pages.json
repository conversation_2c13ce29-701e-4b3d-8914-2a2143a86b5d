{
  "easycom": {
    // 注意一定要放在custom里，否则无效，https://ask.dcloud.net.cn/question/131175
    "custom": {
      "^u--(.*)": "@/uni_modules/uview-plus/components/u-$1/u-$1.vue",
      "^up-(.*)": "@/uni_modules/uview-plus/components/u-$1/u-$1.vue",
      "^u-([^-].*)": "@/uni_modules/uview-plus/components/u-$1/u-$1.vue"
    }
  },
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "工作台",
        "enablePullDownRefresh": false,
        "onReachBottomDistance": 50
      }
    },
    {
      "path": "pages/team/team",
      "style": {
        "navigationBarTitleText": "团队",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/my/my",
      "style": {
        "navigationBarTitleText": "我的",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/login/index",
      "style": {
        "navigationBarTitleText": "登录",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/login/verifyPhone",
      "style": {
        "navigationBarTitleText": "验证手机号",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/login/setNewPassword",
      "style": {
        "navigationBarTitleText": "设置新密码",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/identityCard/identityCard",
      "style": {
        "navigationBarTitleText": "营业执照",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/solutionSelection/solutionSelection",
      "style": {
        "navigationBarTitleText": "方案选择",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/corporateIdentityCard/corporateIdentityCard",
      "style": {
        "navigationBarTitleText": "法人身份证",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/my/editingInfo",
      "style": {
        "navigationBarTitleText": "编辑资料",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/my/account",
      "style": {
        "navigationBarTitleText": "账号与安全",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/merchantRecord/merchantRecord",
      "style": {
        "navigationBarTitleText": "新增商户",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/my/changePassword",
      "style": {
        "navigationBarTitleText": "设置密码",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/my/changeName",
      "style": {
        "navigationBarTitleText": "修改名字",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/team/teamList",
      "style": {
        "navigationBarTitleText": "团队列表",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/index/setCard",
      "style": {
        "navigationBarTitleText": "商户",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/shopScene/shopScene",
      "style": {
        "navigationBarTitleText": "店铺场景",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/debitCard/debitCard",
      "style": {
        "navigationBarTitleText": "结算卡",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/my/about",
      "style": {
        "navigationBarTitleText": "关于",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/my/privacyPolicy",
      "style": {
        "navigationBarTitleText": "隐私政策",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/my/userAgreement",
      "style": {
        "navigationBarTitleText": "用户协议",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/accomplish/accomplish",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/replaceDebitCard/replaceDebitCard",
      "style": {
        "navigationBarTitleText": "更换结算卡",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/business/business",
      "style": {
        "navigationBarTitleText": "业务",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/business/merchant",
      "style": {
        "navigationBarTitleText": "商家列表",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/business/merchantDetail",
      "style": {
        "navigationBarTitleText": "商家详情",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/business/search",
      "style": {
        "navigationBarTitleText": "搜索",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/business/transaction",
      "style": {
        "navigationBarTitleText": "交易记录",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/business/monthlyNew",
      "style": {
        "navigationBarTitleText": "当月新增",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/business/selectStore",
      "style": {
        "navigationBarTitleText": "选择门店",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/business/searchResult",
      "style": {
        "navigationBarTitleText": "搜索结果",
        "enablePullDownRefresh": false
      }
    }
  ],
  "tabBar": {
    "color": "#18191A",
    "selectedColor": "#1386FB",
    "borderStyle": "white",
    "backgroundColor": "#ffffff",
    "list": [
      {
        "pagePath": "pages/index/index",
        "iconPath": "static/images/index-icon1.png",
        "selectedIconPath": "static/images/index-icon.png",
        "text": "工作台"
      },
      {
        "pagePath": "pages/business/business",
        "iconPath": "static/images/business-icon1.png",
        "selectedIconPath": "static/images/business-icon.png",
        "text": "业务"
      },
      {
        "pagePath": "pages/team/team",
        "iconPath": "static/images/team-icon1.png",
        "selectedIconPath": "static/images/team-icon.png",
        "text": "团队"
      },
      {
        "pagePath": "pages/my/my",
        "iconPath": "static/images/my-icon1.png",
        "selectedIconPath": "static/images/my-icon.png",
        "text": "我的"
      }
    ]
  },
  "globalStyle": {
    "navigationStyle": "custom",
    "backgroundColor": "#F8F8F8",
    "app-plus": {
      "background": "#efeff4"
    },
    "rpxCalcMaxDeviceWidth": 960, // rpx 计算所支持的最大设备宽度，单位 px，默认值为 960
    "rpxCalcBaseDeviceWidth": 375, // rpx 计算使用的基准设备宽度，设备实际宽度超出 rpx 计算所支持的最大设备宽度时将按基准宽度计算，单位 px，默认值为 375
    "rpxCalcIncludeWidth": 750, // rpx 计算特殊处理的值，始终按实际的设备宽度计算，单位 rpx，默认值为 750
    "style": {
      "softinputMode": "adjustResize" // 或 "adjustPan"
    }
  },
  "condition": { //模式配置，仅开发期间生效
    "current": 0, //当前激活的模式(list 的索引项)
    "list": [
      {
        "name": "", //模式名称
        "path": "", //启动页面，必选
        "query": "" //启动参数，在页面的onLoad函数里面得到
      }
    ]
  }
}