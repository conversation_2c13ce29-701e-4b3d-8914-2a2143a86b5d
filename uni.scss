/* 需要放到文件最上面 */
@import '@/uni_modules/uni-scss/variables.scss';
@import '@/uni_modules/uview-plus/theme.scss';

/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
// 主色
$uni-primary: #1386fb;
$uni-primary-disable: mix(#fff, $uni-primary, 50%);
$uni-primary-light: mix(#fff, $uni-primary, 80%);

// 辅助色
// 除了主色外的场景色，需要在不同的场景中使用（例如危险色表示危险的操作）。
$uni-success: #36C60E;
$uni-success-disable: mix(#fff, $uni-success, 50%);
$uni-success-light: mix(#fff, $uni-success, 80%);

$uni-warning: #F39237;
$uni-warning-disable: mix(#fff, $uni-warning, 50%);
$uni-warning-light: mix(#fff, $uni-warning, 80%);

$uni-error: #F73429;
$uni-error-disable: mix(#fff, $uni-error, 50%);
$uni-error-light: mix(#fff, $uni-error, 80%);

$uni-info: #8f939c;
$uni-info-disable: mix(#fff, $uni-info, 50%);
$uni-info-light: mix(#fff, $uni-info, 80%);

$u-primary: #1386fb;
$u-warning: $uni-warning;
$u-success: $uni-success;
$u-error: $uni-error;
$u-info: $uni-info;

// 中性色
$uni-main-color: #18191A; // 标题文字、正文文字
$uni-base-color: #575E65; // 正文文字
$uni-secondary-color: #9DA5AD; // 辅助文字

// 边框颜色
$uni-border-1: #CDD1D6;
// 不可点击、分割线
$uni-border-2: #DEE2E8;
// 可点击、表头背景
$uni-border-3: #EDF0F5;

// 常规色
$uni-black: #1c1b12;
$uni-white: #ffffff;
$uni-transparent: rgba(
  $color: #000000,
  $alpha: 0
);

// 背景色
$uni-bg-color: #F6F8FC;
$uni-bg-color-menu: #1c1812; // 菜单背景色
$uni-bg-color-page: #332e2e; // 页面背景色
$uni-bg-color-module: #3c3c3c; // 模块背景色
$uni-bg-color-area: #787878; // 区域背景色
$uni-bg-color-area-1: #565656; // 区域背景色

/* 水平间距 */
$uni-spacing-sm: 3rpx;
$uni-spacing-base: 6rpx;
$uni-spacing-lg: 12rpx;

/*字体大小*/
$uni-font-size-min: 4.69rpx;  // 12px
$uni-font-size-mm: 5.46rpx; // 14px
$uni-font-size-sm: 6.25rpx;  // 16px
$uni-font-size-base: 7rpx;  // 18rpx

// 阴影
$uni-shadow-sm: 0 0 5px
  rgba(
    $color: #d8d8d8,
    $alpha: 0.5
  );
$uni-shadow-base: 0 1px 8px 1px
  rgba(
    $color: #a5a5a5,
    $alpha: 0.2
  );
$uni-shadow-lg: 0px 1px 10px 2px
  rgba(
    $color: #a5a4a4,
    $alpha: 0.5
  );

// 蒙版
$uni-mask: rgba(
  $color: #000000,
  $alpha: 0.4
);
