
/** 团队 首部统计*/
export const teamIndexApi = () => uni.$http.get('tenant_salesman.team/index');

/** 团队列表 首部统计*/
export const teamListIndexApi = (team_id:any) => uni.$http.get(`tenant_salesman.team/index?team_id=${team_id}`);

/** 团队业绩排名 */
export const teamPerApi = (type:any) => uni.$http.get(`tenant_salesman.statistics/teamPerformanceRanking?type=${type}`);

/** 团队列表 */
export const teamListApi = () => uni.$http.get('tenant_salesman.team/list');

/** 团队成员列表 */
export const teamMemberListApi = (team_id:any) => uni.$http.get(`tenant_salesman.team/list?team_id=${team_id}`);

