<template>
  <view class="title" :class="[`size-${props.size}`]">
    <view class="title-text">
      <span class="title-icon"></span>
      <span>{{ title }}</span>
    </view>

    <slot></slot>
    <uni-icons v-if="closeIcon" @click="emit('close')" type="undo-filled" size="12rpx" color="#fff"></uni-icons>
  </view>
</template>

<script lang="ts" setup>
	/**
	 * 标题
	 * @description 模块的标题、弹框的标题
	 * @property {Number}			size		标题字体大小。1：普通标题；2：弹框标题。默认：1
	 * @property {String}			title		标题名字
	 * @property {Boolean}		closeIcon		是否显示右侧的关闭按钮
	 * @event {Function} close 点击关闭按钮触发
	 * @example <Title title="标题" size="2" ></Title>
	 */
	// 默认插槽，显示到右侧。
const props = defineProps({
  /**标题字体大小。1：普通标题；2：弹框标题*/
  size: {
    type: Number,
    default: 1
  },
  title: {
    type: String,
    default: ''
  },
  closeIcon: {
    type: Boolean,
    default: false
  }
});
const emit = defineEmits(['close']);
</script>

<style lang="scss" scoped>
.title {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  &.size-1 {
    font-size: $uni-font-size-base;
    .title-text {
      font-weight: bold;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .title-icon {
        width: 3.5rpx;
        height: 8.6rpx;
        background-color: $uni-primary;
        border-radius: 4.6rpx;
        margin: 2rpx;
      }
    }
  }
  &.size-2 {
    font-size: $uni-font-size-base * 1.2;
    .title-text {
      font-weight: bold;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .title-icon {
        width: 3.5rpx;
        height: 10rpx;
        background-color: $uni-primary;
        border-radius: 4.6rpx;
        margin: 2rpx;
      }
    }
  }
}
</style>
