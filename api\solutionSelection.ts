/** 获取通道 */
export const mapChannels = (id: any) => uni.$http.get('tenant_salesman.Incoming/getPlatformGangway?id=' + id);

/** 获取费率模板 */
export const getRateTemplate = (bank_name: any) => uni.$http.get(`tenant_salesman.Incoming/getAgentByRateTemplate?bank_name=${bank_name}`);

/** 更新商户通道信息 */
export const updateMerchantInfo = (data: any) => uni.$http.post('tenant_salesman.Incoming/perfectMerchantIncoming', data);

/** 优惠活动 */
export const getActivity = (data: any) => uni.$http.get(`tenant_salesman.Incoming/getMerchantActivity?current_gangway=` + data);
