<template>
	<view class="page-index-container">
		<!-- 导航栏 -->
		<navbar title="" leftIcon=" " :isAutoBack="false" :height="0" />
		<view class="page-index-top">
			<view class="page-index-t flex-sty">
				<view class="img-box">
					<image v-if="userStore.userInfo.salesman_avatar" class="img1" :src="baseURL + userStore.userInfo.salesman_avatar" mode=""></image>
					<image v-else class="img1" :src="noImg" mode=""></image>
				</view>
				<view class="page-index-t-r">
					<view class="txt1" @click="jumpTo">新增商户</view>
					<view class="code" @click="scanHandel">
						<uni-icons type="scan" size="22" color="#fff"></uni-icons>
						<text class="code-txt">扫一扫</text>
					</view>
				</view>
			</view>
			<view class="page-index-m" v-if="userStore.userInfo.greetings">
				<view class="page-index-m-t flex-sty2">
					<view class="txt2">{{ userStore.userInfo.greetings.greeting }}, {{ userStore.userInfo.name }}</view>
					<image v-if="userStore.userInfo.salesman_position == '业务经理'" style="width: 104rpx; height: 40rpx" src="@/static/images/ywjl.png" mode=""></image>
					<image v-if="userStore.userInfo.salesman_position == '业务主管'" style="width: 104rpx; height: 40rpx" src="@/static/images/ywzg.png" mode=""></image>
					<image v-if="userStore.userInfo.salesman_position == '业务专员'" style="width: 104rpx; height: 40rpx" src="@/static/images/ywzy.png" mode=""></image>
					<image v-if="userStore.userInfo.salesman_position == '业务总监'" style="width: 104rpx; height: 40rpx" src="@/static/images/ywzj.png" mode=""></image>
				</view>
				<view class="page-index-m-b">{{ userStore.userInfo.greetings.randomPhrase }}</view>
			</view>
			<view class="page-index-b flex-sty">
				<view class="module1">
					<view class="">
						<view class="module1-l flex-sty1">
							<view class="txt3">今日开户数</view>
							<view class="txt4">{{ staticList.salesman_today_count_merchant }}</view>
						</view>
						<view class="module1-r flex-sty1">
							<view class="txt3">今日团队开户</view>
							<view class="txt4">{{ staticList.team_today_count_merchant }}</view>
						</view>
					</view>
					<view class="">
						<view class="module1-l flex-sty1">
							<view class="txt3">本月开户数</view>
							<view class="txt4">{{ staticList.salesman_monthly_count_merchant }}</view>
						</view>
						<view class="module1-r flex-sty1">
							<view class="txt3">本月团队开户</view>
							<view class="txt4">{{ staticList.team_monthly_count_merchant }}</view>
						</view>
					</view>
				</view>
				<view class="module2 flex-sty1">
					<view class="txt3">本月完成度</view>
					<view>
						<view class="txt4">
							{{ staticList.salesman_monthly_count_merchant }}
							<span class="txt5">/{{ staticList.salesman_monthly_tasks }}</span>
						</view>
						<up-line-progress height="6" :percentage="staticList.salesman_proportion" activeColor="#F39237 " :showText="false"></up-line-progress>
					</view>
				</view>
			</view>
		</view>

		<view class="page-index-bot">
			<view class="flex-sty">
				<LeftIndex1 v-if="userStore.userInfo.level == 4" :staticList="staticList" />
				<LeftIndex v-else :staticList="staticList" />
				<RightIndex @getType="getType" :teamList="teamList" />
			</view>
			<view class="tit1">待办事项</view>
			<view class="" style="padding-bottom: 100rpx" v-if="toDoItemList.length > 0">
				<ItemIndex v-for="item in toDoItemList" :item="item" @goComplete="goComplete" @viewHandel="viewHandel" />
				<u-loadmore :status="status" color="#AAAAAA" fontSize="10" />
			</view>
			<view v-else class="none-txt">暂无数据</view>
		</view>

		<PopperReject :show="isShow" :merchantInfo="merchantInfo" @close="close" @editDataHandel="editDataHandel" />
		<PopperSigning :show="isShowSign" :signCode="signCode" @close="closeSign" />
		<!-- 更新弹窗 -->
		<PopperVersionUpdate v-model="isPopperUpdateVisible" @closeUpdateHandel="closeUpdateHandel" />
	</view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onBeforeMount, onMounted, inject, onUnmounted } from 'vue';
import qrcode from '@/utils/qrcode.js';
import { onReachBottom, onShow } from '@dcloudio/uni-app';
import navbar from '@/components/navbar/navbar.vue';
import PopperVersionUpdate from '@/components/PopperVersionUpdate.vue'
import LeftIndex from '@/pages/index/components/LeftIndex.vue';
import LeftIndex1 from '@/pages/index/components/LeftIndex1.vue';
import RightIndex from '@/pages/index/components/RightIndex.vue';
import ItemIndex from '@/pages/index/components/ItemIndex.vue';
import PopperReject from '@/pages/index/components/PopperReject.vue';
import PopperSigning from '@/pages/index/components/PopperSigning.vue';
import useUserStore from '@/store/user';
import useVersionStore from '@/store/version';
import { statisticsApi, teamPerApi, toDoItemsApi, signApi } from '@/api/index';
import noImg from '@/static/images/noAvatar.png';

const baseURL = inject('baseURL');
const userStore = useUserStore();
const versionStore = useVersionStore();
const isPopperUpdateVisible = ref(false) //显示更新弹窗

const statusBarHeight = ref(0);
/** 获取手机顶部状态栏高度 */
function otgetSystemInfo() {
	uni.getSystemInfo({
		success: function (res) {
			statusBarHeight.value = res.statusBarHeight;
		}
	});
}

/** 判断是否显示团队导航*/
function setTabBar() {
	/** 如果是业务专员 则不显示团队 */
	if (userStore.userInfo.level == 4) {
		uni.setTabBarItem({
			index: 2,
			visible: false
		});
	} else {
		uni.setTabBarItem({
			index: 2,
			visible: true
		});
	}
}

/** 显示商户签约 */
let isShowSign = ref(false);
/** 签约的二维码 */
let signCode = ref(null);
/** 去完成 */
function goComplete(target: any) {
	// 保存 save_page 到本地
	uni.setStorageSync('save_page', target.save_page);
	// to_do_items_type待办事项：1 完善资料 2 提交结算卡 3 绑定付款码  4 审核驳回  5 商户签约  6 确认资料',
	// 判断跳转哪个页面  save_page  0 1 2 3 4
	uni.setStorageSync('merchant_id', target.id);
	uni.setStorageSync('code_data', null);

	/** 绑定结算卡 绑定设备  直接跳转结算卡页面*/
	if (target.to_do_items_type == 2 || target.to_do_items_type == 3) {
		uni.navigateTo({
			url: '/pages/debitCard/debitCard'
		});
	}
	/** 商户信息页面 */
	if (target.to_do_items_type == 1 && target.save_page == 0) {
		uni.navigateTo({
			url: '/pages/merchantRecord/merchantRecord'
		});
	}

	/** 方案选择 */
	if (target.to_do_items_type == 1 && target.save_page == 1) {
		uni.navigateTo({
			url: '/pages/solutionSelection/solutionSelection'
		});
	}

	/** 营业执照 */
	if (target.to_do_items_type == 1 && target.save_page == 2) {
		uni.navigateTo({
			url: '/pages/identityCard/identityCard'
		});
	}

	/** 法人身份证 */
	if (target.to_do_items_type == 1 && target.save_page == 3) {
		uni.navigateTo({
			url: '/pages/corporateIdentityCard/corporateIdentityCard'
		});
	}

	/** 店铺场景 */
	if (target.to_do_items_type == 1 && target.save_page == 4) {
		uni.navigateTo({
			url: '/pages/shopScene/shopScene'
		});
	}

	/** 结算卡 */
	if (target.to_do_items_type == 1 && target.save_page == 5) {
		uni.navigateTo({
			url: '/pages/debitCard/debitCard'
		});
	}

	/** 商户签约 */
	if (target.to_do_items_type == 5) {
		otsignApi(target.id);
	}
}

/** 商户签约 */
async function otsignApi(val: any) {
	const data = {};
	data.id = val;
	const res = await signApi(data);
	if (res.msg == 'ok') {
		signCode.value = res.data.png;
		isShowSign.value = true;
	} else {
		uni.utils.toast(res.msg);
	}
}

/** 显示驳回信息弹窗 */
let isShow = ref(false);
/** 驳回内容信息 */
let merchantInfo = ref({});
/** 查看 */
function viewHandel(target: any) {
	merchantInfo.value = target;
	uni.setStorageSync('merchant_id', target.id);
	uni.setStorageSync('code_data', null);
	isShow.value = true;
}
/** 修改资料  驳回类型判断跳转哪个页面*/
/** 营业执照信息有误、法人信息有误、店铺场景照有误、结算卡信息有误、不予进件 */
function editDataHandel(target: any) {
	isShow.value = false;
	/** 营业执照信息有误 */
	if (target.rejection_type == 1) {
		uni.navigateTo({
			url: '/pages/identityCard/identityCard'
		});
	}
	/** 法人信息有误 */
	if (target.rejection_type == 2) {
		uni.navigateTo({
			url: '/pages/corporateIdentityCard/corporateIdentityCard'
		});
	}
	/** 店铺场景照有误 */
	if (target.rejection_type == 3) {
		uni.navigateTo({
			url: '/pages/shopScene/shopScene'
		});
	}

	/** 结算卡信息有误 */
	if (target.rejection_type == 4) {
		uni.navigateTo({
			url: '/pages/debitCard/debitCard'
		});
	}

	/** 不予进件  点击确认后不再在代办事项里显示 需要重新请求接口*/
	if (target.rejection_type == 5) {
	}
}

/** 关闭驳回信息弹窗 */
function close() {
	isShow.value = false;
}

/** 关闭商户签约弹窗 */
function closeSign() {
	isShowSign.value = false;
	pageNum.value = 1;
	otstatisticsApi();
	ottoDoItemsApi();
}

/** 跳转商户进件 */
function jumpTo() {
	// 清空本地存储的 save_page
	uni.setStorageSync('save_page', null);
	/** 把本地的merchant_id清除 */
	uni.setStorageSync('merchant_id', null);
	uni.setStorageSync('code_data', null);
	uni.navigateTo({
		url: '/pages/merchantRecord/merchantRecord'
	});
}

/** 获取扫描的二维码 */
let code = ref('123');
/** 扫码进件 */
function scanHandel() {
	/*#ifdef APP-PLUS*/
	uni.scanCode({
		success: async function (res) {
			code.value = res.result;
			await request(code.value);
		}
	});
	/*#endif*/

	/*#ifdef H5*/
	scanCode();
	/*#endif*/
}
/** h5的扫码 */
function scanCode() {
	// 调用uni提供的调用相机api
	uni.chooseImage({
		sizeType: ['original'],
		sourceType: ['camera'],
		count: 1,
		success: function (res) {
			const tempFilePaths = res.tempFilePaths[0]; // 获取到二维码图片的链接
			qrcode.decode(tempFilePaths); // 解析二维码图片
			qrcode.callback = async function (res1) {
				code.value = res1;
				// 解析成功
				//https://dev.server.keyupay.com/index/pay?d=
				if (code.value.startsWith(`https://dev.server.keyupay.com/`) || code.value.startsWith(`https://server.keyupay.com/`)) {
					// 解析成功返回二维码链接
					code.value = res1;
					await request(code.value);
				} else {
					// 解析失败
					uni.utils.toast('识别二维码失败，请重新上传');
				}
			};
		},
		fail: () => {
			uni.utils.toast('识别二维码失败，请重新上传');
		}
	});
}

/** 请求接口 */
function request(url: any) {
	uni.request({
		url: url,
		header: {
			token: userStore.token || ''
		},
		success: (res) => {
			if (res.data.code == 200) {
				/** 判断是 跳转商户进件页面 0 跳转更换结算卡 1*/
				if (res.data.data.status == 1) {
					uni.setStorageSync('merchant_id', res.data.data.merchant_id);
					uni.setStorageSync('code_data', null);
					uni.navigateTo({
						url: '/pages/index/setCard'
					});
				} else {
					/** 把本地的merchant_id清除 */
					uni.setStorageSync('merchant_id', null);
					/** 存储一下code码 */
					uni.setStorageSync('code_data', res.data.data.code_data);
					uni.navigateTo({
						url: '/pages/merchantRecord/merchantRecord'
					});
				}
			} else if (res.data.code == 403) {
				uni.reLaunch({
					url: '/pages/login/index'
				});
			} else {
				uni.utils.toast(res.data.msg);
			}
		},
		fail: (err) => {
			uni.utils.toast(err);
		}
	});
}

/** 首页数据 */
let staticList = ref({});
/** 开户统计 */
async function otstatisticsApi() {
	const res = await statisticsApi();
	if (res.code == 200) {
		staticList.value = res.data;
	} else {
		uni.utils.toast(res.msg);
	}
}

/** 团队排名列表 */
let teamList = ref([]);
/** 团队业绩排名 */
function getType(target: any) {
	otteamPerApi(target);
}
/** 团队业绩接口 */
async function otteamPerApi(target: any) {
	const res = await teamPerApi(target);
	if (res.code == 200) {
		teamList.value = res.data;
	} else {
		uni.utils.toast(res.msg);
	}
}

let pageNum = ref(1);
let pageSize = ref(20);
let total = ref(-1);
const status = ref('loadmore');

/** 代办事项 */
let toDoItemList = ref([]);
async function ottoDoItemsApi() {
	status.value = 'loading';
	const res = await toDoItemsApi(pageNum.value, pageSize.value);
	if (pageNum.value == 1) {
		toDoItemList.value = [];
	}
	if (res.code == 200) {
		toDoItemList.value = [...toDoItemList.value, ...res.rows];
		total.value = res.total;
		if (toDoItemList.value.length >= total.value) {
			status.value = 'nomore';
		} else {
			status.value = 'loadmore';
		}
	} else {
		uni.utils.toast(res.msg);
	}
}

	/** 获取当前日期 */
	const getCurrentDate = () => {
		const date = new Date();
		const year = date.getFullYear();
		const month = ('0' + (date.getMonth() + 1)).slice(-2);
		const day = ('0' + date.getDate()).slice(-2);
		return `${year}-${month}-${day}`;
	};

//关闭更新按钮
	function closeUpdateHandel() {
		//判断如果是强制更新退出应用
		if (versionStore.is_update == 1) {
			plus.runtime.quit()
		} else {
			isPopperUpdateVisible.value = false
		}
	}
	
	//APP检查版本更新
	function versionUpdate() {
		versionStore.otgetSystemInfo();
		if (versionStore.osName == 'android') {
			versionStore.platformAndroid().then(() => {
				
				//判断有没有新版本 弹出更新弹窗
				if ( compareVersions(versionStore.new_version, versionStore.version) === 1 ) {
					if (versionStore.is_update == 0) {
						if (uni.getStorageSync('isVersionUpdate') < getCurrentDate()) {
							isPopperUpdateVisible.value = true;
							uni.setStorageSync('isVersionUpdate', getCurrentDate());
						}
					} else {
						isPopperUpdateVisible.value = true;
					}
				}
			});
		}
	}
	
	function compareVersions(v1, v2) {
	  // 将版本号拆分为数字数组
	  const parts1 = v1.split('.').map(Number);
	  const parts2 = v2.split('.').map(Number);
	  // 获取最大长度
	  const maxLength = Math.max(parts1.length, parts2.length);
	  // 逐段比较
	  for (let i = 0; i < maxLength; i++) {
	    const num1 = parts1[i] || 0; // 如果某一段不存在，默认为 0
	    const num2 = parts2[i] || 0;
	    if (num1 > num2) return 1;  // v1 > v2
	    if (num1 < num2) return -1; // v1 < v2
	  }
	  return 0; // v1 == v2
	}

onReachBottom(() => {
	pageNum.value++;
	ottoDoItemsApi();
});

onMounted(() => {
	pageNum.value = 1;
	userStore.otgetUserInfoApi();
	setTabBar();
	otgetSystemInfo();
	// otstatisticsApi()
	// ottoDoItemsApi()
	
	// #ifdef APP-PLUS
		// setTimeout(() => {
		// 	versionUpdate()
		// }, 1000)
	// #endif
});

onShow(() => {
	pageNum.value = 1;
	otstatisticsApi();
	ottoDoItemsApi();
});
</script>

<style lang="scss" scoped>
.flex-sty {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.flex-sty1 {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.flex-sty2 {
	display: flex;
	align-items: center;
}

.page-index-container {
	width: 100%;
	height: 100%;
	box-sizing: border-box;

	.page-index-top {
		width: 100%;
		height: 656rpx;
		box-sizing: border-box;
		padding: 0 32rpx;
		background-image: url('../../static/images/bg2.png');
		background-repeat: no-repeat;
		background-position: center center;

		.page-index-t {
			height: 151rpx;
		}

		.page-index-t-r {
			display: flex;
			align-items: center;

			.txt1 {
				width: 152rpx;
				height: 64rpx;
				font-weight: 400;
				font-size: 26rpx;
				text-align: center;
				color: #ffffff;
				line-height: 64rpx;
				font-style: normal;
				background: linear-gradient(180deg, #1398fb 0%, #1386fb 100%);
				border-radius: 32rpx;
			}

			.code {
				display: flex;
				align-items: center;
				justify-content: center;
				margin-left: 24rpx;
				width: 204rpx;
				height: 64rpx;
				background: linear-gradient(180deg, #1398fb 0%, #1386fb 100%);
				border-radius: 32rpx;

				.code-txt {
					margin-left: 8rpx;
					height: 40rpx;
					font-weight: 400;
					font-size: 26rpx;
					color: #ffffff;
					line-height: 40rpx;
					text-align: center;
					font-style: normal;
				}
			}
		}

		.img-box {
			width: 95rpx;
			height: 95rpx;
			background: #fff;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;

			.img1 {
				width: 87rpx;
				height: 87rpx;
				border-radius: 50%;
			}
		}

		.page-index-m {
			.page-index-m-t {
				.txt2 {
					max-width: 50%;
					margin-right: 16rpx;
					height: 56rpx;
					font-weight: 600;
					font-size: 40rpx;
					color: #ffffff;
					line-height: 56rpx;
					font-style: normal;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}

			.page-index-m-b {
				margin-top: 12rpx;
				width: 100%;
				height: 40rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: #ffffff;
				line-height: 40rpx;
				font-style: normal;
				opacity: 0.85;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
		}

		.page-index-b {
			margin-top: 32rpx;
			height: 288rpx;

			.txt3 {
				width: 100%;
				height: 40rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: #E5F2FF;
				line-height: 40rpx;
				font-style: normal;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			.txt4 {
				width: 100%;
				height: 48rpx;
				font-weight: 600;
				font-size: 32rpx;
				color: #FFFFFF;
				line-height: 48rpx;
				font-style: normal;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			.txt5 {
				height: 40rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: #ffffff;
				line-height: 40rpx;
				font-style: normal;
			}

			.module1 {
				display: flex;
				height: 100%;
				width: 452rpx;
				background: #3296FC;
				border-radius: 24rpx;
				box-sizing: border-box;
				padding: 24rpx;

				.module1-l {
					width: 200rpx;
					height: 96rpx;
				}

				.module1-r {
					margin-top: 48rpx;
					width: 200rpx;
					height: 96rpx;
				}
			}

			.module2 {
				width: 218rpx;
				height: 100%;
				background: #3296fc;
				border-radius: 24rpx;
				box-sizing: border-box;
				padding: 24rpx;
			}
		}
	}

	.page-index-bot {
		position: absolute;
		top: 710rpx;
		/* #ifdef H5 */
		top: 610rpx;
		/* #endif */
		left: 0;
		right: 0;
		width: 100%;
		height: 200rpx;
		background: #F0F2F5;
		border-radius: 24rpx 24rpx 0rpx 0rpx;
		box-sizing: border-box;
		padding: 40rpx 32rpx;

		.tit1 {
			height: 48rpx;
			font-weight: 600;
			font-size: 34rpx;
			color: #18191a;
			line-height: 48rpx;
			font-style: normal;
			margin: 56rpx 0 32rpx 0;
		}

		.none-txt {
			width: 100%;
			text-align: center;
			padding-bottom: 120rpx;
			height: 40rpx;
			font-weight: 400;
			font-size: 24rpx;
			color: $uni-secondary-color;
			line-height: 40rpx;
			font-style: normal;
		}
	}
}
</style>
