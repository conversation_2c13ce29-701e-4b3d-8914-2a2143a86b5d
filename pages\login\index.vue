<template>
	<view class="page-login-container">
		<!-- 导航栏 -->
		<navbar title="" leftIcon=" " :isAutoBack="false" :height="0" />
		<view class="page-login-top">
			<view class="txt1">登录</view>
			<view class="page-login-logo">
				<image style="width: 136rpx;height: 136rpx;" src="@/static/images/logo.png" mode=""></image>
			</view>
			<view class="txt2">您好！欢迎使用客予助手</view>
		</view>
		<view class="page-login-bot">
			<up-form labelPosition="top" :model="baseFormData" :rules="rules" ref="baseForm" errorType="toast">
				<up-form-item label="账号" prop="account">
					<up-input v-model="baseFormData.account" placeholder="请输入您的账号" border="none"></up-input>
				</up-form-item>
				<up-form-item label="密码" prop="password">
					<up-input v-model="baseFormData.password" placeholder="请输入账号密码" border="none"
						:password="showPassword"></up-input>
					<template #right>
						<uni-icons @click="changePassword" color="#9DA5AD;" :type="showPassword ? 'eye-slash' : 'eye'"
							size="22"></uni-icons>
					</template>
				</up-form-item>
				<view class="flex-sty">
					<up-checkbox-group>
						<up-checkbox :customStyle="{marginBottom: '8px'}" size="14" labelColor="#9DA5AD" labelSize="12" label="记住账号密码" name="agree" :checked="rememberPwd" @change="changeHandel"></up-checkbox>
					</up-checkbox-group>
					<view class="txt5" @click="jumpTo(`/pages/login/verifyPhone?mobile=${baseFormData.account}`)">忘记密码？</view>
				</view>
				<view class="txt3">登录即代表您已阅读并同意
					<span class="txt4" @click="jumpTo('/pages/my/userAgreement')">《用户协议》</span>和
					<span class="txt4" @click="jumpTo('/pages/my/privacyPolicy')">《隐私政策》</span>
				</view>
				<view class="">
					<view v-if="showLoginBtn" class="btn1 btn2" @click="formSubmit">登录</view>
					<view v-else class="btn1">登录</view>
				</view>
			</up-form>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, reactive, computed,onMounted } from 'vue';
	import useUserStore from '@/store/user';

	const userState = useUserStore();
	const baseForm = ref();
	const showPassword = ref(true)
	const baseFormData = ref({
		account: uni.getStorageSync('account') || '',
		password: uni.getStorageSync('password') || '',
		// account: '***********',
		// password: '123456',
	});
	
	/** 是否记住账号密码 */
	let rememberPwd = uni.getStorageSync('rememberPwd') || false

	function changeHandel(val){
		rememberPwd = val
	}

	const rules = reactive({
		account: [{ required: true, message: '请输入账号', trigger: 'blur' }],
		password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
	})

	let showLoginBtn = computed(() => {
		if (baseFormData.value.account && baseFormData.value.password) {
			return true
		} else {
			return false
		}
	})


	function changePassword() {
		showPassword.value = !showPassword.value
	}

	// 登录回调
	const formSubmit = () => {
		/** 判断有没有记住密码 */
		uni.setStorageSync('rememberPwd', rememberPwd)
		if(rememberPwd){
			uni.setStorageSync('account', baseFormData.value.account)
			uni.setStorageSync('password', baseFormData.value.password)
		}else{
			uni.setStorageSync('account', '')
			uni.setStorageSync('password', '')
		}
		baseForm.value.validate().then((valid : any) => {
			if (valid) {
				// 从pinia调用login函数传递账号密码
				userState.login(baseFormData.value);
			} else {

			}
		})
	}

	/** 页面跳转 */
	function jumpTo(url) {
		uni.navigateTo({
			url: url
		});
	}
	
	/** 获取位置信息 */
	function otgetLocation(){
		uni.getLocation({
		  type: 'gcj02',
		  success: function (res) {
				/** 把位置信息存储到本地 */
				uni.setStorageSync('longitude', res.longitude)
				uni.setStorageSync('latitude', res.latitude)
		  }
		})
	}
	
	onMounted(()=>{
		otgetLocation()
	})
</script>

<style lang="scss" scoped>
	::v-deep .u-form-item__body__left {
		padding: 10rpx 0;
	}

	::v-deep .u-form-item__body__right {
		padding: 20rpx;
		background: #F0F2F5;
		border-radius: 16rpx;
		font-weight: 500;
		font-size: 32rpx !important;
		color: $uni-main-color;
	}

	::v-deep .uni-input-input {
		font-weight: 500;
		font-size: 32rpx !important;
		color: $uni-main-color;
	}

	::v-deep .uni-input-placeholde {
		color: $uni-secondary-color;
	}
	
	.flex-sty{
		display: flex;
		justify-content: space-between;
	}

	.page-login-container {
		width: 100%;
		height: 100%;
		background-color: #ffffff;

		.page-login-top {
			width: 100%;
			height: 656rpx;
			background-image: url('../../static/images/bg1.png');
			background-repeat: no-repeat;
			background-position: center center;

			.txt1 {
				height: 88rpx;
				font-size: 34rpx;
				font-weight: 500;
				text-align: center;
				line-height: 88rpx;
				color: #fff;
			}

			.page-login-logo {
				margin-top: 52rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.txt2 {
				margin-top: 40rpx;
				text-align: center;
				height: 60rpx;
				font-weight: 600;
				font-size: 44rpx;
				color: #FFFFFF;
				line-height: 60rpx;
			}
		}

		.page-login-bot {
			position: absolute;
			top: 492rpx;
			/* #ifdef H5 */
			top: 404rpx;
			/* #endif */
			left: 0;
			right: 0;
			width: 100%;
			height: 1100rpx;
			background: #FFFFFF;
			border-radius: 24rpx 24rpx 0rpx 0rpx;
			box-sizing: border-box;
			padding: 72rpx 32rpx;

			.txt3 {
				margin-top: 26rpx;
				height: 40rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: $uni-secondary-color;
				line-height: 40rpx;
				text-align: left;
				font-style: normal;
			}

			.txt4 {
				color: $uni-primary;
			}
			
			.txt5{
				font-weight: 500;
				font-size: 24rpx;
				color: #575E65;
				// line-height: 40rpx;
			}
		}

		.btn1 {
			margin-top: 98rpx;
			width: 100%;
			height: 88rpx;
			line-height: 88rpx;
			background: #B3D8FF;
			border-radius: 8rpx;
			font-weight: 500;
			font-size: 26rpx;
			color: #FFFFFF;
			text-align: center;
			font-style: normal;
		}

		.btn2 {
			background-color: $uni-primary;
		}
	}
</style>