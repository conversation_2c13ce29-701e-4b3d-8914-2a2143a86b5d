<template>
	<view class="page-name-container">
		<!-- 导航栏 -->
		<navbar title="修改名字" />
		<view class="name-txt1">方便业务发展需要，请使用您的真实姓名</view>
		<up-form labelPosition="top" :model="baseFormData" :rules="rules" ref="baseForm" labelWidth="100">
			<up-form-item label="" prop="name">
				<up-input v-model="baseFormData.name" :clearable="true" placeholder="请输入名字" border="none"></up-input>
			</up-form-item>
			<view class="btn-box">
				<view v-if="showLoginBtn" class="btn1 btn2" @click="formSubmit">完成</view>
				<view v-else class="btn1">完成</view>
			</view>
		</up-form>
	</view>
</template>

<script setup lang="ts">
	import { ref, reactive, computed } from 'vue';
	import navbar from '@/components/navbar/navbar.vue';
	import { updateInfoApi } from '@/api/user';
	import useUserStore from '@/store/user';
	 
	const userStore = useUserStore();

	const baseFormData = ref({
		name: userStore.userInfo.name,
	});

	const rules = reactive({
		name: [{ required: true, message: '请输入名字', trigger: 'blur' }],
	})
	
	let showLoginBtn = computed(() => {
		if (baseFormData.value.name) {
			return true
		} else {
			return false
		}
	})
	
	// 表单引用
	const baseForm = ref(null); 
	/** 完成按钮 */
	function formSubmit(){
		baseForm.value.validate().then((valid : any) => {
			if (valid) {
				otupdateInfoApi()
			}else{
				
			}
	})
	}
	
	/** 修改资料 */
	async function otupdateInfoApi(){
		const params = {
			id: userStore.userInfo.id,
			name: baseFormData.value.name,
		}
		const res = await updateInfoApi(params)
		if(res.code == 200){
			userStore.otgetUserInfoApi().then(()=>{
				uni.utils.toast(res.msg)
				setTimeout(()=>{
					uni.redirectTo({
						url:'/pages/my/editingInfo'
					})
				},1000)
			})
		}else{
			userStore.otgetUserInfoApi().then(()=>{
				uni.utils.toast(res.msg)
			})
		}
	}
	
</script>

<style lang="scss" scoped>
	::v-deep .u-form-item__body__left {
		padding: 10rpx 0;
		font-weight: 600;
		font-size: 28rpx;
		color: $uni-main-color;
		font-style: normal;
	}
	
	::v-deep .u-form-item__body__right {
		padding: 20rpx 20rpx;
		background: #FFFFFF;
		border-radius: 16rpx;
	}
	
	::v-deep .uni-input-input{
		font-weight: 500;
		font-size: 32rpx;
		color: $uni-main-color;
	}
	
	::v-deep .uni-input-placeholde {
		color: $uni-secondary-color;
	}

	.page-name-container {
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		padding: 32rpx;
		
		.name-txt1{
			height: 40rpx;
			font-weight: 400;
			font-size: 24rpx;
			color: $uni-secondary-color;
			line-height: 40rpx;
			font-style: normal;
		}
		
		.btn-box {
			position: fixed;
			left: 0;
			bottom: 0;
			width: 100%;
			height: 188rpx;
			box-sizing: border-box;
			padding: 0 32rpx;
			background-color: #f6f8fc;
			
			.btn1 {
				width: 100%;
				height: 88rpx;
				line-height: 88rpx;
				background: #B3D8FF;
				border-radius: 8rpx;
				font-weight: 500;
				font-size: 26rpx;
				color: #FFFFFF;
				text-align: center;
				font-style: normal;
			}
			
			.btn2 {
				background-color: $uni-primary;
			}
		}
	}
</style>