// 导入uni-app-fetch包
import { createUniFetch } from 'uni-app-fetch';
// 导入store
import useUserStore from '@/store/user';

let loadingTimer = null; // 定义一个定时器来控制加载提示的显示频率

// 创建createUniFetch实例对象
const uniFetch = createUniFetch({
	baseURL: 'https://dev.server.keyupay.com/platform_api/', // 根地址
	// baseURL: 'https://server.keyupay.com/platform_api/', // 根地址
	intercept: {
		// 请求拦截器
		request(options) {
			const userStore = useUserStore();

			// 如果没有正在加载，并且没有正在计时的定时器
			if (!loadingTimer) {
				// 在第一次请求时显示loading提示
				uni.showToast({
					title: '正在加载...',
					image: '/static/images/index.svg',
					mask: true
				});
				// 设置一个定时器，3秒内如果有请求，显示一次loading，不重复显示
				loadingTimer = setTimeout(() => {
					loadingTimer = null; // 清空定时器
				}, 3000);
			}

			// 设置默认请求头
			const defaultHeader = {
				token: userStore.token || ''
			};

			// 对options的header进行覆盖
			options.header = Object.assign({}, defaultHeader, options.header);

			return options;
		},
		// 响应拦截器
		response(response) {
			if (response.data.code == 403) {
				// token失效，跳转到登录页
				uni.reLaunch({
					url: '/pages/login/index'
				});
			}
			// 请求完成后隐藏loading
			uni.hideLoading();
			return response.data;
		}
	}
});

// 默认导出方式
export default uniFetch;

// 全局挂载方式
uni.$http = uniFetch;