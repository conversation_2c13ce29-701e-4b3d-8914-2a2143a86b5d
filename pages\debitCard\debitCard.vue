<template>
	<view class="">
		<!-- 导航栏 -->
		<navbar title="开通支付" backGroundColor="#fff" />
		<!-- 步骤条 -->
		<StepBar :currentStep="4" />
	</view>
	<view class="content">
		<view class="schemeSelection">
			<view class="title">
				结算类型
				<span class="required">*</span>
			</view>
			<view class="elect">
				<view class="list" v-for="(item, index) in settlementTypes" :key="index"
					:class="{ active: selectedType === item.type }" @click="selectType(index, item)">
					<text class="text">{{ item.name }}</text>
					<image v-if="selectedType === item.type" src="@/static/images/gouxuan.png" class="icon"></image>
				</view>
			</view>
		</view>
		<view v-if="selectedName === '对私法人账户' || selectedName === '对私非法人账户'" class="schemeSelection">
			<view class="" style="display: flex; justify-content: space-between">
				<view class="title">上传结算卡</view>
				<view class="checkbox">
					<up-checkbox :customStyle="{ marginBottom: '8px' }" shape="circle" label="暂不提交结算卡" name="agree"
						:checked="aloneChecked" labelColor="#575E65" labelSize="24rpx" size="16" activeColor="#1386fb"
						@change="toggleAlert" :usedAlone="true"></up-checkbox>
				</view>
			</view>
			<p class="hint">请上传真实有效信息，以下信息将用于各类申请</p>
		</view>
		<view v-if="selectedName === '对私法人账户' && !aloneChecked" class="uploading">
			<view class="uploadingMessage">
				<view class="title">请上传结算卡</view>
				<view class="text">请上传结算卡正反面，大小不超过2M</view>
			</view>
			<view class="uploadingImages">
				<view class="uploadBox" @longpress="frontImageUrl && openPopup('front')">
					<image class="charter" v-if="frontImageUrl" :src="baseURL + frontImageUrl" mode="aspectFit"></image>
					<image class="charter" v-else src="@/static/images/jiesuanka.png" mode="aspectFit" @click="uploadFrontImage">
					</image>
					<view class="btnBox" :class="{ uploaded: isFrontImageUploaded, failed: frontImageUploadFailed }"
						@click="uploadFrontImage">
						<view class="btn" v-if="isFrontImageUploaded">上传成功</view>
						<view class="btn" v-else-if="frontImageUploadFailed">上传失败!</view>
						<view class="btn" v-else>上传结算卡正面</view>
					</view>
				</view>
				<view class="uploadBox" @longpress="backImageUrl && openPopup('back')">
					<image class="charter" v-if="backImageUrl" :src="baseURL + backImageUrl" mode="aspectFit"></image>
					<image class="charter" v-else src="@/static/images/jiesuanka.png" mode="aspectFit" @click="uploadBackImage">
					</image>
					<view class="btnBox" :class="{ uploaded: isBackImageUploaded, failed: backImageUploadFailed }"
						@click="uploadBackImage">
						<view class="btn" v-if="isBackImageUploaded">上传成功</view>
						<view class="btn" v-else-if="backImageUploadFailed">上传失败!</view>
						<view class="btn" v-else>上传结算卡反面</view>
					</view>
				</view>
			</view>
		</view>
		<view v-if="selectedName === '对公账户'" class="schemeSelection">
			<view class="" style="display: flex; justify-content: space-between">
				<view class="title">上传开户许可证</view>
			</view>
			<p class="hint">请上传真实有效信息，以下信息将用于各类申请</p>
		</view>
		<view v-if="selectedName === '对公账户'" class="uploading" style="height: 450rpx">
			<view class="uploadingImages" style="justify-content: center">
				<view style="width: 410rpx; margin-top: 40rpx" class="uploadBox"
					@longpress="merchantImgPermitImageUrl && openPopup('merchantImgPermit')">
					<image class="charter" v-if="merchantImgPermitImageUrl" :src="baseURL + merchantImgPermitImageUrl"
						mode="aspectFit"></image>
					<image class="charter" v-else src="@/static/images/jiesuanka.png" mode="aspectFit"
						@click="uploadmerchantImgPermitImage"></image>
					<view class="btnBox"
						:class="{ uploaded: ********************************, failed: merchantImgPermitImageUploadFailed }"
						@click="uploadmerchantImgPermitImage">
						<view class="btn" v-if="********************************">上传成功</view>
						<view class="btn" v-else-if="merchantImgPermitImageUploadFailed">上传失败!</view>
						<view class="btn" v-else>开户许可证</view>
					</view>
				</view>
			</view>
		</view>
		<view v-if="selectedName === '对私非法人账户' && !aloneChecked" class="uploading" style="height: 1000rpx">
			<view class="uploadingMessage">
				<view class="title">请上传结算卡</view>
				<view class="text">请上传结算卡正反面，大小不超过2M</view>
			</view>
			<view class="uploadingImages">
				<view class="uploadBox" @longpress="frontImageUrl && openPopup('front')">
					<image class="charter" v-if="frontImageUrl" :src="baseURL + frontImageUrl" mode="aspectFit"></image>
					<image class="charter" v-else src="@/static/images/jiesuanka.png" mode="aspectFit" @click="uploadFrontImage">
					</image>
					<view class="btnBox" :class="{ uploaded: isFrontImageUploaded, failed: frontImageUploadFailed }"
						@click="uploadFrontImage">
						<view class="btn" v-if="isFrontImageUploaded">上传成功</view>
						<view class="btn" v-else-if="frontImageUploadFailed">上传失败!</view>
						<view class="btn" v-else>上传结算卡正面</view>
					</view>
				</view>
				<view class="uploadBox" @longpress="backImageUrl && openPopup('back')">
					<image class="charter" v-if="backImageUrl" :src="baseURL + backImageUrl" mode="aspectFit"></image>
					<image class="charter" v-else src="@/static/images/jiesuanka.png" mode="aspectFit" @click="uploadBackImage">
					</image>
					<view class="btnBox" :class="{ uploaded: isBackImageUploaded, failed: backImageUploadFailed }"
						@click="uploadBackImage">
						<view class="btn" v-if="isBackImageUploaded">上传成功</view>
						<view class="btn" v-else-if="backImageUploadFailed">上传失败!</view>
						<view class="btn" v-else>上传结算卡反面</view>
					</view>
				</view>
			</view>
			<view class="uploadingMessage">
				<view class="title">请上传授权结算书</view>
				<view class="text">请上传授权予益科技结算书，大小不超过2M</view>
			</view>
			<view class="uploadingImages" style="justify-content: center">
				<view class="uploadBox" @longpress="authorizationImageUrl && openPopup('authorization')">
					<image class="charter" v-if="authorizationImageUrl" :src="baseURL + authorizationImageUrl" mode="aspectFit">
					</image>
					<image class="charter" v-else src="@/static/images/jiesuanka.png" mode="aspectFit"
						@click="uploadAuthorizationImage"></image>
					<view class="btnBox"
						:class="{ uploaded: isAuthorizationImageUploaded, failed: authorizationImageUploadFailed }"
						@click="uploadAuthorizationImage">
						<view class="btn" v-if="isAuthorizationImageUploaded">上传成功</view>
						<view class="btn" v-else-if="authorizationImageUploadFailed">上传失败!</view>
						<view class="btn" v-else>上传授权结算书</view>
					</view>
				</view>
			</view>
		</view>
		<view class="uploading" style="height: 520rpx" v-if="selectedName === '对私非法人账户' && aloneChecked == true">
			<view class="uploadingMessage">
				<view class="title">请上传授权结算书</view>
				<view class="text">请上传授权予益科技结算书，大小不超过2M</view>
			</view>
			<view class="uploadingImages" style="justify-content: center">
				<view class="uploadBox" @longpress="authorizationImageUrl && openPopup('authorization')">
					<image class="charter" v-if="authorizationImageUrl" :src="baseURL + authorizationImageUrl" mode="aspectFit">
					</image>
					<image class="charter" v-else src="@/static/images/jiesuanka.png" mode="aspectFit"
						@click="uploadAuthorizationImage"></image>
					<view class="btnBox"
						:class="{ uploaded: isAuthorizationImageUploaded, failed: authorizationImageUploadFailed }"
						@click="uploadAuthorizationImage">
						<view class="btn" v-if="isAuthorizationImageUploaded">上传成功</view>
						<view class="btn" v-else-if="authorizationImageUploadFailed">上传失败!</view>
						<view class="btn" v-else>上传授权结算书</view>
					</view>
				</view>
			</view>
		</view>
		<view class="require">
			<view class="title">拍摄要求</view>
			<view class="imgList">
				<view class="img1">
					<image src="@/static/images/biaozhun.png" mode="widthFix" style="width: 160rpx"></image>
					<view class="text">
						<image src="@/static/images/duihao.png" mode="widthFix" style="width: 32rpx"></image>
						<view class="word">标准</view>
					</view>
				</view>
				<view class="img2">
					<image src="@/static/images/queshi.png" mode="widthFix" style="width: 160rpx"></image>
					<view class="text">
						<image src="@/static/images/cuowu.png" mode="widthFix" style="width: 32rpx"></image>
						<view class="word">边角缺失</view>
					</view>
				</view>
				<view class="img3">
					<image src="@/static/images/mohu.png" mode="widthFix" style="width: 160rpx"></image>
					<view class="text">
						<image src="@/static/images/cuowu.png" mode="widthFix" style="width: 32rpx"></image>
						<view class="word">照片模糊</view>
					</view>
				</view>
				<view class="img4">
					<image src="@/static/images/fanguang.png" mode="widthFix" style="width: 160rpx"></image>
					<view class="text">
						<image src="@/static/images/cuowu.png" mode="widthFix" style="width: 32rpx"></image>
						<view class="word">反光强烈</view>
					</view>
				</view>
			</view>
		</view>
		<view v-if="!aloneChecked" class="messageBox">
			<view class="message">
				<view class="title">账户信息</view>
				<view class="form">
					<up-form labelPosition="left" :model="model1" :rules="rules" ref="form1">
						<up-form-item prop="settlement_account_name" borderBottom>
							<view class="text" style="width: 40%">
								账户名
								<span class="required">*</span>
							</view>
							<up-input border="none" placeholder="请输入账户名" v-model="form.settlement_account_name"></up-input>
						</up-form-item>
						<up-form-item prop="settlement_card_number" borderBottom>
							<view class="text" style="width: 40%">
								账户号
								<span class="required">*</span>
							</view>
							<up-input border="none" placeholder="上传后自动识别或手动输入" v-model="form.settlement_card_number"></up-input>
						</up-form-item>
						<up-form-item prop="settlement_bank" borderBottom>
							<view class="text" style="width: 40%">
								开户行
								<span class="required">*</span>
							</view>
							<view style="display: flex; align-items: center" @click="openSelectBankPopup">
								<up-input v-if="currentGangway === 'changsha_bank'" border="none" placeholder="请选择开户行" color="#9DA5AD"
									v-model="form.settlement_bank" :disabled="true" disabledColor="#fff"></up-input>
								<!-- 非changsha_bank开户行显示激活的下拉图标 -->
								<up-input v-else border="none" placeholder="请选择开户行" v-model="form.settlement_bank"
									@click="openSelectBankPopup" :disabled="true" disabledColor="#fff"></up-input>
								<image v-if="currentGangway === 'changsha_bank'" src="@/static/images/xiala.png" mode="aspectFit"
									style="width: 32rpx; height: 32rpx" @click="openSelectBankPopup"></image>
								<image v-else src="@/static/images/xialajihuo.png" mode="aspectFit" style="width: 32rpx; height: 32rpx"
									@click="openSelectBankPopup"></image>
							</view>
						</up-form-item>
						<up-form-item prop="settlement_open_subbank" borderBottom>
							<view class="text" style="width: 40%">
								开户支行
								<span class="required">*</span>
							</view>
							<view style="display: flex; align-items: center" @click="openSelectSubBranchPopup">
								<up-input border="none" placeholder="请选择开户支行" @click="openSelectSubBranchPopup"
									v-model="truncatedSubBank" :disabled="true" disabledColor="#fff"></up-input>
								<image src="@/static/images/xialajihuo.png" mode="aspectFit" style="width: 32rpx; height: 32rpx"
									@click="openSelectSubBranchPopup"></image>
							</view>
						</up-form-item>
						<up-form-item prop="settlement_bank_tel" borderBottom>
							<view class="text" style="width: 40%">
								银行预留手机号
								<span class="required">*</span>
							</view>
							<up-input border="none" placeholder="请输入银行预留手机号" v-model="form.settlement_bank_tel"></up-input>
						</up-form-item>
					</up-form>
				</view>
			</view>
		</view>
		<view v-if="selectedName === '对私非法人账户'" class="schemeSelection">
			<view class="" style="display: flex; justify-content: space-between">
				<view class="title">上传结算人身份证</view>
			</view>
			<p class="hint">请上传真实有效信息，以下信息将用于各类申请</p>
		</view>
		<view v-if="selectedName === '对私非法人账户'" class="uploading" style="height: 1000rpx">
			<view class="uploadingMessage">
				<view class="title">请上传身份证</view>
				<view class="text">请上传结算人身份证正反面，大小不超过2M</view>
			</view>
			<view class="uploadingImages">
				<view class="uploadBox" @longpress="idCardFrontUrl && openPopup('idCardFront')">
					<image v-if="idCardFrontUrl" class="charter" :src="baseURL + idCardFrontUrl" mode="aspectFit"></image>
					<image v-else class="charter" src="@/static/images/renxiang.png" mode="aspectFit"
						@click="uploadIdCardFrontImage"></image>
					<view class="btnBox" :class="{ uploaded: isIdCardFrontUploaded, failed: idCardFrontUploadFailed }"
						@click="uploadIdCardFrontImage">
						<view class="btn" v-if="isIdCardFrontUploaded">上传成功</view>
						<view class="btn" v-else-if="idCardFrontUploadFailed">无法识别,请重新上传</view>
						<view class="btn" v-else>上传人像页</view>
					</view>
				</view>
				<view class="uploadBox" @longpress="idCardBackUrl && openPopup('idCardBack')">
					<image v-if="idCardBackUrl" class="charter" :src="baseURL + idCardBackUrl" mode="aspectFit"></image>
					<image v-else class="charter" src="@/static/images/guohui.png" mode="aspectFit"
						@click="uploadIdCardBackImage"></image>
					<view class="btnBox" :class="{ uploaded: isIdCardBackUploaded, failed: idCardBackUploadFailed }"
						@click="uploadIdCardBackImage">
						<view class="btn" v-if="isIdCardBackUploaded">上传成功</view>
						<view class="btn" v-else-if="idCardBackUploadFailed">无法识别,请重新上传</view>
						<view class="btn" v-else>上传国徽页</view>
					</view>
				</view>
			</view>
			<view class="uploadingMessage">
				<view class="title">请上传结算人手持身份证</view>
				<view class="text">请上传结算人身份证正面，大小不超过2M</view>
			</view>
			<view class="uploadingImages" style="justify-content: center">
				<view class="uploadBox" @click="uploadHandheldIdCardImage"
					@longpress="handheldIdCardUrl && openPopup('handheld')">
					<image class="charter" v-if="handheldIdCardUrl" :src="baseURL + handheldIdCardUrl" mode="aspectFit"></image>
					<image class="charter" v-else src="@/static/images/renxiang.png" mode="aspectFit"></image>
					<view class="btnBox" :class="{ uploaded: isHandheldIdCardUploaded, failed: handheldIdCardUploadFailed }">
						<view class="btn" v-if="isHandheldIdCardUploaded">上传成功</view>
						<view class="btn" v-else-if="handheldIdCardUploadFailed">上传失败!</view>
						<view class="btn" v-else>上传结算人手持身份证</view>
					</view>
				</view>
			</view>
		</view>
		<view class="messageBox" v-if="selectedName === '对私非法人账户'">
			<view class="message">
				<view class="title">结算人信息</view>
				<view class="form">
					<up-form labelPosition="left" :model="model1" :rules="rules" ref="form1">
						<up-form-item prop="name" borderBottom>
							<view class="text" style="width: 50%">
								结算人姓名
								<span class="required">*</span>
							</view>
							<up-input border="none" placeholder="上传后自动识别或手动输入" v-model="picDataFront.name"></up-input>
						</up-form-item>
						<up-form-item prop="number" borderBottom>
							<view class="text" style="width: 50%">
								结算人身份证号
								<span class="required">*</span>
							</view>
							<up-input border="none" placeholder="上传后自动识别或手动输入" v-model="picDataFront.number"></up-input>
						</up-form-item>
						<up-form-item prop="valid_from" borderBottom>
							<view class="text" style="width: 50%">
								结算人身份证生效日期
								<span class="required">*</span>
							</view>
							<up-input border="none" placeholder="上传后自动识别或手动输入" v-model="picDataBack.valid_from"></up-input>
						</up-form-item>
						<up-form-item prop="valid_to" borderBottom>
							<view class="text" style="width: 50%">
								结算人身份证有效期
								<span class="required">*</span>
							</view>
							<up-input border="none" placeholder="上传后自动识别或手动输入" v-model="picDataBack.valid_to"></up-input>
						</up-form-item>
					</up-form>
				</view>
			</view>
		</view>
		<view class="binding">
			<view class="title">绑定付款码</view>
			<view class="bindingBtn">
				<view class="text">{{ displayedPayCodeId || payCodeId || '暂无' }}</view>
				<view class="btn" @click="openBindingPopup">
					<view class="payText">去绑定</view>
					<image src="@/static/images/bangding.png" mode="aspectFit" style="width: 32rpx; height: 32rpx"></image>
				</view>
			</view>
		</view>
		<view class="bottomBox">
			<view class="box">
				<view class="backBox" @click="back">
					<view class="text">上一步</view>
				</view>
				<view class="nextBox" :class="{ active: allUploaded }" @click="submit">
					<view class="text">提交</view>
				</view>
			</view>
		</view>
		<view class="" style="width: 100%; height: 226rpx"></view>
		<!-- 图片长摁弹窗组件 -->
		<view class="longPress">
			<PoperImg :show="isPopupVisible" @close="closePopup" @delete="deleteImage" @reupload="reuploadImage" />
		</view>
		<!-- 绑定弹窗 -->
		<up-popup :show="isBindingPopupVisible" mode="center" closeable @close="closeBindingPopup"
			:closeOnClickOverlay="false">
			<view class="bindingPoper">
				<view class="title">绑定付款码</view>
				<view class="hint">请输入收款码ID或扫描收款码以完成绑定</view>
				<view class="" style="margin: 0 32rpx; margin-top: 32rpx">
					<up-input v-model="payCodeId" placeholder="请输入收款ID" border="surround">
						<template #suffix>
							<image @click="scanHandel" src="@/static/images/saoyisao.png" mode="aspectFit"
								style="width: 44rpx; height: 44rpx"></image>
						</template>
					</up-input>
					<view class="bindingPoperBtn" :style="{ backgroundColor: payCodeId ? '#1386FB' : '#7abcff' }"
						@click="submitIfNotEmpty">
						<text class="txt">确定</text>
					</view>
				</view>
			</view>
		</up-popup>
		<!-- 开户行弹窗组件 -->
		<Selectbranch v-if="currentGangway !== 'changsha_bank'" :show="showPopup" @closeHandel="closeSelectBranchPopup"
			@confirm="setBank" />
		<!-- 开户支行弹窗组件 -->
		<Selectsub :show="showSubPopup" @closeHandel="closeSelectSubBranchPopup" @confirm="setSubBank" />
	</view>
</template>

<script setup>
	import {
		ref,
		reactive,
		inject,
		onMounted,
		computed,
		onBeforeUnmount,
		watch,
		watchEffect
	} from 'vue';
	import navbar from '@/components/navbar/navbar.vue';
	import StepBar from '@/components/steps.vue';
	import PoperImg from '@/components/PoperImg.vue';
	import qrcode from '@/utils/qrcode.js';
	import useUserStore from '@/store/user';
	import {
		submitDebitCard,
		getBankBranch
	} from '@/api/debitCard';
	import {
		getMerchantApi
	} from '@/api/merchant';
	import {
		verifyCardId
	} from '@/utils/rules';
	import Selectbranch from '@/pages/debitCard/Selectbranch.vue';
	import Selectsub from '@/pages/debitCard/Selectsub.vue';

	/** 获取位置信息 */
	function otgetLocation() {
		uni.getLocation({
			type: 'gcj02', // 返回可以用于uni.openLocation的经纬度
			success: function(res) {
				/** 把位置信息存储到本地 */
				uni.setStorageSync('longitude', res.longitude);
				uni.setStorageSync('latitude', res.latitude);
			},
			fail: function(err) {
				console.log('获取位置信息失败：' + JSON.stringify(err));
			}
		});
	}
	const settlementTypes = ref([{
			name: '对私法人账户',
			type: 0
		},
		{
			name: '对私非法人账户',
			type: 1
		},
		{
			name: '对公账户',
			type: 2
		}
	]);

	const maxLength = 10;
	/** 文本超过十个字显示... */
	const truncatedSubBank = computed(() => {
		return form.settlement_open_subbank.length > maxLength ? form.settlement_open_subbank.slice(0, maxLength) +
			'...' : form.settlement_open_subbank;
	});
	/** 设置开户行弹窗 */
	const showPopup = ref(false);
	/** 打开开户行弹窗 */
	function openSelectBankPopup() {
		showPopup.value = true;
	}
	/** 关闭开户行弹窗 */
	const closeSelectBranchPopup = () => {
		showPopup.value = false; // 关闭弹窗
	};
	/** 选择开户行后，接收子组件传来的开户行值 */
	function setBank(selectedBank) {
		form.settlement_bank = selectedBank; // 将选中的开户行赋值给输入框
		showPopup.value = false; // 关闭弹窗
		uni.setStorageSync('selectedBank', selectedBank); // 存储到本地存储
	}

	/** 用于显示开户支行弹窗的状态 */
	const showSubPopup = ref(false);
	/** 打开开户支行弹窗 */
	function openSelectSubBranchPopup() {
		if (!form.settlement_bank) {
			uni.showToast({
				title: '请先选择开户行',
				icon: 'none'
			});
			return;
		}
		showSubPopup.value = true;
	}
	/** 关闭开户支行弹窗 */
	const closeSelectSubBranchPopup = () => {
		showSubPopup.value = false; // 关闭弹窗
	};
	/** 选择开户支行后，接收子组件传来的开户支行值 */
	function setSubBank(selectedSubBank) {
		form.settlement_open_subbank = selectedSubBank.branch_name; // 将选中的开户支行赋值给输入框
		form.settlement_bank_code = selectedSubBank.banking; // 将选中的开户支行联行号赋值
		showSubPopup.value = false; // 关闭弹窗
	}

	const form = reactive({
		settlement_bank: '',
		settlement_open_subbank: '',
		settlement_account_name: '',
		settlement_bank_code: '',
		settlement_card_number: '',
		settlement_bank_tel: ''
	});

	const change = (e) => {
		form.settlement_open_subbank = e.label;
		form.settlement_bank_code = e.value;
	};

	const fetchMerchantData = async (merchantId) => {
		const response = await getMerchantApi(merchantId);
		if (response.code === 200) {
			const data = response.data;
			if (data.current_gangway == 'icbc_bank') {
				settlementTypes.value = [{
						name: '对私法人账户',
						type: 0
					},
					{
						name: '对公账户',
						type: 2
					}
				];
			}
			if (data.current_gangway === 'changsha_bank') {
				settlementTypes.value = [{
						name: '对私法人账户',
						type: 0
					},
					{
						name: '对公账户',
						type: 2
					}
				];
				// 如果通道为长沙银行，设置默认值为“长沙银行”并禁用
				form.settlement_bank = '长沙银行';
				form.settlement_bank_disabled = true; // 设置禁用状态
			} else {
				form.settlement_bank_disabled = false; // 非长沙银行时启用编辑
			}
			currentGangway.value = data.current_gangway; // 保存当前通道
			// 判断是否为长沙银行通道
			if (currentGangway.value === 'changsha_bank') {
				uni.setStorageSync('selectedBank', '长沙银行'); // 存储到本地
			} else {
				uni.setStorageSync('selectedBank', ''); // 存储到本地
			}
			displayedPayCodeId.value = data.codes;
			frontImageUrl.value = data.merchant_img_bank_card || '';
			backImageUrl.value = data.merchant_img_bank_card_back || '';
			authorizationImageUrl.value = data.merchant_img_authorization_statement || '';
			(merchantImgPermitImageUrl.value = data.merchant_img_permit_for_bank_account || ''), (isFrontImageUploaded
				.value = !!frontImageUrl.value);
			isBackImageUploaded.value = !!backImageUrl.value;
			isAuthorizationImageUploaded.value = !!authorizationImageUrl.value;
			form.settlement_account_name = data.settlement_account_name || '';
			form.settlement_card_number = data.settlement_card_number || '';
			form.settlement_bank = data.settlement_bank || '';
			form.settlement_bank_code = data.settlement_bank_code || '';
			form.settlement_open_subbank = data.settlement_open_subbank || '';
			cityName.value = data.city_name;
			handheldIdCardUrl.value = data.merchant_img_holding_photo || '';
			form.settlement_bank_tel = data.settlement_bank_tel || '';
			selectedType.value = data.settlement_account_type_to || 0;
			selectedName.value = data.settlement_account_type == '00' ? '对公账户' : data.settlement_account_type == '02' ?
				'对私非法人账户' : '对私法人账户';
			payCodeId.value = data.merchan_qrcode || '';
			aloneChecked.value = data.not_submitted == 0 ? false : true;
			idCardFrontUrl.value = data.merchant_img_principal_id_card_front;
			idCardBackUrl.value = data.merchant_img_principal_id_card_obverse;
			picDataFront.value = {
				name: data.crp_nm,
				number: data.crp_id
			};
			picDataBack.value = {
				valid_from: data.crp_start_dt,
				valid_to: data.s_legal_exp_dt
			};
			isIdCardFrontUploaded.value = !!idCardFrontUrl.value;
			isIdCardBackUploaded.value = !!idCardBackUrl.value;
			showAlert.value = isIdCardFrontUploaded.value && isIdCardBackUploaded.value;
		}
	};

	onBeforeUnmount(async () => {
		handleBeforeUnload();
		// 清空本地存储中的 selectedBank
		uni.removeStorageSync('selectedBank');
	});

	onMounted(async () => {
		otgetLocation();
		merchant_id.value = uni.getStorageSync('merchant_id');
		if (merchant_id.value) {
			await fetchMerchantData(merchant_id.value);
		}
		// 从本地存储中获取code_data
		const storedCodeData = uni.getStorageSync('code_data');
		if (storedCodeData) {
			payCodeId.value = storedCodeData;
		}
		const storedBank = uni.getStorageSync('selectedBank');
		if (storedBank) {
			form.settlement_bank = storedBank; // 如果有已存储的开户行信息，则赋值到表单字段
		}
	});

	const baseURL = inject('baseURL');

	/** 获取顶部状态栏高度 */
	const statusBarHeight = uni.getStorageSync('statusBarHeight');

	const selectedType = ref(0);
	const selectedName = ref(); //结算账户方式名称
	const payCodeId = ref(''); // 绑定收款码id
	const merchant_id = ref(''); // 商户添加页面传过来的id
	const idCardFrontUrl = ref(''); // 身份证正面图片URL
	const idCardBackUrl = ref(''); // 身份证反面图片URL
	const isIdCardFrontUploaded = ref(false); // 身份证正面是否上传成功
	const isIdCardBackUploaded = ref(false); // 身份证反面是否上传成功
	const idCardFrontUploadFailed = ref(false); // 身份证正面上传是否失败
	const idCardBackUploadFailed = ref(false); // 身份证反面上传是否失败
	const handheldIdCardUrl = ref(''); // 手持身份证图片URL
	const isHandheldIdCardUploaded = ref(false); // 手持身份证是否上传成功
	const handheldIdCardUploadFailed = ref(false); // 手持身份证上传是否失败

	const picDataFront = ref({}); // 存储身份证正面识别的数据
	const picDataBack = ref({}); // 存储身份证反面识别的数据
	const currentGangway = ref(''); // 长沙银行通道字段

	const userStore = useUserStore();

	const cityName = ref();
	const cusSelectsRef = ref(null);
	const displayedPayCodeId = ref(''); // 用于显示绑定码

	function selectType(index, item) {
		selectedType.value = item.type;
		selectedName.value = item.name;
		aloneChecked.value = false; // 重置暂不提交结算卡状态
		// 使用引用调用子组件方法，清空子组件的选择框内容
		if (cusSelectsRef.value) {
			cusSelectsRef.value.clearItem(); // 假设子组件内定义了 clearItem() 用来清空输入
		}
		// 检查是否为长沙银行，保留开户行字段
		if (currentGangway.value !== 'changsha_bank') {
			resetFormAndImages(); // 清空表单和图片数据，除了开户行字段
		}
		// 继续清空身份证信息表单字段
		idCardFrontUrl.value = '';
		idCardBackUrl.value = '';
		isIdCardFrontUploaded.value = false;
		isIdCardBackUploaded.value = false;
		idCardFrontUploadFailed.value = false;
		idCardBackUploadFailed.value = false;
		picDataFront.value = {
			name: '',
			number: ''
		};
		picDataBack.value = {
			valid_from: '',
			valid_to: ''
		};
	}

	const handleBeforeUnload = async () => {
		// 用户正在离开页面
		const data = {
			merchant_id: uni.getStorageSync('merchant_id') || '',
			codes: displayedPayCodeId.value,
			not_submitted: aloneChecked.value ? 1 : 0,
			settlement_bank: currentGangway.value === 'changsha_bank' ? '长沙银行' : form.settlement_bank,
			settlement_open_subbank: form.settlement_open_subbank,
			settlement_account_name: form.settlement_account_name,
			settlement_bank_code: form.settlement_bank_code,
			settlement_card_number: form.settlement_card_number,
			settlement_bank_tel: form.settlement_bank_tel,
			settlement_account_type: selectedName.value === '对私法人账户' ? '01' : selectedName.value === '对私非法人账户' ? '02' :
				'00',
			merchant_img_authorization_statement: selectedName.value === '对私非法人账户' ? authorizationImageUrl.value : '',
			merchant_img_permit_for_bank_account: selectedName.value === '对公账户' ? merchantImgPermitImageUrl.value : '',
			merchant_img_bank_card: frontImageUrl.value,
			merchant_img_bank_card_back: backImageUrl.value,
			merchant_img_principal_id_card_front: idCardFrontUrl.value,
			merchant_img_principal_id_card_obverse: idCardBackUrl.value,
			longitude: uni.getStorageSync('longitude') || '', // 获取经度，如果没有则为空
			latitude: uni.getStorageSync('latitude') || '', // 获取纬度，如果没有则为空
			temp_save: 1,
			merchant_img_holding_photo: handheldIdCardUrl.value, // 添加手持身份证图片URL
			s_legal_name: picDataFront.value.name, // 结算人姓名
			s_legal_id: picDataFront.value.number, // 结算人身份证号
			s_legal_start_dt: picDataBack.value.valid_from, // 结算人身份证生效日期
			s_legal_exp_dt: picDataBack.value.valid_to // 结算人身份证有效期
		};
		await submitDebitCard(data);
	};

	function resetFormAndImages() {
		frontImageUrl.value = '';
		backImageUrl.value = '';
		authorizationImageUrl.value = '';
		isFrontImageUploaded.value = false;
		isBackImageUploaded.value = false;
		isAuthorizationImageUploaded.value = false;
		frontImageUploadFailed.value = false;
		backImageUploadFailed.value = false;
		authorizationImageUploadFailed.value = false;
		merchantImgPermitImageUrl.value = '';
		********************************.value = false;
		merchantImgPermitImageUploadFailed.value = false;
		form.settlement_account_name = '';
		form.settlement_card_number = '';
		form.settlement_bank_code = '';
		form.settlement_bank = '';
		form.settlement_open_subbank = '';
		form.settlement_bank_tel = '';
	}

	const frontImageUrl = ref('');
	const backImageUrl = ref('');
	const authorizationImageUrl = ref('');
	const isFrontImageUploaded = ref(false); // 结算卡正面上传成功状态
	const isBackImageUploaded = ref(false); // 结算卡反面上传成功状态
	const isAuthorizationImageUploaded = ref(false); // 授权结算书上传成功状态
	const frontImageUploadFailed = ref(false); // 结算卡正面上传失败状态
	const backImageUploadFailed = ref(false); // 结算卡反面上传失败状态
	const authorizationImageUploadFailed = ref(false); // 授权结算书上传失败状态
	const merchantImgPermitImageUploadFailed = ref(false);
	const ******************************** = ref(false);
	const merchantImgPermitImageUrl = ref('');
	const isPopupVisible = ref(false); // 控制弹窗显示状态
	const currentImageType = ref(''); // 当前操作的图片类型

	const isBindingPopupVisible = ref(false); // 控制绑定付款码弹窗显示状态
	const isBindingPopupFacility = ref(false); // 控制绑定设备弹窗显示状态

	const aloneChecked = ref(false); // 是否选择暂不提交结算卡
	const showAlert = ref(false); // 显示提示框

	const validateDate = (date) => {
		// 如果有效期为“长期”，直接通过校验
		if (date === '长期') {
			return true;
		}
		// 修改正则表达式以仅匹配有效的日期格式
		const regex = /^(\d{4}-\d{1,2}-\d{1,2}|\d{4}年\d{1,2}月\d{1,2}日)$/;
		if (!regex.test(date)) return false;

		// 验证日期是否合法
		if (date.includes('年')) {
			date = date.replace(/(\d{4})年(\d{1,2})月(\d{1,2})日/, '$1-$2-$3');
		}

		const [year, month, day] = date.split('-').map(Number);
		const dateObj = new Date(year, month - 1, day);
		return dateObj.getFullYear() === year && dateObj.getMonth() + 1 === month && dateObj.getDate() === day;
	};

	// 打开弹窗
	const openPopup = (type) => {
		currentImageType.value = type;
		isPopupVisible.value = true;
	};

	// 关闭弹窗
	const closePopup = () => {
		isPopupVisible.value = false;
		currentImageType.value = '';
	};

	// 删除图片
	const deleteImage = () => {
		if (currentImageType.value === 'front') {
			frontImageUrl.value = '';
			isFrontImageUploaded.value = false;
			frontImageUploadFailed.value = false;
		} else if (currentImageType.value === 'back') {
			backImageUrl.value = '';
			isBackImageUploaded.value = false;
			backImageUploadFailed.value = false;
		} else if (currentImageType.value === 'authorization') {
			authorizationImageUrl.value = '';
			isAuthorizationImageUploaded.value = false;
			authorizationImageUploadFailed.value = false;
		} else if (currentImageType.value === 'merchantImgPermit') {
			merchantImgPermitImageUrl.value = '';
			********************************.value = false;
			merchantImgPermitImageUploadFailed.value = false;
		} else if (currentImageType.value === 'idCardFront') {
			idCardFrontUrl.value = '';
			isIdCardFrontUploaded.value = false;
			idCardFrontUploadFailed.value = false;
		} else if (currentImageType.value === 'idCardBack') {
			idCardBackUrl.value = '';
			isIdCardBackUploaded.value = false;
			idCardBackUploadFailed.value = false;
		} else if (currentImageType.value === 'handheld') {
			handheldIdCardUrl.value = '';
			isHandheldIdCardUploaded.value = false;
			handheldIdCardUploadFailed.value = false;
		}
		closePopup();
	};

	const toggleAlert = (value) => {
		aloneChecked.value = value;
	};

	// 重新上传图片
	const reuploadImage = () => {
		if (currentImageType.value === 'front') {
			frontImageUrl.value = ''; // 清空图片地址
			form.settlement_card_number = ''; // 清空卡号
			isFrontImageUploaded.value = false; // 重置上传状态
			frontImageUploadFailed.value = false; // 重置失败状态
			uploadFrontImage();
		} else if (currentImageType.value === 'back') {
			backImageUrl.value = ''; // 清空图片地址
			isBackImageUploaded.value = false; // 重置上传状态
			backImageUploadFailed.value = false; // 重置失败状态
			uploadBackImage();
		} else if (currentImageType.value === 'authorization') {
			authorizationImageUrl.value = ''; // 清空图片地址
			isAuthorizationImageUploaded.value = false; // 重置上传状态
			authorizationImageUploadFailed.value = false; // 重置失败状态
			uploadAuthorizationImage();
		} else if (currentImageType.value === 'merchantImgPermit') {
			merchantImgPermitImageUrl.value = ''; // 清空图片地址
			********************************.value = false; // 重置上传状态
			merchantImgPermitImageUploadFailed.value = false; // 重置失败状态
			uploadmerchantImgPermitImage();
		} else if (currentImageType.value === 'idCardFront') {
			idCardFrontUrl.value = ''; // 清空图片地址
			isIdCardFrontUploaded.value = false; // 重置上传状态
			idCardFrontUploadFailed.value = false; // 重置失败状态
			picDataFront.value = {}; // 清空身份证正面识别数据
			uploadIdCardFrontImage();
		} else if (currentImageType.value === 'idCardBack') {
			idCardBackUrl.value = ''; // 清空图片地址
			isIdCardBackUploaded.value = false; // 重置上传状态
			idCardBackUploadFailed.value = false; // 重置失败状态
			picDataBack.value = {}; // 清空身份证反面识别数据
			uploadIdCardBackImage();
		} else if (currentImageType.value === 'handheld') {
			handheldIdCardUrl.value = ''; // 清空图片地址
			isHandheldIdCardUploaded.value = false; // 重置上传状态
			handheldIdCardUploadFailed.value = false; // 重置失败状态
			uploadHandheldIdCardImage();
		}
		closePopup();
	};

	// 上传结算卡正面按钮
	const uploadFrontImage = async () => {
		uni.chooseImage({
			sourceType: ['album', 'camera'], // 从相册选择
			count: 1,
			success: (chooseImageRes) => {
				const tempFilePaths = chooseImageRes.tempFilePaths;
				uni.uploadFile({
					url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						type: 'bankCardFrontPic',
						id: ''
					},
					header: {
						token: userStore.token
					},
					success: (res) => {
						const data = JSON.parse(res.data);
						if (data.code !== 200) {
							uni.showToast({
								title: data.msg,
								icon: 'none'
							});
							frontImageUploadFailed.value = true; // 标记上传失败
							isFrontImageUploaded.value = false;
							frontImageUrl.value = '';
							form.settlement_card_number = '';
							return;
						}
						frontImageUrl.value = data.path;
						form.settlement_card_number = data.data.card_number;
						// form.settlement_bank = data.data.bank_name;
						isFrontImageUploaded.value = true; // 标记上传成功
						frontImageUploadFailed.value = false; // 重置上传失败状态
					}
				});
			}
		});
	};

	// 上传结算卡反面按钮
	const uploadBackImage = async () => {
		uni.chooseImage({
			sourceType: ['album', 'camera'], // 从相册选择
			count: 1,
			success: (chooseImageRes) => {
				const tempFilePaths = chooseImageRes.tempFilePaths;
				uni.uploadFile({
					url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						type: '',
						id: ''
					},
					header: {
						token: userStore.token
					},
					success: (res) => {
						const data = JSON.parse(res.data);
						if (data.code !== 200) {
							uni.showToast({
								title: data.msg,
								icon: 'none'
							});
							backImageUploadFailed.value = true; // 标记上传失败
							return;
						}
						backImageUrl.value = data.path;
						isBackImageUploaded.value = true; // 标记上传成功
						backImageUploadFailed.value = false; // 重置上传失败状态
					}
				});
			}
		});
	};

	// 上传授权结算书按钮
	const uploadAuthorizationImage = async () => {
		uni.chooseImage({
			sourceType: ['album', 'camera'], // 从相册选择
			count: 1,
			success: (chooseImageRes) => {
				const tempFilePaths = chooseImageRes.tempFilePaths;
				uni.uploadFile({
					url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						type: '',
						id: ''
					},
					header: {
						token: userStore.token
					},
					success: (res) => {
						const data = JSON.parse(res.data);
						if (data.code !== 200) {
							uni.showToast({
								title: data.msg,
								icon: 'none'
							});
							authorizationImageUploadFailed.value = true; // 标记上传失败
							return;
						}
						authorizationImageUrl.value = data.path;
						isAuthorizationImageUploaded.value = true; // 标记上传成功
						authorizationImageUploadFailed.value = false; // 重置上传失败状态
					}
				});
			}
		});
	};

	// 上传开户许可证按钮
	const uploadmerchantImgPermitImage = async () => {
		uni.chooseImage({
			sourceType: ['album', 'camera'], // 从相册选择
			count: 1,
			success: (chooseImageRes) => {
				const tempFilePaths = chooseImageRes.tempFilePaths;
				uni.uploadFile({
					url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						type: '',
						id: ''
					},
					header: {
						token: userStore.token
					},
					success: (res) => {
						const data = JSON.parse(res.data);
						if (data.code !== 200) {
							uni.showToast({
								title: data.msg,
								icon: 'none'
							});
							merchantImgPermitImageUploadFailed.value = true; // 标记上传失败
							return;
						}
						merchantImgPermitImageUrl.value = data.path;
						********************************.value = true; // 标记上传成功
						merchantImgPermitImageUploadFailed.value = false; // 重置上传失败状态
					}
				});
			}
		});
	};

	// 上传身份证正面图片函数
	const uploadIdCardFrontImage = async () => {
		uni.chooseImage({
			sourceType: ['album', 'camera'],
			count: 1,
			success: (chooseImageRes) => {
				const tempFilePaths = chooseImageRes.tempFilePaths;
				uni.uploadFile({
					url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						type: 'idcardFrontPic',
						id: merchant_id.value
					},
					header: {
						token: userStore.token
					},
					success: (res) => {
						const data = JSON.parse(res.data);
						if (data.code !== 200) {
							uni.showToast({
								title: data.msg,
								icon: 'none'
							});
							idCardFrontUploadFailed.value = true;
							return;
						}
						idCardFrontUrl.value = data.path;
						picDataFront.value = data.data;
						isIdCardFrontUploaded.value = true;
						idCardFrontUploadFailed.value = false;
						if (picDataBack.value !== '' && picDataFront.value !== '') {
							showAlert.value = true;
						}
					}
				});
			}
		});
	};

	// 上传身份证反面图片函数
	const uploadIdCardBackImage = async () => {
		uni.chooseImage({
			sourceType: ['album', 'camera'],
			count: 1,
			success: (chooseImageRes) => {
				const tempFilePaths = chooseImageRes.tempFilePaths;
				uni.uploadFile({
					url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						type: 'idcardBackPic',
						id: merchant_id.value
					},
					header: {
						token: userStore.token
					},
					success: (res) => {
						const data = JSON.parse(res.data);
						if (data.code !== 200) {
							uni.showToast({
								title: data.msg,
								icon: 'none'
							});
							idCardBackUploadFailed.value = true;
							return;
						}
						idCardBackUrl.value = data.path;
						picDataBack.value = data.data;
						isIdCardBackUploaded.value = true;
						idCardBackUploadFailed.value = false;
						if (picDataBack.value !== '' && picDataFront.value !== '') {
							showAlert.value = true;
						}
					}
				});
			}
		});
	};

	// 上传手持身份证图片函数
	const uploadHandheldIdCardImage = async () => {
		uni.chooseImage({
			sourceType: ['album', 'camera'],
			count: 1,
			success: (chooseImageRes) => {
				const tempFilePaths = chooseImageRes.tempFilePaths;
				uni.uploadFile({
					url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						type: '',
						id: ''
					},
					header: {
						token: userStore.token
					},
					success: (res) => {
						const data = JSON.parse(res.data);
						if (data.code !== 200) {
							uni.showToast({
								title: data.msg,
								icon: 'none'
							});
							handheldIdCardUploadFailed.value = true;
							return;
						}
						handheldIdCardUrl.value = data.path;
						isHandheldIdCardUploaded.value = true;
						handheldIdCardUploadFailed.value = false;
					}
				});
			}
		});
	};

	// 打开绑定付款码弹窗
	const openBindingPopup = () => {
		isBindingPopupVisible.value = true;
	};

	const submitIfNotEmpty = () => {
		if (payCodeId.value) {
			displayedPayCodeId.value = payCodeId.value; // 将输入的值赋给展示的变量
			closeBindingPopup(); // 关闭弹窗
		}
	};

	// 关闭绑定付款码弹窗
	const closeBindingPopup = () => {
		isBindingPopupVisible.value = false;
		payCodeId.value = ''; // 清空输入框
	};

	/** 获取扫描的二维码 */
	let code = ref(null);
	/** 扫码绑定付款码 */
	function scanHandel() {
		/*#ifdef APP-PLUS*/
		uni.scanCode({
			success: async function(res) {
				code.value = res.result + '&m=1';
				await requestAndSetCode(code.value);
			}
		});
		/*#endif*/

		/*#ifdef H5*/
		scanCode();
		/*#endif*/
	}
	/** h5的扫码 */
	function scanCode() {
		// 调用uni提供的调用相机api
		uni.chooseImage({
			sizeType: ['original'],
			sourceType: ['camera'],
			count: 1,
			success: function(res) {
				const tempFilePaths = res.tempFilePaths[0]; // 获取到二维码图片的链接
				qrcode.decode(tempFilePaths); // 解析二维码图片
				qrcode.callback = async function(res1) {
					// 解析失败返回 error decoding QR Code
					if (res1 == 'error decoding QR Code') {
						uni.utils.toast('识别二维码失败，请重新上传');
					} else {
						console.log(res1);
						// 解析成功返回二维码链接
						code.value = res1 + '&m=1';
						await requestAndSetCode(code.value);
					}
				};
			}
		});
	}
	/** 请求接口 */
	function requestAndSetCode(url) {
		uni.request({
			url: url,
			header: {
				token: userStore.token || ''
			},
			success: (res) => {
				console.log(res);
				if (res.data.code == 200) {
					// 更新 payCodeId 和 displayedPayCodeId，并清空输入框
					payCodeId.value = res.data.data.code_data;
				} else {
					uni.utils.toast(res.data.msg);
				}
			}
		});
	}

	// 上一步按钮函数
	const back = async () => {
		const data = {
			merchant_id: uni.getStorageSync('merchant_id') || '',
			codes: displayedPayCodeId.value,
			not_submitted: aloneChecked.value ? 1 : 0,
			settlement_bank: currentGangway.value === 'changsha_bank' ? '长沙银行' : form.settlement_bank,
			settlement_open_subbank: form.settlement_open_subbank,
			settlement_account_name: form.settlement_account_name,
			settlement_bank_code: form.settlement_bank_code,
			settlement_card_number: form.settlement_card_number,
			settlement_bank_tel: form.settlement_bank_tel,
			settlement_account_type: selectedName.value === '对私法人账户' ? '01' : selectedName.value === '对私非法人账户' ? '02' :
				'00',
			merchant_img_authorization_statement: selectedName.value === '对私非法人账户' ? authorizationImageUrl.value : '',
			merchant_img_permit_for_bank_account: selectedName.value === '对公账户' ? merchantImgPermitImageUrl.value : '',
			merchant_img_bank_card: frontImageUrl.value,
			merchant_img_bank_card_back: backImageUrl.value,
			merchant_img_principal_id_card_front: idCardFrontUrl.value,
			merchant_img_principal_id_card_obverse: idCardBackUrl.value,
			temp_save: 1,
			merchant_img_holding_photo: handheldIdCardUrl.value, // 添加手持身份证图片URL
			s_legal_name: picDataFront.value.name, // 结算人姓名
			s_legal_id: picDataFront.value.number, // 结算人身份证号
			s_legal_start_dt: picDataBack.value.valid_from, // 结算人身份证生效日期
			s_legal_exp_dt: picDataBack.value.valid_to, // 结算人身份证有效期
			longitude: uni.getStorageSync('longitude') || '', // 获取经度，如果没有则为空
			latitude: uni.getStorageSync('latitude') || '' // 获取纬度，如果没有则为空
		};
		await submitDebitCard(data);
		uni.redirectTo({
			url: '/pages/shopScene/shopScene'
		});
	};

	// 计算属性：检查所有上传状态和表单状态
	const allUploaded = computed(() => {
		// 判断是否为长沙银行通道，决定是否校验开户行
		const shouldValidateBank = currentGangway.value !== 'changsha_bank';

		// 暂不提交结算卡，仅验证其他部分
		if (aloneChecked.value) {
			if (selectedName.value === '对私非法人账户') {
				return (
					authorizationImageUrl.value &&
					idCardFrontUrl.value &&
					idCardBackUrl.value &&
					handheldIdCardUrl.value &&
					picDataFront.value.name &&
					picDataFront.value.number &&
					picDataBack.value.valid_from &&
					validateDate(picDataBack.value.valid_to) &&
					new Date(picDataBack.value.valid_to) > new Date()
				);
			}
			if (selectedName.value === '对公账户') {
				return merchantImgPermitImageUrl.value;
			}
			return true;
		}

		// 完整验证逻辑
		if (selectedName.value === '对私法人账户') {
			return (
				frontImageUrl.value &&
				form.settlement_account_name &&
				form.settlement_card_number &&
				(shouldValidateBank ? form.settlement_bank : true) && // 判断是否需要校验开户行
				form.settlement_open_subbank &&
				form.settlement_bank_tel
			);
		}

		if (selectedName.value === '对私非法人账户') {
			return (
				frontImageUrl.value &&
				authorizationImageUrl.value &&
				idCardFrontUrl.value &&
				idCardBackUrl.value &&
				handheldIdCardUrl.value &&
				form.settlement_account_name &&
				form.settlement_card_number &&
				(shouldValidateBank ? form.settlement_bank : true) && // 判断是否需要校验开户行
				form.settlement_open_subbank &&
				form.settlement_bank_tel &&
				picDataFront.value.name &&
				picDataFront.value.number &&
				picDataBack.value.valid_from &&
				validateDate(picDataBack.value.valid_to) &&
				new Date(picDataBack.value.valid_to) > new Date()
			);
		}

		if (selectedName.value === '对公账户') {
			return (
				merchantImgPermitImageUrl.value &&
				form.settlement_account_name &&
				form.settlement_card_number &&
				(shouldValidateBank ? form.settlement_bank : true) && // 判断是否需要校验开户行
				form.settlement_open_subbank &&
				form.settlement_bank_tel
			);
		}

		return false;
	});

	// 提交按钮函数
	const submit = async () => {
		console.log('displayedPayCodeId:', displayedPayCodeId.value);
		console.log('payCodeId:', payCodeId.value);
		// 校验结算类型是否已选择
		if (!selectedType.value && selectedType.value !== 0) {
			uni.showToast({
				title: '请选择结算类型',
				icon: 'none'
			});
			return;
		}
		if (selectedName.value === '对私法人账户') {
			// 对私法人账户
			if (!aloneChecked.value && !frontImageUrl.value) {
				uni.showToast({
					title: '请上传结算卡正面图片',
					icon: 'none'
				});
				return;
			}
		} else if (selectedName.value === '对私非法人账户') {
			// 对私非法人账户
			if (!authorizationImageUrl.value || !idCardFrontUrl.value || !idCardBackUrl.value || !handheldIdCardUrl
				.value) {
				uni.showToast({
					title: '请上传所有必需的授权书和身份证图片',
					icon: 'none'
				});
				return;
			}
			if (!picDataFront.value.name || !picDataFront.value.number || !picDataBack.value.valid_from || !picDataBack
				.value.valid_to) {
				uni.showToast({
					title: '请填写完整的结算人信息',
					icon: 'none'
				});
				return;
			}
			// 校验身份证号逻辑
			const isValid = verifyCardId(picDataFront.value.number);
			if (!isValid) return uni.utils.toast('请输入正确的身份证号');
			if (!validateDate(picDataBack.value.valid_to) || new Date(picDataBack.value.valid_to) <= new Date()) {
				uni.showToast({
					title: '结算人身份证有效期格式不正确，请按照xxxx-xx-xx或xxxx年xx月xx日格式填写，并且有效期不能小于当前日期',
					icon: 'none'
				});
				return;
			}
		} else if (selectedName.value === '对公账户') {
			const shouldValidateBank = currentGangway.value !== 'changsha_bank';
			// 对公账户
			if (!merchantImgPermitImageUrl.value) {
				uni.showToast({
					title: '请上传必需的开户许可证图片',
					icon: 'none'
				});
				return;
			}
			if (
				!form.settlement_account_name ||
				!form.settlement_card_number ||
				(shouldValidateBank && !form.settlement_bank) ||
				!form.settlement_open_subbank ||
				!form.settlement_bank_tel
			) {
				uni.showToast({
					title: '请填写完整的账户信息',
					icon: 'none'
				});
				return;
			}
		}

		// 校验账户信息
		if (!aloneChecked.value && (selectedName.value === '对私法人账户' || selectedName.value === '对公账户')) {
			const shouldValidateBank = currentGangway.value !== 'changsha_bank';
			if (
				!form.settlement_account_name ||
				!form.settlement_card_number ||
				(shouldValidateBank && !form.settlement_bank) || // 判断是否需要校验开户行
				!form.settlement_open_subbank ||
				!form.settlement_bank_tel
			) {
				uni.showToast({
					title: '请填写完整的账户信息',
					icon: 'none'
				});
				return;
			}
		}

		const data = {
			merchant_id: uni.getStorageSync('merchant_id') || '',
			codes: displayedPayCodeId.value,
			not_submitted: aloneChecked.value ? 1 : 0,
			settlement_bank: currentGangway.value === 'changsha_bank' ? '长沙银行' : form.settlement_bank,
			settlement_open_subbank: form.settlement_open_subbank,
			settlement_account_name: form.settlement_account_name,
			settlement_bank_code: form.settlement_bank_code,
			settlement_card_number: form.settlement_card_number,
			settlement_bank_tel: form.settlement_bank_tel,
			settlement_account_type: selectedName.value === '对私法人账户' ? '01' : selectedName.value === '对私非法人账户' ? '02' :
				'00',
			merchant_img_authorization_statement: selectedName.value === '对私非法人账户' ? authorizationImageUrl.value : '',
			merchant_img_permit_for_bank_account: selectedName.value === '对公账户' ? merchantImgPermitImageUrl.value : '',
			merchant_img_bank_card: frontImageUrl.value,
			merchant_img_bank_card_back: backImageUrl.value,
			merchant_img_principal_id_card_front: idCardFrontUrl.value,
			merchant_img_principal_id_card_obverse: idCardBackUrl.value,
			longitude: uni.getStorageSync('longitude') || '', // 获取经度，如果没有则为空
			latitude: uni.getStorageSync('latitude') || '', // 获取纬度，如果没有则为空
			temp_save: 0,
			merchant_img_holding_photo: handheldIdCardUrl.value, // 添加手持身份证图片URL
			s_legal_name: picDataFront.value.name, // 结算人姓名
			s_legal_id: picDataFront.value.number, // 结算人身份证号
			s_legal_start_dt: picDataBack.value.valid_from, // 结算人身份证生效日期
			s_legal_exp_dt: picDataBack.value.valid_to // 结算人身份证有效期
		};

		await submitDebitCard(data).then((res) => {
			if (res.code === 200) {
				// 将 save_page 存储为 5
				uni.setStorageSync('save_page', 5);
				uni.showToast({
					title: res.msg,
					icon: 'none'
				});
				// 清空本地存储中的开户行信息
				uni.removeStorageSync('selectedBank');
				uni.reLaunch({
					url: '/pages/accomplish/accomplish'
				});
			} else {
				uni.showToast({
					title: res.msg,
					icon: 'none'
				});
			}
		});
	};
</script>

<style lang="scss" scoped>
	.longPress {
		::v-deep .u-popup__content {
			width: 100%;
			height: 440rpx;
			box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
			border-radius: 24rpx;
		}
	}

	::v-deep .u-popup__content {
		width: 615rpx;
		height: 464rpx;
		box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
		border-radius: 24rpx;
	}

	::v-deep .uni-input-input {
		// margin-left: 64rpx;
	}

	::v-deep .uni-input-placeholder {
		// margin-left: 64rpx;
		font-size: 28rpx;
		font-weight: 400;
		color: #9da5ad !important;
	}

	.content {
		padding-top: 164rpx;
		width: 100%;
		height: calc(100% - 500rpx);

		.steps {
			width: 100%;
			height: 164rpx;
			background-color: #ffffff;
		}

		.schemeSelection {
			padding: 0 32rpx;
			margin-top: 56rpx;

			.title {
				font-weight: 500;
				font-size: 34rpx;
				color: #18191a;
				margin-bottom: 8rpx;

				.required {
					color: red;
				}
			}

			.elect {
				width: 100%;
				height: 176rpx;
				border-radius: 8rpx;
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				align-items: center;

				.elect .disabled {
					background-color: #f0f0f0;
					color: #9da5ad;
					pointer-events: none;
					/* 禁用点击 */
				}

				.list {
					width: 336rpx;
					height: 80rpx;
					background: #ffffff;
					border-radius: 8rpx;
					margin-top: 16rpx;
					text-align: center;
					line-height: 80rpx;
					position: relative;

					.text {
						font-weight: 500;
						font-size: 28rpx;
						color: #575e65;
					}

					&.active {
						background: #e5f2ff;
						border: 1rpx solid #1386fb;

						.text {
							color: #1386fb;
						}

						.icon {
							display: block;
						}
					}

					.icon {
						position: absolute;
						right: 8rpx;
						bottom: 8rpx;
						width: 48rpx;
						height: 48rpx;
						display: none;
					}
				}
			}

			.hint {
				font-size: 24rpx;
				color: #9da5ad;
			}
		}

		.uploading {
			width: 92%;
			height: 528rpx;
			background-color: #ffffff;
			margin-left: 32rpx;
			margin-top: 32rpx;
			box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
			border: 1rpx solid #edf0f5;
			border-radius: 24rpx;

			.uploadingMessage {
				padding: 0 32rpx;
				margin-top: 40rpx;

				.title {
					font-weight: 500;
					font-size: 30rpx;
					color: #18191a;
				}

				.text {
					font-weight: 400;
					font-size: 24rpx;
					color: #9da5ad;
					margin-top: 8rpx;
				}
			}

			.uploadingImages {
				display: flex;
				justify-content: space-between;
				margin-top: 24rpx;
				padding: 0 32rpx;

				.uploadBox {
					width: 304rpx;

					.charter {
						width: 100%;
						height: 256rpx;
						background-color: #f9fafb;
					}

					.btnBox {
						width: 100%;
						height: 64rpx;
						background-color: #c1dffe;
						border-radius: 0rpx 0rpx 8rpx 8rpx;

						&.uploaded {
							background-color: #aadb9a;

							/* 背景颜色 */
							.btn {
								color: #198703;
								/* 文字颜色改变 */
							}
						}

						&.failed {
							background-color: #e6b9b5;

							.btn {
								color: #b91810;
							}
						}

						.btn {
							line-height: 64rpx;
							text-align: center;
							font-size: 26rpx;
							color: #0056ad;
						}
					}
				}
			}
		}

		.require {
			margin-top: 56rpx;
			padding: 0 32rpx;

			.title {
				font-weight: 500;
				font-size: 30rpx;
				color: #18191a;
			}

			.imgList {
				display: flex;
				justify-content: space-between;
				margin-top: 16rpx;
			}

			.text {
				display: flex;
				justify-content: center;

				.word {
					font-size: 24rpx;
					font-weight: 400;
					color: #575e65;
				}
			}
		}

		.messageBox {
			// height: 556rpx;
			background: #fff;
			box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
			border-radius: 8rpx;
			border: 1rpx solid #edf0f5;
			margin-bottom: 64rpx;
			margin: 0 32rpx;
			margin-top: 56rpx;

			.message {
				padding: 40rpx 32rpx;
			}

			.form {
				margin-top: 16rpx;

				.text {
					font-weight: 400;
					font-size: 28rpx;
					color: #575e65;

					.required {
						color: red;
					}
				}
			}
		}

		.binding {
			width: 100%;
			box-sizing: border-box;
			height: 180rpx;
			padding: 0 32rpx;
			margin-top: 56rpx;

			.title {
				font-weight: 500;
				font-size: 34rpx;
				color: #18191a;
			}

			.bindingBtn {
				margin-top: 24rpx;
				height: 108rpx;
				background: #ffffff;
				box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
				border-radius: 16rpx;
				border: 1rpx solid #edf0f5;
				display: flex;
				justify-content: space-between;
				line-height: 108rpx;
				padding-left: 32rpx;
				padding-right: 32rpx;

				.text {
					font-weight: 400;
					font-size: 30rpx;
					color: #575e65;
				}

				.btn {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 24rpx;
					color: #1386fb;
				}
			}
		}

		.bindingPoper {
			.title {
				margin-top: 40rpx;
				font-weight: 600;
				font-size: 34rpx;
				color: #575e65;
				line-height: 48rpx;
				text-align: center;
			}

			.hint {
				margin: 0 auto;
				margin-top: 16rpx;
				width: 262rpx;
				height: 80rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: #9da5ad;
				line-height: 40rpx;
				text-align: center;
			}

			.bindingPoperBtn {
				width: 554rpx;
				height: 88rpx;
				background: #7abcff;
				border-radius: 8rpx;
				margin-top: 32rpx;
				text-align: center;

				.txt {
					font-weight: 600;
					font-size: 26rpx;
					color: #ffffff;
					line-height: 40rpx;
					text-align: center;
					line-height: 88rpx;
				}
			}

			::v-deep .u-input {
				margin-top: 16rpx;
				width: 518rpx !important;
				height: 76rpx !important;
				background: #f0f2f5 !important;
				border-radius: 8rpx !important;
				border: none;
			}
		}
	}

	.bottomBox {
		position: fixed;
		box-shadow: 0rpx -2rpx 4rpx 0rpx rgba(75, 80, 85, 0.06);
		background: #ffffff;
		margin-top: 58rpx;
		width: 100%;
		height: 172rpx;
		bottom: 0;
		left: 0;
		padding: 0 32rpx;

		.box {
			display: flex;

			.backBox {
				width: 218rpx;
				height: 88rpx;
				border: 2rpx solid #1b7de1;
				border-radius: 8rpx;
				margin-top: 24rpx;
				margin-right: 16rpx;

				.text {
					line-height: 88rpx;
					text-align: center;
					color: #1386fb;
					font-weight: 600;
					font-size: 26rpx;
				}
			}

			.nextBox {
				width: 452rpx;
				height: 94rpx;
				background: #dee2e8;
				border-radius: 8rpx;
				margin-top: 24rpx;

				&.active {
					background: #1386fb;

					.text {
						color: #ffffff;
					}
				}

				.text {
					line-height: 94rpx;
					text-align: center;
					color: #9da5ad;
					font-size: 26rpx;
					font-weight: 500;
				}
			}
		}
	}
</style>