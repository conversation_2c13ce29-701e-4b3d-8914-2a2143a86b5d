<template>
	<view class="container-item">
		<view class="flex-sty">
			<view class="txt1" v-if="props.item.to_do_items_type==1">完善资料</view>
			<view class="txt1" v-if="props.item.to_do_items_type==2">提交结算卡</view>
			<view class="txt1" v-if="props.item.to_do_items_type==3">绑定设备</view>
			<view class="txt4" v-if="props.item.to_do_items_type==4">审核驳回</view>
			<view class="txt1" v-if="props.item.to_do_items_type==5">商户签约</view>
			<view class="txt1" v-if="props.item.to_do_items_type==6">确认资料</view>
			<view class="txt2">创建时间: {{props.item.create_time}}</view>
		</view>
		<view class="flex-sty2" style="margin-top: 20rpx;">
			<view class="" style="width: 112rpx;">
				<view class="txt3">商户名称:</view>
				<view class="txt3">联系人:</view>
				<view class="txt3">联系电话:</view>
				<view class="txt3">联系地址:</view>
			</view>
			<view class="" style="width: calc(100% - 112rpx)">
				<view class="flex-sty3" style="width: 100%;">
					<view class="" style="width: calc(100% - 142rpx)">
						<view class="txt3">{{props.item.bse_lice_nm ? props.item.bse_lice_nm : props.item.store_name}}</view>
						<view class="txt3">{{props.item.store_contacts ? props.item.store_contacts : props.item.s_legal_name}}</view>
					</view>
					<view class="btn1" v-if="props.item.to_do_items_type==4" @click="goViewHandel">查看</view>
					<view class="btn1" v-else @click="goHandel">去完成</view>
				</view>
				<view class="txt3">{{props.item.s_legal_phone}}</view>
				<view class="txt3">
					<view class="txt3">{{props.item.merchant_address}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, reactive, computed } from 'vue';
	import { areaList } from '@/utils/wangpu_address.js'

	const emit = defineEmits(['goComplete','viewHandel'])
	
	const props = defineProps({
		item: {
			type: Object,
			default: {}
		}
	})
	
	// 根据地址码和地址列表获取文字地址
	function getAddressText(addressCode:any, areas:any) {
	  for (let area of areas) {
	    if (area.value === addressCode) {
	      return area.text;
	    }
	    if (area.children) {
	      const text = getAddressText(addressCode, area.children);
	      if (text) {
	        return text;
	      }
	    }
	  }
	  return null;
	}
	
	// 根据地址字符串获取文字地址
	function getAddressFromCodeString(addressString:any) {
	  const addressCodes = addressString.split(',');
	  let textAddress = addressCodes.map(code => getAddressText(code, areaList));
	  return textAddress.join('');
	}
	
	/** 去完成 */
	function goHandel(){
		emit('goComplete',props.item)
	}
	
	/** 查看 */
	function goViewHandel(){
		emit('viewHandel',props.item)
	}
	
</script>

<style lang="scss" scoped>
	.flex-sty {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.flex-sty1 {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.flex-sty2 {
		display: flex;
		align-items: center;
	}
	
	.flex-sty3 {
		display: flex;
		justify-content: space-between;
	}

	.container-item {
		width: 100%;
		height: 304rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
		border-radius: 16rpx;
		border: 1rpx solid #EDF0F5;
		margin-bottom: 24rpx;
		box-sizing: border-box;
		padding: 32rpx;

		.txt1 {
			font-weight: 600;
			font-size: 32rpx;
			color: #1B7DE1;
			font-style: normal;
		}

		.txt2 {
			font-weight: 400;
			font-size: 24rpx;
			color: $uni-secondary-color;
			font-style: normal;
		}

		.txt3 {
			width: 100%;
			font-weight: 400;
			font-size: 24rpx;
			color: $uni-base-color;
			height: 44rpx;
			// line-height: 40rpx;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			font-style: normal;
		}
		
		.txt4{
			font-weight: 600;
			font-size: 32rpx;
			color: #F39237;
			font-style: normal;
		}

		.btn1 {
			width: 142rpx;
			height: 64rpx;
			line-height: 64rpx;
			background: #E5F2FF;
			border-radius: 4rpx;
			text-align: center;
			font-weight: 600;
			font-size: 26rpx;
			color: $uni-primary;
			font-style: normal;
		}
	}
</style>