/**
 * 将金额格式化为千分位显示
 * @param amount 待格式化的金额
 * @returns 格式化后的金额字符串
 */

// 金额显示千分位隔开显示 保留两位小数，不去除多余的0 不缩写直接显示  如：123,456.00  123.20  123.00
const money = (amount : number|string) => {
	let amountNum = Number(amount).toFixed(2)

	if (amountNum.indexOf('.') !== -1) {  
		const parts = amountNum.split('.');
		const integerPart = parts[0];
		const decimalPart = parts[1];
		amountNum = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '.' + decimalPart;
	} else {
		amountNum = amountNum.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
	}

	return amountNum;
};

// 金额显示千分位隔开显示，去除多余的0，大于100000000显示1亿，大于100000显示10万，32,123.45 123,456.01 23,456.2
const money1 = (amount : number|string) => {
	let amountNum = Number(amount).toFixed(2)
	if (amountNum >= 100000000) {
		// 大于等于100000000的金额转换成"亿"，保留最多两位小数，并添加千分位
		const inWan = (parseInt((amountNum / 100000000)*100)/100).toString();
		const parts = inWan.split('.');
		const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ','); // 添加千分位分隔符
		const decimalPart = parts[1] !== '00' ? '.' + parts[1].replace(/0+$/, '') : ''; // 移除多余的0
		return integerPart + decimalPart  + '亿'; // 返回最终结果
	} else if (amountNum >= 100000) {
		// 大于等于100000的金额转换成"万"，保留最多两位小数，并添加千分位
		const inWan = (parseInt((amountNum / 10000)*100)/100).toString();
		const parts = inWan.split('.');
		const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ','); // 添加千分位分隔符
		const decimalPart = parts[1] !== '00' ? '.' + parts[1].replace(/0+$/, '') : ''; // 移除多余的0
		return integerPart + decimalPart  + '万'; // 返回最终结果
	} else {
		// 小于100000的金额保留千分位并保留最多两位小数
		const parts = amountNum.split('.');
		const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ','); // 添加千分位分隔符
		const decimalPart = parts[1] !== '00' ? '.' + parts[1].replace(/0+$/, '') : ''; // 移除多余的0
		return integerPart + decimalPart; // 返回最终结果
	}
}

// 金额显示千分位隔开显示 去除多余的0 不缩写直接显示 如：123,456  12,123,456.2  5,123,456.23
const money2 = (amount : number|string) => {
	const amountNum = Number(amount).toFixed(2); // 保留两位小数
	const parts = amountNum.split('.');
	const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ','); // 添加千分位分隔符
	const decimalPart = parts[1] !== '00' ? '.' + parts[1].replace(/0+$/, '') : ''; // 移除多余的0

	return integerPart + decimalPart; // 返回最终结果
};

// 数量显示千分位隔开显示 不缩写直接显示 如：123,456 123,456,789
const number = (amount : number|string) => {
	let amountStr = amount.toString()

	// 添加千分位分隔符
	if (amountStr.indexOf('.') !== -1) {
		const parts = amountStr.split('.');
		const integerPart = parts[0];
		const decimalPart = parts[1];
		amountStr = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '.' + decimalPart;
	} else {
		amountStr = amountStr.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
	}

	return amountStr;
};

// 数量显示千分位隔开金额显示千分位隔开显示，去除多余的0，大于100000000显示1亿，大于100000显示10万，23,456 13,456
const number1 = (amount : number|string) => {
	let amountNum = Number(amount)
	if(amountNum >= 100000000){
		// 大于等于100000000的金额转换成"亿"，并添加千分位
		let amountStr = (parseInt((amountNum / 100000000)*100)/100).toString();
		amountStr = amountStr.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
		return amountStr + '亿';
		
	}else if (amountNum >= 100000) {
		// 大于等于100000的金额转换成"万"，并添加千分位
		let amountStr = (parseInt((amountNum / 10000)*100)/100).toString();
		amountStr = amountStr.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
		return amountStr + '万';
	} else {
		// 对小于1000000的金额进行千分位格式化
		let amountStr = amount.toString();
		if (amountStr.indexOf('.') !== -1) {
			const parts = amountStr.split('.');
			const integerPart = parts[0];
			const decimalPart = parts[1];
			amountStr = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '.' + decimalPart;
		} else {
			amountStr = amountStr.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
		}
		return amountStr;
	}
};

//默认导出
export default {
	money,
	money1,
	money2,
	number,
	number1
}