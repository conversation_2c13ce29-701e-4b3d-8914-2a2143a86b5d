// #ifndef VUE3
import Vue from 'vue';
import App from './App';
import mitt from 'mitt';

Vue.config.productionTip = false;

App.mpType = 'app';

const app = new Vue({
  ...App
});
app.$mount();
// #endif

// #ifdef VUE3
import App from './App.vue';
import uviewPlus from '@/uni_modules/uview-plus';
import {
  createSSRApp
} from 'vue';
import {
  createPinia
} from 'pinia';
//2.导入持久化工具
import {
  piniaPluginPersistedstate
} from '@/store/persist'; //配置默认的选项
import '@/utils/utils';
import '@/common/request';


export function createApp() {
  const app = createSSRApp(App);
  // app.config.globalProperties.mittBus = mitt();
  const pinia = createPinia();
  //pinia挂载持久化插件
  pinia.use(piniaPluginPersistedstate);
  app.use(uviewPlus);
  app.use(pinia);
  return {
    app
  };
}
// #endif