<template>
	<view class="record-card">
		<!-- 年份标题 -->
		<view class="year-header">
			<text class="year" @click="isExpand = !isExpand">{{props.item.year}}年</text>
			<up-icon v-if="isExpand" name="arrow-down-fill" color="#18191A" size="12" @click="isExpand = !isExpand"></up-icon>
			<up-icon v-else name="arrow-up-fill" color="#1386FB" size="12" @click="isExpand = !isExpand"></up-icon>
		</view>

		<!-- 交易统计 -->
		<view class="stats-section">
			<view class="stats-content">
				<!-- 总交易额 -->
				<view class="amount-block">
					<text class="label">总交易额</text>
					<view class="amount">
						<text class="currency">¥</text>
						<text class="number">{{format.money1(props.item.order_amount)}}</text>
					</view>
				</view>

				<!-- 交易笔数 -->
				<view class="count-block">
					<text class="label">交易笔数(笔)</text>
					<text class="number">{{format.number1(props.item.order_count)}}</text>
				</view>
			</view>
			<image class="stats-img" src="@/static/images/business-trade.png" mode=""></image>
		</view>

		<!-- 月度数据 -->
		<view class="" v-if="isExpand && item.month_data.length>0">
			<view class="month-data" v-for="(month,index) in item.month_data" :key="month">
				<view class="month-data-item">
					<view class="month">{{month.month}}月</view>
					<view class="amount">¥{{format.money1(month.order_amount)}}</view>
					<view class="count">{{format.number1(month.order_count)}}笔</view>
				</view>
				<view class="time-info">
					<view class="label">统计时间:</view>
					<view class="label time">{{month.day}}</view>
				</view>
				<view class="month-data-line" v-if="index != item.month_data.length - 1"></view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref } from 'vue'
	import format from '@/utils/format.ts'
	
	const props = defineProps({
		item: {
			type: Object,
			default: {}
		}
	})
	
	const isExpand = ref(true) //是否展开
</script>

<style lang="scss" scoped>

.record-card {
	margin-top: 24rpx;
	min-height: 276rpx;
	background: #FFFFFF;
	border-radius: 16rpx;
	border: 1rpx solid #EDF0F5;
	box-sizing: border-box;
	padding: 32rpx 24rpx;

	.year-header {
		display: flex;
		align-items: center;

		.year {
			margin-left: 8rpx;
			margin-right: 4rpx;
			font-size: 30rpx;
			color: #18191A;
			line-height: 44rpx;
			font-weight: 600;
		}
	}

	.stats-section {
		position: relative;
		margin-top: 16rpx;
		width: 100%;
		height: 152rpx;
		background: linear-gradient( 270deg, #F7FBFF 0%, #E5F2FF 100%);
		border-radius: 12rpx;
		
		.stats-img{
			position: absolute;
			right: 24rpx;
			bottom: 0rpx;
			width: 186rpx;
			height: 126rpx;
			z-index: 1;
		}

		.stats-content {
			position: absolute;
			top: 0;
			left: 0rpx;
			z-index: 2;
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: space-between;
			box-sizing: border-box;
			padding: 24rpx;

			.amount-block {
				width: 55%;
				
				.label {
					font-size: 28rpx;
					color: #1386FB;
					line-height: 40rpx;
				}

				.amount {
					width: 100%;
					margin-top: 16rpx;
					font-size: 34rpx;
					color: #006AD7;
					font-weight: 600;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;

					.currency {
						font-size: 30rpx;
						color: #006AD7;
						line-height: 40rpx;
						margin-right: 4rpx;
					}

					.number {
						font-size: 34rpx;
						color: #006AD7;
						line-height: 48rpx;
						letter-spacing: 0.5px;
						font-weight: 600;
					}
				}
			}

			.count-block {
				width: 35%;
				.label {
					font-size: 28rpx;
					color: #1386FB;
					line-height: 40rpx;
				}

				.number {
					display: block;
					margin-top: 16rpx;
					font-size: 34rpx;
					color: #006AD7;
					line-height: 48rpx;
					letter-spacing: 0.5px;
					font-weight: 600;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
		}
	}

	.month-data {
		margin-top: 32rpx;
		
		.month-data-item{
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
		}

		.month {
			width: 30%;
			font-weight: 500;
			font-size: 30rpx;
			color: #18191A;
			line-height: 40rpx;
			margin-left: 8rpx;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}

		.amount {
			width: 35%;
			font-weight: 500;
			font-size: 30rpx;
			color: #F39237;
			line-height: 40rpx;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}

		.count {
			width: 35%;
			font-weight: 500;
			font-size: 30rpx;
			color: #575E65;
			line-height: 40rpx;
			text-align: right;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
		
		.month-data-line {
			margin-top: 24rpx;
			width: 100%;
			height: 1rpx;
			background: #DEE2E8;
		}
	}

	.time-info {
		margin-top: 8rpx;
		margin-left: 8rpx;
		display: flex;
		align-items: center;

		.label {
			font-size: 26rpx;
			color: #979B9F;
			line-height: 40rpx;
		}

		.time {
			margin-left: 8rpx;
		}
	}
}
</style>