<template>
	<view class="page-editingInfo">
		<!-- 导航栏 -->
		<navbar title="账号与安全" />
		<view class="page-box">
			<view class="page-box-item flex-sty">
				<view class="info-txt1">员工编号</view>
				<view class="flex-sty2">
					<view class="info-txt2">{{userStore.userInfo.salesman_num}}</view>
				</view>
			</view>
			<view class="page-box-item1 flex-sty">
				<view class="info-txt1">手机号</view>
				<view class="flex-sty2">
					<view class="info-txt2">{{userStore.userInfo.tel.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')}}</view>
					<!-- <uni-icons type="right" size="20" color="#A0A8B0"></uni-icons> -->
				</view>
			</view>
			<view class="page-box-item1 flex-sty border-none" @click="jumpTo('/pages/my/changePassword')">
				<view class="info-txt1">密码</view>
				<view class="flex-sty2">
					<view class="info-txt2"></view>
					<uni-icons type="right" size="20" color="#A0A8B0"></uni-icons>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, reactive, computed } from 'vue';
	import navbar from '@/components/navbar/navbar.vue';
	import useUserStore from '@/store/user';

	const userStore = useUserStore();

	/** 页面跳转 */
	function jumpTo(url) {
		uni.navigateTo({
			url: url
		});
	}
</script>

<style lang="scss" scoped>
	.flex-sty {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.flex-sty1 {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.flex-sty2 {
		display: flex;
		align-items: center;
	}

	.page-editingInfo {
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		padding: 32rpx;

		.page-box {
			width: 100%;
			height: 352rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
			border-radius: 16rpx;
			border: 1rpx solid #EDF0F5;
			box-sizing: border-box;
			padding: 32rpx;

			.page-box-item {
				height: 104rpx;
				border-bottom: 1rpx solid #DEE2E8;
			}

			.page-box-item1 {
				height: 90rpx;
				border-bottom: 1rpx solid #DEE2E8;
			}

			.border-none {
				border-bottom: none;
			}

			.info-txt1 {
				font-weight: 400;
				font-size: 28rpx;
				color: $uni-main-color;
				font-style: normal;
			}

			.info-txt2 {
				width: 300rpx;
				font-weight: 400;
				font-size: 28rpx;
				color: $uni-secondary-color;
				text-align: right;
				font-style: normal;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
		}
	}
</style>