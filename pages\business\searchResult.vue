<template>
  <scroll-view scroll-y class="container" @scroll="handleScroll" @scrolltolower="onReachBottom">
    <!-- 背景区域 -->
    <view class="bg-gradient"></view>

    <!-- 导航栏 -->
    <navbar title="商家列表" :backGroundColor="navBgColor" />

    <!-- 内容区域 -->
    <view class="merchant-page" style="padding-top:0;margin-top: 16rpx;">
      <!-- 数量显示 -->
      <view class="count-info">
        <text class="label">数量:{{ count }}</text>
      </view>

      <!-- 商家列表 -->
      <view class="merchant-list">
        <view v-for="(merchant, index) in merchantList" :key="merchant.merchant_id" class="merchant-card">
          <view class="merchant-item">
            <view class="merchant-info">
              <view class="name-row">
                <text v-if="merchant.to_do_items_type >= 0 || merchant.to_do_items_type == -1"
                  class="status-tag">进件中</text>
                <text class="name">{{ truncateName(merchant.bse_lice_nm) }}</text>
              </view>
              <text class="contact-name">{{ merchant.s_legal_name || '未设置' }}</text>
              <text class="contact-phone">{{ formatPhone(merchant.s_legal_phone) }}</text>
            </view>
            <view class="action" @click="handleMerchantClick(merchant)">
              <text class="action-text">{{ merchant.to_do_items_type >= 0 || merchant.to_do_items_type == -1 ? '去进件' :
                '商家详情' }}</text>
              <image src="@/static/images/right-icon.png" class="right-icon" mode="aspectFit"></image>
            </view>
          </view>
        </view>
        <!-- 加载更多 -->
        <up-loadmore :status="loadMoreStatus" @loadmore="loadMore" />
      </view>
    </view>
  </scroll-view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import navbar from '@/components/navbar/navbar.vue'
import { statisticsApi } from '@/api/business'

// 导航栏背景色
const navBgColor = ref('transparent')
const keywords = ref('')
const merchantList = ref([])
const page = ref(1)
const loadMoreStatus = ref('loadmore')
const count = ref(0)

// 处理滚动事件
function handleScroll (e) {
  const scrollTop = e.detail.scrollTop
  navBgColor.value = scrollTop <= 0 ? 'transparent' : '#ffffff'
}

// 页面跳转
function jumpTo (url) {
  uni.navigateTo({ url })
}

// 加载商家列表
async function loadMerchantList () {
  try {
    loadMoreStatus.value = 'loading'
    const params = {
      page: page.value,
      limit: 10,
      keywords: keywords.value
    }
    const res = await statisticsApi(params)
    if (res.code == 200) {
      const list = res.data.list
      count.value = res.data.count
      if (page.value == 1) {
        merchantList.value = list
      } else {
        merchantList.value.push(...list)
      }

      // 判断是否加载完成
      if (list.length < 10) {
        loadMoreStatus.value = 'nomore'
      } else {
        loadMoreStatus.value = 'loadmore'
        page.value++
      }
    }
  } catch (error) {
    console.error('搜索商家失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
    loadMoreStatus.value = 'loadmore'
  }
}

// 加载更多
function loadMore () {
  if (loadMoreStatus.value == 'loadmore') {
    loadMerchantList()
  }
}

// 触底加载
function onReachBottom () {
  if (loadMoreStatus.value == 'loadmore') {
    loadMerchantList()
  }
}

// 处理商家名称，超过11个字符的加上省略号
function truncateName (name) {
  if (!name) return '未命名商家'
  return name.length > 11 ? name.slice(0, 11) + '...' : name
}

// 格式化电话号码
function formatPhone (phone) {
  if (!phone) return ''
  return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')
}

// 处理商家点击
function handleMerchantClick (merchant) {
  const merchant_id = merchant.id
  uni.setStorageSync('merchant_id', merchant_id)

  if (merchant.to_do_items_type == 4) {
    uni.navigateTo({
      url: '/pages/accomplish/accomplish?id=1'
    })
    return
  } else if (merchant.to_do_items_type == 5) {
    uni.navigateTo({
      url: '/pages/accomplish/accomplish?id=1'
    })
    return
  } else if (merchant.to_do_items_type == -1) {
    uni.navigateTo({
      url: '/pages/accomplish/accomplish?id=1'
    })
    return
  }

  if (merchant.to_do_items_type >= 0) {
    // 进件中状态，根据进度跳转
    switch (merchant.save_page) {
      case 1:
        uni.navigateTo({
          url: '/pages/solutionSelection/solutionSelection?merchant_id=' + merchant_id
        })
        break
      case 2:
        uni.navigateTo({
          url: '/pages/identityCard/identityCard?merchant_id=' + merchant_id
        })
        break
      case 3:
        uni.navigateTo({
          url: '/pages/corporateIdentityCard/corporateIdentityCard?merchant_id=' + merchant_id
        })
        break
      case 4:
        uni.navigateTo({
          url: '/pages/shopScene/shopScene?merchant_id=' + merchant_id
        })
        break
      case 5:
        uni.navigateTo({
          url: '/pages/debitCard/debitCard?merchant_id=' + merchant_id
        })
        break
      default:
        uni.navigateTo({
          url: '/pages/solutionSelection/solutionSelection?merchant_id=' + merchant_id
        })
    }
  } else {
    jumpTo('/pages/business/merchantDetail?merchant_id=' + merchant.merchant_id)
  }
}

onLoad((options) => {
  if (options.keywords) {
    keywords.value = decodeURIComponent(options.keywords)
    loadMerchantList()
  }
})
</script>

<style lang="scss" scoped>
// 继承 merchant.vue 的基础样式
.container {
  height: 100vh;
  background: #F0F2F5;
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 272rpx;
  background: linear-gradient(180deg, #B3D8FF 0%, #F0F2F5 100%);
  z-index: 1;
}

.merchant-page {
  position: relative;
  z-index: 2;
  padding: 32rpx;
}

// 新增数量显示样式
.count-info {
  margin-top: 16rpx;
  color: #979B9F;
  font-size: 26rpx;
  line-height: 40rpx;

  .label {
    margin-right: 8rpx;
    font-weight: 400;
    font-size: 26rpx;
    color: #979B9F;
    line-height: 40rpx;
  }
}

.merchant-list {
  .merchant-card {
    margin-top: 16rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    padding: 32rpx;
    box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.06);
    margin-bottom: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .merchant-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .merchant-info {
      display: flex;
      flex-direction: column;

      .name-row {
        display: flex;
        align-items: center;

        .status-tag {
          display: inline-flex;
          align-items: center;
          font-size: 24rpx;
          color: #FFFFFF;
          background: #F39237;
          padding: 4rpx 12rpx;
          border-radius: 6rpx;
          margin-right: 10rpx;
          line-height: 36rpx;
          font-weight: 400;
          height: 36rpx;
        }

        .name {
          font-size: 30rpx;
          color: #18191A;
          font-weight: 500;
          line-height: 44rpx;
          letter-spacing: 0.5px;
        }
      }

      .contact-name {
        font-size: 26rpx;
        color: #979B9F;
        font-weight: 500;
        line-height: 40rpx;
        margin-top: 8rpx;
      }

      .contact-phone {
        font-size: 26rpx;
        color: #979B9F;
        font-weight: 400;
        line-height: 40rpx;
        margin-top: 4rpx;
      }
    }

    .action {
      display: flex;
      align-items: center;
      // margin-top: 4rpx;

      .action-text {
        font-size: 24rpx;
        color: #979B9F;
        font-weight: 400;
        line-height: 36rpx;
      }

      .right-icon {
        width: 44rpx;
        height: 44rpx;
        margin-left: 8rpx;
      }
    }
  }
}
</style>