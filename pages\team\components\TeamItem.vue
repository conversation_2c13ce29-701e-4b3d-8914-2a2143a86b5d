<template>
	<view class="team-item flex-sty">
		<view class="team-item-l flex-sty1">
			<view class="team-txt1">{{props.item.team_name}}</view>
			<view class="flex-sty">
				<view class="">
					<view class="team-txt2">本月目标</view>
					<view class="team-txt3">{{props.item.monthly_tasks}}</view>
				</view>
				<view class="">
					<view class="team-txt2">本月开户数</view>
					<view class="team-txt3">{{props.item.count_merchant}}</view>
				</view>
			</view>
		</view>
		<view class="team-item-r flex-sty1">
			<view class="team-txt4">{{props.item.count_salesman}}人</view>
			<!-- <view class="">
				<view class="team-txt4">10人</view>
				<view class="">
					
				</view>
			</view> -->
			<view class="">
				<view class="team-txt5">本月完成度</view>
				<view v-if="props.item.proportion < 100" class="team-txt6">{{props.item.proportion}}%</view>
				<view v-else class="team-txt6" style="color:#36C60E">{{props.item.proportion}}%</view>
				<up-line-progress v-if="props.item.proportion < 100" height="8" :percentage="props.item.proportion" activeColor="#1386FB" :showText="false"></up-line-progress>
				<up-line-progress v-else  height="8" :percentage="props.item.proportion" activeColor="#36C60E" :showText="false"></up-line-progress>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, reactive, computed } from 'vue';
	const props = defineProps({
		item: {
			type: Object,
			default: {}
		}
	})
	
</script>

<style lang="scss" scoped>
	.flex-sty{
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.flex-sty1{
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.team-item{
		width: 100%;
		height: 260rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75,80,85,0.04);
		border-radius: 16rpx;
		border: 1rpx solid #EDF0F5;
		margin-bottom: 24rpx;
		box-sizing: border-box;
		padding: 32rpx;
		
		.team-item-l{
			width: 50%;
			height: 100%;
			
			.team-txt1{
				width: 100%;
				height: 48rpx;
				font-weight: 600;
				font-size: 32rpx;
				color: $uni-main-color;
				line-height: 48rpx;
				font-style: normal;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
			
			.team-txt2{
				width: 120rpx;
				height: 40rpx;
				font-weight: 500;
				font-size: 22rpx;
				color: $uni-secondary-color;
				line-height: 40rpx;
				font-style: normal;
			}
			
			.team-txt3{
				width: 120rpx;
				font-weight: 600;
				font-size: 32rpx;
				color: $uni-main-color;
				font-style: normal;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
		}
		
		.team-item-r{
			width: 36%;
			height: 100%;
			
			.team-txt4{
				// width: 80rpx;
				height: 40rpx;
				font-weight: 500;
				font-size: 24rpx;
				color: $uni-secondary-color;
				line-height: 40rpx;
				text-align: right;
				font-style: normal;
				// overflow: hidden;
				// white-space: nowrap;
				// text-overflow: ellipsis;
			}
			
			.team-txt5{
				height: 40rpx;
				font-weight: 500;
				font-size: 22rpx;
				color: $uni-secondary-color;
				line-height: 40rpx;
				font-style: normal;
				text-align: right;
			}
			
			.team-txt6{
				height: 44rpx;
				font-weight: 600;
				font-size: 28rpx;
				color: $uni-primary;
				line-height: 44rpx;
				font-style: normal;
				text-align: right;
			}
		}
	}
</style>