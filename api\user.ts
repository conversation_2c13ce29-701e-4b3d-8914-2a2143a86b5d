/**登录*/
export const loginApi = (data: any) => uni.$http.post('tenant_salesman.user/login', data);

/** 手机号发送验证码 */
export const sendsmsApi = (data: any) => uni.$http.post('tenant_salesman.user/sendsms', data);

/** 检测验证码 */
export const checksmsApi = (data: any) => uni.$http.post('tenant_salesman.user/checksms', data);

/** 忘记密码 */
export const forgetPasswordApi = (data: any) => uni.$http.post('tenant_salesman.user/forgetpassword', data);

/**获取用户信息*/
export const getUserInfoApi = () => uni.$http.get('tenant_salesman.user/getInfo');

/* 退出登录 */
export const loginOutApi = () => uni.$http.get('merchant/auth/logout');

/** 修改密码 */
export const updatePwApi = (data: any) => uni.$http.post('tenant_salesman.user/updatePassword', data);

/** 修改资料 */
export const updateInfoApi = (data: any) => uni.$http.post('tenant_salesman.user/updateInfo',data);

/**获取版本信息*/
export const getLastAppVersion = (type: any) => uni.$http.get(`tenant_salesman.user/getLastAppVersion?type=${type}`);
