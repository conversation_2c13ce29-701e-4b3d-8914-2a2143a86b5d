<template>
	<view class="page-editingInfo">
		<!-- 导航栏 -->
		<navbar title="编辑资料" />
		<view class="page-box">
			<view class="page-box-item flex-sty" @click="show = true">
				<view class="info-txt1">头像</view>
				<view class="flex-sty2">
					<image v-if="userStore.userInfo.salesman_avatar" style="width: 72rpx;height: 72rpx;border-radius: 50%;"
						:src=" baseURL + userStore.userInfo.salesman_avatar" mode=""></image>
					<image v-else style="width: 72rpx;height: 72rpx;border-radius: 50%;"
						:src="noImg" mode=""></image>
					<uni-icons type="right" size="20" color="#A0A8B0"></uni-icons>
				</view>
			</view>
			<view class="page-box-item1 flex-sty" @click="jumpTo('/pages/my/changeName')">
				<view class="info-txt1">名字</view>
				<view class="flex-sty2">
					<view class="info-txt2">{{userStore.userInfo.name}}</view>
					<uni-icons type="right" size="20" color="#A0A8B0"></uni-icons>
				</view>
			</view>
			<view class="page-box-item1 flex-sty border-none" @click="showSex = true">
				<view class="info-txt1">性别</view>
				<view class="flex-sty2">
					<view class="info-txt2">{{userStore.userInfo.salesman_sex ==1?'男':userStore.userInfo.salesman_sex == 2?'女':'保密'}}
					</view>
					<uni-icons type="right" size="20" color="#A0A8B0"></uni-icons>
				</view>
			</view>
		</view>

		<PopperAvatar :show="show" @closePopperAvatar="closePopperAvatar" />
		
		<up-popup :show="showSex" :round="10">
			<view class="popup4">
				<view class="flex-sty">
					<view class="" style="width: 44rpx;height: 44rpx;"> </view>
					<view class="popup4-txt1">设置性别</view>
					<image @click="showSex = false" style="width: 44rpx;height: 44rpx;" src="@/static/images/close.png" mode=""></image>
				</view>
				<view class="popup4-txt2 margin-sty border-bot" @click="sexSelect(1)">男</view>
				<view class="popup4-txt2 border-bot" @click="sexSelect(2)">女</view>
				<view class="popup4-txt2"  @click="sexSelect(0)">保密</view>
			</view>
		</up-popup>
	</view>
</template>

<script setup lang="ts">
	import { ref, reactive, computed, inject } from 'vue';
	import navbar from '@/components/navbar/navbar.vue';
	import PopperAvatar from '@/pages/my/PopperAvatar.vue'
	import { updateInfoApi } from '@/api/user';
	import useUserStore from '@/store/user';
	import noImg from '@/static/images/noAvatar.png'

	const baseURL = inject('baseURL');
	const userStore = useUserStore();

	/** 头像弹窗 */
	const show = ref(false)
	function closePopperAvatar() {
		show.value = false
	}

	let showSex = ref(false)
	/** 选择的性别 */
	function sexSelect(val : any) {
		userStore.userInfo.salesman_sex = val
		showSex.value = false
		otupdateInfoApi()
	}

	/** 修改资料 修改性别*/
	async function otupdateInfoApi() {
		const params = {
			id: userStore.userInfo.id,
			salesman_sex: userStore.userInfo.salesman_sex
		}
		const res = await updateInfoApi(params)
		if (res.code == 200) {
			userStore.otgetUserInfoApi().then(() => {
				uni.utils.toast(res.msg)
			})
		} else {
			userStore.otgetUserInfoApi().then(() => {
				uni.utils.toast(res.msg)
			})
		}
	}

	/** 页面跳转 */
	function jumpTo(url) {
		uni.navigateTo({
			url: url
		});
	}
</script>

<style lang="scss" scoped>
	::v-deep .u-action-sheet__header__title {
		font-weight: 600;
		font-size: 34rpx;
		color: $uni-main-color;
		font-style: normal;
	}

	::v-deep .u-action-sheet__item-wrap__item {
		font-weight: 400;
		font-size: 32rpx;
		color: $uni-main-color;
		font-style: normal;
	}

	.flex-sty {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.flex-sty1 {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.flex-sty2 {
		display: flex;
		align-items: center;
	}

	.page-editingInfo {
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		padding: 32rpx;

		.page-box {
			width: 100%;
			height: 352rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
			border-radius: 16rpx;
			border: 1rpx solid #EDF0F5;
			box-sizing: border-box;
			padding: 32rpx;

			.page-box-item {
				height: 104rpx;
				border-bottom: 1rpx solid #DEE2E8;
			}

			.page-box-item1 {
				height: 90rpx;
				border-bottom: 1rpx solid #DEE2E8;
			}

			.border-none {
				border-bottom: none;
			}

			.info-txt1 {
				font-weight: 400;
				font-size: 28rpx;
				color: $uni-main-color;
				font-style: normal;
			}

			.info-txt2 {
				width: 120rpx;
				font-weight: 400;
				font-size: 28rpx;
				color: $uni-secondary-color;
				text-align: right;
				font-style: normal;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
		}
	}
	
	.popup4 {
		width: 100%;
		height: 412rpx;
		background: #FFFFFF;
		border-radius: 24rpx 24rpx 0rpx 0rpx;
		box-sizing: border-box;
		padding: 40rpx 32rpx;
	
		.popup4-txt1 {
			font-weight: 600;
			font-size: 34rpx;
			color: $uni-base-color;
			font-style: normal;
		}
	
		.margin-sty {
			margin-top: 24rpx;
		}
		
		.border-bot{
			box-sizing: border-box;
			border-bottom: 1rpx solid #DEE2E8;
		}
	
		.popup4-txt2 {
			width: 100%;
			height: 96rpx;
			line-height: 96rpx;
			text-align: center;
			font-weight: 500;
			font-size: 32rpx;
			color: $uni-main-color;
			font-style: normal;
		}
	}
</style>