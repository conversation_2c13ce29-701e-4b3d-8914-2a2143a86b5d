// 打印插件使用方式:
() => {
	const printModule = uni.requireNativePlugin("mfc-print-by-usb")
	printModule.init(
		(res) => {
			if (res.success === true) {
				printModule.print({
					data: getTobePrintData()
				}, (res) => {
					uni.showToast({ 
						title: res.message
					})
				})
			} else {
				uni.showToast({
					title: res.message
				})
			}
		})
}

/**
 * 获取待打印的数据
 * @param {{}} option 
 * @returns 
 */
export default function getTobePrintData(option) {
	/**打印的数据
	 * 单列不用处理，多列使用formatColumn方法处理。
	 */
	return [{
			align: 'center', // 对其方式：left center right
			text: '商户名称',
			fontSize: 2, // 默认值：1。1：为正常大小。2： 翻倍。
			fontWeight: 'bold',
			rowSpace: 10 // 行间距 默认： 10 
		},
		{
			align: 'center',
			fontWeight: 'bold', // 加粗，默认不加粗
			text: '结账单',
		},
		{
			align: 'left',
			fontWeight: 'bold',
			text: '订单号：12021 150 2 54',
		},
		{
			align: 'left',
			fontWeight: 'bold',
			text: '桌台号：A-12',
		},
		{
			align: 'left',
			fontWeight: 'bold',
			text: '就餐时间：2023-02-15 11::56:36',
		},
		{
			align: 'left',
			fontWeight: 'bold',
			text: '就餐人数：8'
		},
		{
			align: 'center',
			text: '—— —— —— —— —— —— —'
		},
		{
			align: 'left',
			fontWeight: 'bold',
			text: formatColumn([{
					length: 9,
					text: '品名',
					align: 'right'
				},
				{
					length: 3,
					text: '数量',
					align: 'left'
				},
				{
					length: 4,
					text: '金额',
					align: 'left'
				},
			]).join('')
		},
		{
			align: 'left',
			fontWeight: 'bold',
			text: formatColumn([{
					length: 9,
					text: '内蒙古烤羊腿',
					align: 'right'
				},
				{
					length: 3,
					text: '0.6',
					align: 'left'
				},
				{
					length: 4,
					text: '27.26',
					align: 'left'
				},
			]).join('\n')
		},
		{
			align: 'left',
			fontWeight: 'bold',
			text: formatColumn([{
					length: 9,
					text: '美团398套餐七八',
					align: 'right'
				},
				{
					length: 3,
					text: '123.12',
					align: 'left'
				},
				{
					length: 4,
					text: '12345.12',
					align: 'left'
				},
			]).join('\n')
		},
		{
			align: 'left',
			text: formatColumn([{
					length: 9,
					text: '内蒙古',
					align: 'right'
				},
				{
					length: 3,
					text: '0.6',
					align: 'left'
				},
				{
					length: 4,
					text: '27.26',
					align: 'left'
				},
			]).join('\n')
		},
		{
			align: 'center',
			text: '—— —— —— —— —— —— —'
		},
		{
			align: 'left',
			fontWeight: 'bold',
			text: formatColumn([{
					length: 9,
					text: '消费合计',
					align: 'right'
				},
				{
					length: 7,
					text: '123.26',
					align: 'left'
				}
			]).join('\n')
		},
		{
			align: 'left',
			text: formatColumn([{
					length: 9,
					text: '扫码支付',
					align: 'right'
				},
				{
					length: 7,
					text: '123.26',
					align: 'left'
				}
			]).join('\n')
		},
		{
			align: 'left',
			fontWeight: 'bold',
			text: formatColumn([{
					length: 9,
					text: '实收金额',
					align: 'right'
				},
				{
					length: 7,
					text: '123.26',
					align: 'left'
				}
			]).join('\n')
		},
		{
			align: 'center',
			text: '—— —— —— —— —— —— —'
		},
		{
			align: 'left',
			text: '门店地址：xxxxxx'
		},
		{
			align: 'left',
			text: '联系电话：xxxxxx'
		},
		{
			align: 'center',
			text: '本系统由客予提供'
		},
	]


}



/** formatColumn 参数的示例数据。一行数据，数组每一项代表一列。一行最多16个中文。英文、数字大小为中文字符的一半。 */
const textList = [{
		/**当前列最大字符数量，超出会换行 */
		length: 8,
		text: '我是打印我是我111',
		/**left: 左侧填充空字符，right：右侧填充空字符 */
		align: 'right'
	},
	{
		length: 3,
		text: '122312',
		align: 'left'
	},
	{
		length: 3,
		text: '份',
		align: 'left'
	}
]
/**
 * 多列的格式化
 * @param {textList} textList 
 * @returns 
 */
function formatColumn(textList) {
	const formatedTotalList = []
	textList.forEach((item, index) => {
		const formatedArr = formatStrToArr(String(item.text), item.length, item.align)
		if (index === 0) {
			formatedTotalList.push(...formatedArr)
		} else {
			formatedArr.forEach((formatedItem, formatedIndex) => {
				let leftText = `${formatedTotalList[formatedIndex] || ''}`
				if (formatedTotalList[formatedIndex] === undefined) {
					for (let i = index - 1; i >= 0; i--) {
						leftText += `${new Array(textList[i].length).fill('  ').join('')}`
					}
				}
				formatedTotalList[formatedIndex] = `${leftText}${formatedItem}`
			})
		}
	})
	return formatedTotalList
}
const arr = formatColumn(textList)
console.log(arr)

/**
 * 把字符串分割成数组，\n 换行
 * @param {string} text 待分割的字符串
 * @param {number} maxLength 最大长度
 * @param {string} align 默认值：left 。left: 左侧填充空字符，right：右侧填充空字符
 */
function formatStrToArr(text, maxLength, align = 'left') {
	const formatList = []
	const splitList = text.split('\n')
	splitList.forEach(item => {
		splitTextToArr(item, 0, maxLength, formatList, align)
	})
	return formatList
}
// const str = '12345\n67\n890123456789012345\n67890'
// const arr = formatStrToArr(str, 5)
// console.log(arr)


/**
 * 把字符串按照指定长度截取，存入指定数组
 * @param {string} text 
 * @param {number} startIndex 
 * @param {number} maxLength
 * @param {Array} arr 
 * @param {string} align left: 左侧填充空字符，right：右侧填充空字符
 */
function splitTextToArr(text, startIndex, maxLength, arr, align = 'left') {
	const textLength = getTextLength(text)
	// if (startIndex > textLength - 1) return
	const enxIndex = startIndex + maxLength
	// console.log(startIndex, enxIndex)
	let currentText = substringText(text, startIndex, enxIndex)
	if (!currentText) return
	const currentTextLength = getTextLength(currentText)
	if (currentTextLength <= maxLength) {
		if (align === 'left') {
			currentText = `${new Array((maxLength - currentTextLength) * 2).fill(' ').join('')}${currentText}`
		} else {
			currentText = `${currentText}${new Array((maxLength - currentTextLength) * 2).fill(' ').join('')}`
		}
	}
	arr.push(currentText)

	splitTextToArr(text, startIndex + maxLength, maxLength, arr, align)
}
// const arr = []
// const str = '123456789012345678901234567890'
// splitTextToArr(str, 0, 29, arr)
// console.log(arr)

/**截取字符串
 * @param {string} text
 */
function substringText(text, startIndex, enxIndex) {
	const res = text.match(/[^\d^.]+/g)
	// 数字
	if (!res) {
		const currentLen = getTextLength(text)
		return text.substring(startIndex * 2, enxIndex * 2)
	}
	return text.substring(startIndex, enxIndex)
}

/**
 * 获取字符串的长度。打印机数字和字母的占位是文字的一半。
 * @param {string} text 
 * @returns 
 */
function getTextLength(text) {
	const res = text.match(/[a-zA-Z\d.-]+/g)
	let numLen = 0
	if (res) {
		numLen = res.reduce((total, item) => {
			return total + item.length
		}, 0)
	}
	return text.length - numLen + (numLen / 2)
}
// console.log(getTextLength('4561.60'))