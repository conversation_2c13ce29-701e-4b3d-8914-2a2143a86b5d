<template>
  <view class="content">
    <!-- 导航栏 -->
    <navbar title="完成" v-if="id == 1" />
    <navbar title="完成" leftIcon=" " :isAutoBack="false" v-else />
    <view class="accomplish">
      <image class="titleImg" src="@/static/images/tijiao.png" mode="aspectFit"></image>
      <view class="win">信息提交成功</view>
      <view class="audit">信息已移交工作人员审核，审核成功后会通知相关人员进行下一步操作</view>
      <view class="btnBox" @click="back" v-if="id == 1">
        <view class="text">返回</view>
      </view>
      <view class="btnBox" @click="home" v-else>
        <view class="text">回到首页</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
/** 获取顶部状态栏高度 */
const statusBarHeight = uni.getStorageSync('statusBarHeight');
const id = ref(0)
const home = () => {
  uni.reLaunch({
    url: '/pages/index/index'
  });
};

const back = () => {
  uni.navigateBack();
};

onLoad((options) => {
  console.log(options);
  id.value = options.id
})
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;

  .titleImg {
    width: 230rpx;
    height: 176rpx;
    margin-top: 56rpx;
    margin-left: 256rpx;
  }

  .win {
    font-weight: 600;
    font-size: 34rpx;
    color: #18191a;
    margin-top: 32rpx;
    margin-left: 270rpx;
  }

  .audit {
    width: 482rpx;
    font-weight: 400;
    font-size: 26rpx;
    color: #9da5ad;
    margin-left: 134rpx;
    text-align: center;
    margin-top: 24rpx;
  }

  .btnBox {
    width: 686rpx;
    height: 88rpx;
    background: #1386fb;
    border-radius: 8rpx;
    margin-left: 32rpx;
    line-height: 88rpx;
    margin-top: 72rpx;

    .text {
      font-weight: 600;
      font-size: 26rpx;
      color: #ffffff;
      text-align: center;
    }
  }
}
</style>
