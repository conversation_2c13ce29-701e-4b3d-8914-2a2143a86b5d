<template>
	<view class="page-about-container">
		<!-- 导航栏 -->
		<navbar title="关于" />
		<view class="page-about-top">
			<view class="img-box">
				<image style="width: 136rpx;height: 136rpx;" src="@/static/images/logo.png" mode=""></image>
			</view>
			<view class="about-txt1">v{{version}}</view>
			<view class="page-about-m">
				<view class="page-about-m-item flex-sty" @click="jumpTo('/pages/my/privacyPolicy')">
					<view class="about-txt2">隐私政策</view>
					<uni-icons type="right" size="20" color="#A0A8B0"></uni-icons>
				</view>
				<view class="line1"></view>
				<view class="page-about-m-item flex-sty" @click="jumpTo('/pages/my/userAgreement')">
					<view class="about-txt2">用户协议</view>
					<uni-icons type="right" size="20" color="#A0A8B0"></uni-icons>
				</view>
			</view>
		</view>
		<!-- #ifdef APP-PLUS -->
		<view class="btn-box">
			<view class="btn1" @click="checkHandel">检查新版本</view>
		</view>
		<!-- #endif -->
	</view>
</template>

<script lang="ts" setup>
	import { ref, onMounted } from 'vue';
	import navbar from '@/components/navbar/navbar.vue';
	import useVersionStore from '@/store/version'

	const versionStore = useVersionStore()
	/** 当前版本号 */
	const version = ref(null) //当前版本号

	function getSystemInfo() {
		// #ifdef APP-PLUS
		plus.runtime.getProperty(plus.runtime.appid, function (wgtinfo) {
			if (wgtinfo.version) {
				version.value = wgtinfo.version
			}
		});
		// #endif

		// #ifdef H5
		const systemInfo = uni.getSystemInfoSync();
		version.value = systemInfo.appVersion;
		// #endif
	}

	/** 检查新版本 */
	function checkHandel() {
		versionStore.checkUpdate1()
	}

	/** 页面跳转 */
	function jumpTo(url) {
		uni.navigateTo({
			url: url
		});
	}

	onMounted(() => {
		getSystemInfo()
	})
</script>

<style lang="scss" scoped>
	.flex-sty {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.page-about-container {
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		padding: 32rpx;

		.page-about-top {
			margin-top: 88rpx;
			width: 100%;
		}

		.img-box {
			margin-top: 48rpx;
			width: 100%;
			height: 136rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.about-txt1 {
			margin-top: 16rpx;
			text-align: center;
			height: 44rpx;
			font-weight: 400;
			font-size: 28rpx;
			color: $uni-base-color;
			font-style: normal;
		}

		.page-about-m {
			margin-top: 48rpx;
			width: 100%;
			height: 216rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
			border-radius: 16rpx;
			border: 1rpx solid #EDF0F5;
			box-sizing: border-box;
			padding: 24rpx 40rpx;

			.page-about-m-item {
				width: 100%;
				height: 76rpx;
			}

			.line1 {
				margin-top: 6rpx;
				margin-bottom: 6rpx;
				width: 100%;
				height: 1rpx;
				background: #DEE2E8;
			}

			.about-txt2 {
				font-weight: 400;
				font-size: 28rpx;
				color: $uni-main-color;
				font-style: normal;
			}
		}

		.btn-box {
			position: absolute;
			left: 0;
			bottom: 0;
			width: 100%;
			height: 200rpx;
			box-sizing: border-box;
			padding: 0 32rpx;
			background-color: #F0F2F5;

			.btn1 {
				width: 100%;
				height: 88rpx;
				line-height: 88rpx;
				background: $uni-primary;
				border-radius: 8rpx;
				text-align: center;
				font-weight: 600;
				font-size: 26rpx;
				color: #fff;
				font-style: normal;
			}
		}
	}
</style>