<?xml version="1.0" encoding="UTF-8"?>
<svg width="204px" height="95px" viewBox="0 0 204 95" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>上传营业执照</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="106.434783" height="72" rx="4"></rect>
        <filter x="-0.5%" y="-0.7%" width="100.9%" height="102.8%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.871722571   0 0 0 0 0.888232469   0 0 0 0 0.908044345  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <circle id="path-3" cx="18" cy="18" r="18"></circle>
        <filter x="-66.7%" y="-50.0%" width="233.3%" height="233.3%" filterUnits="objectBoundingBox" id="filter-4">
            <feMorphology radius="1" operator="erode" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="6" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="8" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.0822067506   0 0 0 0 0.159225759   0 0 0 0 0.23842455  0 0 0 0.0769179551 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="3" in="SourceAlpha" result="shadowOffsetOuter2"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter2" result="shadowBlurOuter2"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.181502602   0 0 0 0 0.426258855   0 0 0 0 0.675271739  0 0 0 0.101826274 0" type="matrix" in="shadowBlurOuter2" result="shadowMatrixOuter2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="shadowMatrixOuter2"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="进件" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="进件_2" transform="translate(-85.5, -299.5)">
            <g id="编组-14" transform="translate(16, 266)">
                <g id="编组-7" transform="translate(56, 20)">
                    <g id="上传营业执照" transform="translate(14, 14)">
                        <g id="编组-17">
                            <g id="编组-5" transform="translate(49, 11)">
                                <g id="矩形">
                                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-1"></use>
                                </g>
                                <rect id="矩形" stroke="#B3D8FF" stroke-width="2" fill="#E5F2FF" x="7.26086957" y="16.6521739" width="91.9130435" height="48.0869565" rx="2"></rect>
                                <circle id="椭圆形" fill="#B3D8FF" cx="53.2173913" cy="15.6521739" r="9.39130435"></circle>
                                <rect id="矩形" fill="#F6F8FC" x="29.7391304" y="31.3043478" width="46.9565217" height="9.39130435"></rect>
                                <rect id="矩形备份-2" fill="#F6F8FC" x="12.5217391" y="48.5217391" width="36" height="3.13043478"></rect>
                                <rect id="矩形备份-4" fill="#F6F8FC" x="57.9130435" y="48.5217391" width="36" height="3.13043478"></rect>
                                <rect id="矩形备份-3" fill="#F6F8FC" x="12.5217391" y="54.7826087" width="36" height="3.13043478"></rect>
                                <rect id="矩形备份-5" fill="#F6F8FC" x="57.9130435" y="54.7826087" width="36" height="3.13043478"></rect>
                            </g>
                            <g id="编组-15" stroke="#B3D8FF">
                                <polyline id="路径" points="0 16 0 0 16 0"></polyline>
                                <polyline id="路径" points="16 94 0 94 0 78"></polyline>
                                <polyline id="路径" points="187 0 203 0 203 16"></polyline>
                                <polyline id="路径" points="203 78 203 94 187 94"></polyline>
                            </g>
                        </g>
                        <g id="编组-16" transform="translate(84, 29)">
                            <g id="椭圆形">
                                <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                                <use fill="#1386FB" fill-rule="evenodd" xlink:href="#path-3"></use>
                            </g>
                            <g id="编组" transform="translate(6, 6)">
                                <polygon id="矩形" points="0 0 24 0 24 18.8795983 24 24 0 24"></polygon>
                                <rect id="矩形" fill="#FFFFFF" x="2.70967742" y="11.2258065" width="18.5806452" height="1.5483871"></rect>
                                <rect id="矩形" fill="#FFFFFF" x="11.2258065" y="2.70967742" width="1.5483871" height="18.5806452"></rect>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>