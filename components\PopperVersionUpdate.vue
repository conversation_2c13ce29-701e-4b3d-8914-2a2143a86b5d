<template>
	<up-popup :show="props.modelValue" mode="center" bgColor="transparent">
		<view class="popup1">
			<view class="popup1-sty1">
				<up-icon name="close" color="#9DA5AD" size="18" @click="closeUpdateHandel"></up-icon>
			</view>
			<!-- 发现新版本 -->
			<view class="popup1-sty2" v-if="showStep == 1">
				<image src="@/static/images/update.png" mode="" style="width: 200rpx;height: 200rpx;"></image>
				<view class="popup1-txt1 margin-sty1">发现新版本v{{versionStore.new_version}}</view>
				<view class="popup1-txt6 popup1-txt2 margin-sty">{{versionStore.content}}</view>
				<view class="popup1-txt3 margin-sty1" @click="updateHandel">立即更新</view>
				<view class="popup1-txt4 margin-sty" @click="closeUpdateHandel">暂不更新</view>
			</view>
			<!-- 新版本下载中 -->
			<view class="popup1-sty2" v-if="showStep == 2">
				<image src="@/static/images/update.png" mode="" style="width: 200rpx;height: 200rpx;"></image>
				<view class="popup1-txt1 margin-sty1">新版本下载中…</view>
				<view class="popup1-txt2 margin-sty">请不要关闭此页面</view>
				<view class="popup1-sty3 margin-sty3">
					<progress :percent="percent" />
					<view class="popup1-sty3-txt">{{percent}}%</view>
				</view>
				<view class="popup1-txt5 margin-sty2">正在努力更新中，请稍等</view>
			</view>
			<!-- 准备就绪 -->
			<view class="popup1-sty2" v-if="showStep == 3">
				<image src="@/static/images/update.png" mode="" style="width: 200rpx;height: 200rpx;"></image>
				<view class="popup1-txt1 margin-sty1">准备就绪</view>
				<view class="popup1-txt2 margin-sty">点击下方安装按钮立即安装</view>
				<view class="popup1-txt3 margin-sty4" @click="updateHandel1">立即安装</view>
				<view class="popup1-txt4 margin-sty" @click="closeUpdateHandel">暂不安装</view>
			</view>
		</view>
	</up-popup>
</template>

<script lang="ts" setup>
	/**
	 * @description 版本更新弹窗
	 * @param {type}  
	 * @example <PopperVersionUpdate v-model="show" step="1" />
	 */
	import { ref, watch } from 'vue';
	import useVersionStore from '@/store/version';

	const emit = defineEmits(['closeUpdateHandel']);
	const props = defineProps({
		modelValue: {
			type: Boolean,
			default: true
		},
		step: {
			type: Number,
			default: 1
		}
	})

	const versionStore = useVersionStore()
	const showStep = ref(1) //显示第几步
	const percent = ref(0) //下载百分比
	const tempFilePath = ref(null) //下载的临时路径

	watch(() => props.modelValue, (newVal) => {
		if(newVal){
			percent.value = 0
			//如果已经下载了，并且和新版本的一样，则直接弹出安装弹窗
			if(uni.getStorageSync('savedAPKPath') && uni.getStorageSync('savedAPKVersion') == versionStore.new_version){
				showStep.value = 3
			}else if(props.step == 2){
				updateHandel()
			}
		}
	})

	let downloadTask = null
	/** 立即升级 */
	function updateHandel() {
		showStep.value = 2
		downloadTask = uni.downloadFile({
			url: versionStore.down_url, //下载地址
			success: (res) => {
				if (res.statusCode === 200) {
					showStep.value = 3
					tempFilePath.value = res.tempFilePath
					uni.saveFile({
						tempFilePath: res.tempFilePath,
						success: function (res) {
							uni.setStorageSync('savedAPKVersion',versionStore.new_version)
							uni.setStorageSync('savedAPKPath',res.savedFilePath)
						}
					})
				}
			}
		});

		downloadTask.onProgressUpdate((res) => {
			percent.value = res.progress //更新下载进度
		});
	}

	/** 立即更新 */
	function updateHandel1() {
		//安装
		plus.runtime.install(uni.getStorageSync('savedAPKPath'), { force: true },
			function (res) {
				uni.removeStorageSync('savedAPKVersion') //更新本地存储的数据
				uni.removeStorageSync('savedAPKPath') 
				plus.runtime.restart(); //重启
			}
		);
	}

	function closeUpdateHandel() {
		if (showStep.value == 2) {
			downloadTask.abort() //中断下载任务
		}
		emit('closeUpdateHandel')
	}
</script>

<style lang="scss" scoped>
	::v-deep .uni-progress-bar {
		height: 72rpx !important;
		border-radius: 36rpx !important;
		background-color: #464D57 !important;
		overflow: hidden;
	}

	::v-deep .uni-progress-inner-bar {
		border-radius: 36rpx !important;
		background: linear-gradient(90deg, #08B8FF 0%, #1677FF 100%) !important;
		overflow: hidden;
	}

	.margin-sty {
		margin-top: 32rpx;
	}

	.margin-sty1 {
		margin-top: 48rpx;
	}

	.margin-sty2 {
		margin-top: 68rpx;
	}

	.margin-sty3 {
		margin-top: 80rpx;
	}

	.margin-sty4 {
		margin-top: 88rpx;
	}

	.popup1 {
		width: 622rpx;
		height: 752rpx;
		background: #FFFFFF;
		border-radius: 40rpx;
		box-sizing: border-box;
		padding: 32rpx 56rpx 48rpx;

		.popup1-sty1 {
			display: flex;
			justify-content: flex-end;
		}

		.popup1-sty2 {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
		}

		.popup1-sty3 {
			position: relative;
			width: 100%;
			height: 72rpx;

			.popup1-sty3-txt {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				font-weight: 600;
				font-size: 28rpx;
				color: #FFFFFF
			}
		}

		.popup1-txt1 {
			font-weight: 600;
			font-size: 44rpx;
			color: #1386FB;
			line-height: 60rpx;
		}

		.popup1-txt2 {
			font-weight: 400;
			font-size: 28rpx;
			color: #9DA5AD;
			line-height: 40rpx;
		}

		.popup1-txt3 {
			width: 510rpx;
			height: 96rpx;
			line-height: 96rpx;
			background: #1386FB;
			border-radius: 48rpx;
			font-weight: 600;
			font-size: 34rpx;
			color: #FFFFFF;
			text-align: center;
		}

		.popup1-txt4 {
			font-weight: 500;
			font-size: 28rpx;
			color: #3B8FFF;
			line-height: 44rpx;
		}

		.popup1-txt5 {
			font-weight: 500;
			font-size: 28rpx;
			color: #1386FB;
			line-height: 44rpx;
		}
		
		.popup1-txt6{
			height: 80rpx;
			overflow: hidden;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			text-overflow: ellipsis;
			white-space: normal;
		}
	}
</style>