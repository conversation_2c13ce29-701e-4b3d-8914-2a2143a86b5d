<template>
  <view class="main-page">
    <view class="main-container">
      <component :is="activeComponent"></component>
    </view>
		<view class="tabbar-container">
		  <tabbar @changePage="changePage" />
		</view>
  </view>
</template>

<script setup lang="ts">
import tabbar from '@/components/tabbar/tabbar.vue';
import { ref, watch, defineProps, onMounted, computed,onBeforeMount } from 'vue';
import bus from '@/utils/EventBus';
const activeComponent = ref();

function changePage(item: any) {
  activeComponent.value = item.component;
}

onMounted(async () => {
	 bus.on('busChangePage', (item) => {
		 
		 changePage(item);
	 })

})

</script>

<style lang="scss" scoped>
.main-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
	
	.main-container {
	  flex: 1;
	  width: 100%;
		height: calc(100% - 98rpx);
	}

  .tabbar-container {
    width: 100%;
		height: 98rpx;
		position: fixed;
		left: 0;
		bottom: 0;
  }
}
</style>