/** 新增商户信息 */
export const addMerchantApi = (data: any) => uni.$http.post('tenant_salesman.Incoming/addMerchantInfo', data);

/** 修改商户信息 */
export const editMerchantApi = (data: any) => uni.$http.post('tenant_salesman.Incoming/editMerchantInfo', data);

/** 获取商户信息 */
export const getMerchantApi = (id: any) => uni.$http.post('tenant_salesman.Incoming/getMerchantIncomingDetail?id=' + id);

/** 获取当前用户位置 */
export const getLocation = (data: any) => uni.$http.post('tenant_salesman.Incoming/getMerchantLocationAddress', data);
