import {
	defineStore
} from 'pinia'
 
export const usePermission = defineStore('permission', {
	state: () => ({
		dialogView: null,
		permissionListener: null,
		list: [
			{
				name: "ACCESS_COARSE_LOCATION",
				title: "位置权限说明",
				content: "获取位置信息仅为提供更精准服务，我们承诺严格保护您的隐私安全，请放心开启。",
			}, {
				name: "CAMERA",
				title: "拍摄权限说明",
				content: "获取拍摄权限仅为提供扫一扫、拍照上传等功能，我们承诺严格保护您的隐私安全，请放心开启。",
			}, {
				name: "READ_EXTERNAL_STORAGE",
				title: "相册权限说明",
				content: "获取相册权限仅为提供图片上传等功能，我们承诺严格保护您的隐私安全，请放心开启。",
			}]
	}),
	getters: {
 
	},
	actions: {
		//监听权限申请
		async requstPermission(permissionID) {
			return new Promise((resolve, reject) => {
				try {
					// if (!uni.getSystemInfoSync().platform == 'android') return resolve(true) 
					/**
					 * @description plus.navigator.checkPermission 检查应用是否获取指定权限 
					 * 有些权限检测不到 就继续下面的代码，比如相册权限就可以直接检测，就很方便，授权情况下不需要再走下面代码了
					 * checkPermission 返回参数
					 * @params undetermined 未确定
					 * @params authorized 授权
					 */
					let checkPermission = plus.navigator.checkPermission('android.permission.' + permissionID)
					if (checkPermission == 'authorized') return resolve(true)
					//判断是否自己在list里面配置了这个权限
					let index = this.list.findIndex(item => item.name == permissionID)
					if (index == -1) throw new Error('这个权限没有配置')
					//唤起原生权限说明弹框
					this.requstPermissionDialog(index)
					//授权检测回调
					plus.android.requestPermissions(
						[
							'android.permission.' + permissionID  //单个权限
						],
						(resultObj) => {
              this.permissionListener.stop();
							// 权限申请结果
							/**
							 * @description resultObj.deniedAlways 永久拒绝授权
							 * 多个权限返回结果可能是{"granted":["android.permission.CAMERA"],"deniedPresent":[],"deniedAlways":["android.permission.READ_EXTERNAL_STORAGE"]}
							 * 这个情况就是我同时授权相册和相机，但是只允许了相机，没有授权相册
							 * 这个时候 可以通过deniedAlways 查看哪个权限被永久拒绝了，然后自行在设置弹框内容
							 * 所以可以自己判断细分一下，我下面的代码是先判断了是否有永久拒绝的权限，然后直接弹框提示用户去设置
							 */
							if (resultObj.deniedAlways && resultObj.deniedAlways.length > 0) {
								uni.showModal({
									title: '提示',
									content: '操作权限已被拒绝，请手动前往设置',
									confirmText: "立即设置",
									success: (res) => {
										if (res.confirm) {
											this.gotoAppPermissionSetting()
										} else {
											resolve(false)
										}
									}
								})
								console.log('永久拒绝授权');
							} else if (resultObj.deniedPresent && resultObj.deniedPresent.length > 0) {
								resolve(false)
								console.log('拒绝授权');
							} else
								if (resultObj.granted && resultObj.granted.length > 0) {
									resolve(true)
									console.log('授权成功');
								}
						},
						(error) => {
							reject(false)
							console.log('申请权限错误：',error);
						}
					);
				} catch (err) {
					reject(false)
          console.log(err);
				}
			})
		},
		//监听弹框
		requstPermissionDialog(index) {
			try {
				if (!this.permissionListener){
					this.permissionListener = uni.createRequestPermissionListener()
				}
				const dialogData = this.list[index]
				this.permissionListener.onConfirm((res) => {
					this.dialogStyle(dialogData, true)
				})
				this.permissionListener.onComplete(async (res) => {
					this.dialogStyle({}, false)
				})
			} catch (err) {
				console.log('监听弹框错误', err);
			}
		},
		//弹框样式
		dialogStyle({ title = '', content = '' }, status) {
			try {
				if (!status) return this.dialogView.close()
				const systemInfo = uni.getSystemInfoSync();
				const statusBarHeight = systemInfo.statusBarHeight;
				const navigationBarHeight = systemInfo.platform === 'android' ? 48 : 44;
				const totalHeight = statusBarHeight + navigationBarHeight;
				this.dialogView = new plus.nativeObj.View('per-modal', {
					top: '0px',
					left: '0px',
					width: '100%',
					backgroundColor: '#444',
					//opacity: .5;
				})
				this.dialogView.drawRect({
					color: '#fff',
					radius: '5px'
				}, {
					top: totalHeight + 'px',
					left: '5%',
					width: '90%',
					height: "100px",
				})
				this.dialogView.drawText(title, {
					top: totalHeight + 5 + 'px',
					left: "8%",
					height: "30px"
				}, {
					align: "left",
					color: "#000",
				})
				this.dialogView.drawText(content, {
					top: totalHeight + 35 + 'px',
					height: "60px",
					left: "8%",
					width: "84%"
				}, {
					whiteSpace: 'normal',
					size: "14px",
					align: "left",
					color: "#656563"
				})
				this.dialogView.show()
			} catch (e) {
				console.log(e, '权限说明弹框样式错误');
			}
		},
		//跳转到app权限设置页面
		gotoAppPermissionSetting() {
			if (!uni.getSystemInfoSync().platform == 'android') {
				var UIApplication = plus.ios.import("UIApplication");
				var application2 = UIApplication.sharedApplication();
				var NSURL2 = plus.ios.import("NSURL");
				// var setting2 = NSURL2.URLWithString("prefs:root=LOCATION_SERVICES");		
				var setting2 = NSURL2.URLWithString("app-settings:");
				application2.openURL(setting2);
 
				plus.ios.deleteObject(setting2);
				plus.ios.deleteObject(NSURL2);
				plus.ios.deleteObject(application2);
			} else {
				var Intent = plus.android.importClass("android.content.Intent");
				var Settings = plus.android.importClass("android.provider.Settings");
				var Uri = plus.android.importClass("android.net.Uri");
				var mainActivity = plus.android.runtimeMainActivity();
				var intent = new Intent();
				intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
				var uri = Uri.fromParts("package", mainActivity.getPackageName(), null);
				intent.setData(uri);
				mainActivity.startActivity(intent);
			}
		}
	}
})