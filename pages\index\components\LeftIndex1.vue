<template>
	<view class="container-box flex-sty1">
		<view class="container-top flex-sty1">
			<view class="">
				<view class="txt1">团队本月完成度</view>
				<view class="txt2" style="display: flex;align-items: center;">
					团队本月开户数:
					<text class="txt3">{{props.staticList.team_monthly_count_merchant}}</text>
					<text class="txt4">/{{props.staticList.team_monthly_tasks}}</text>
				</view>
			</view>
			<view class="progres" style="margin-top: 60rpx;">
				<l-circularProgress :fontShow="false" :boxWidth="boxWidth" :boxHeight="boxHeight" :percent="props.staticList.team_proportion" type="halfCircular" :lineWidth="10"
					progressColor="#1386FB" fontColor="#1386FB" bgColor="#EDF0F5">
					<view class="text">{{props.staticList.team_proportion}}%</view>
				</l-circularProgress>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, reactive, computed } from 'vue';
	
	const props = defineProps({
		staticList: {
			type: Object,
			default: {}
		}
	})

	const boxWidth = ref(140)
	const boxHeight = ref(70)
</script>

<style lang="scss" scoped>
	::v-deep .circular-container .circular-progress {
		z-index: 0;
	}

	.flex-sty1 {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
	}

	.container-box {
		width: 49%;
		height: 444rpx;

		.txt1 {
			height: 44rpx;
			font-weight: 600;
			font-size: 28rpx;
			color: #18191A;
			line-height: 44rpx;
			font-style: normal;
		}

		.container-top {
			width: 100%;
			height: 100%;
			box-sizing: border-box;
			padding: 22rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
			border-radius: 16rpx;
			border: 1rpx solid #EDF0F5;

			.txt2 {
				margin-top: 8rpx;
				width: 100%;
				height: 40rpx;
				font-weight: 500;
				font-size: 22rpx;
				color: $uni-secondary-color;
				line-height: 40rpx;
				font-style: normal;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			.txt3 {
				margin-left: 8rpx;
				margin-right: 4rpx;
				font-weight: 600;
				font-size: 24rpx;
				color: $uni-primary;
				font-style: normal;
			}

			.txt4 {
				font-weight: 500;
				font-size: 24rpx;
				color: $uni-base-color;
				font-style: normal;
			}
		}
		
		.text{
			font-weight: 600;
			font-size: 44rpx;
			color: $uni-primary;
			font-style: normal;
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%);
		}
	}
</style>