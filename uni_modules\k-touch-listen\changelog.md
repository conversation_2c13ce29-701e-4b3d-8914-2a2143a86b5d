## 1.0.2（2023-09-14）
更新使用文档：
引入本插件 然后使用，手动滑稽，教程简单吗？
<k-touch-listen @touchUp="touchUp" @touchDown="touchDown" @touchLeft="touchLeft" @touchRight="touchRight">
  <view>
   //您需要监听的区域的代码
  </view>
</k-touch-listen>
## 1.0.1（2023-09-14）
更新使用文档：
引入本插件 然后使用，手动滑稽，教程简单吗？
<k-touch-listen @touchUp="touchUp" @touchDown="touchDown" @touchLeft="touchLeft" @touchRight="touchRight">
  <view>
   //您需要监听的区域的代码
  </view>
</k-touch-listen>
## 1.0.0（2023-09-14）
首次提交
