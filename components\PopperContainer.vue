<template>
	<!-- 弹框套弹框时使用。以父元素为中心弹出 -->
	<u-popup :show="props.show" :round="10" mode="center" bgColor="#332e2d"
		:customStyle="{width: props.parentWidth,height: 0,right: 0,position: 'absolute',background: 'transparent'}">
		<view class="popup-class">
			<Title :title="props.title" :size="2" :closeIcon="true" @close="close" />
			<view class="bottom-container">
				<slot></slot>
				<view class="button-container">
					<slot name="button"></slot>
				</view>
			</view>
		</view>
	</u-popup>

</template>

<script lang="ts" setup>
	/**
	 * 提示弹框
	 * @description 提示弹框、小弹框
	 * @property {Boolean}		show	  打开、关闭的标记
	 * @property {String}			title		标题名字
	 * @property {String}		parentWidth		默认值：'100%'，中间弹出。右侧弹出时值为父元素的宽度，以父元素为中心弹出（如：398rpx）
	 * @event {Function} close  关闭事件
	 * @example <PopperContainer title="标题" parentWidth="398rpx" :show="isShow" @close="close"  ></Title>
	 */
	import Title from '@/components/Title.vue'
	const emit = defineEmits(['close'])

	const props = defineProps({
		show: {
			type: Boolean,
			default: false
		},
		/**右侧弹出时父元素的宽度，以父元素为中心弹出，弹框套弹框时使用*/
		parentWidth: {
			type: String,
			default: '100%'
		},
		right: {
			type: [String, Number],
			default: '0'
		},
		title:{
			type: String,
			default: ''
		}
	})

	function close() {
		emit('close', false)
	}
</script>

<style lang="scss" scoped>
	::v-deep .u-transition {
			z-index: 10075 !important;
		}

	.popup-class {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		background-color: $uni-bg-color-area-1;
		border-radius: 6rpx;
		padding: $uni-spacing-base $uni-spacing-lg;
		overflow: hidden;

		.bottom-container {
			margin-top: $uni-spacing-base;
			padding: $uni-spacing-base 0;
			position: relative;

			&:before {
				content: "";
				width: 120%;
				height: 1px;
				background-color: white;
				position: absolute;
				top: 0;
				left: -10%;
			}
			
			.button-container {
				width: 100%;
				display: flex;
				justify-content: space-around;
				margin-top: $uni-spacing-base;
			}
		}
	}
	
	::v-deep {
		.u-button__text {
			font-size: $uni-font-size-base !important;
		}
		.u-button {
			width: 50.8rpx;
			height: 14rpx;
			border-radius: 2.3rpx;
		}
	}
</style>