<template>
	<view class="my" @touchmove.stop.prevent="stopRoll">
		<up-popup :show="props.show" mode="center" round="10" @open="open">
			<view class="popup1">
				<view class="flex-sty2">
					<view class="" style="width: 44rpx;height: 44rpx;"> </view>
					<view class="popup1-txt1">驳回类型</view>
					<image @click="close" style="width:44rpx;height:44rpx;" src="@/static/images/close.png" mode=""></image>
				</view>
				<!-- 营业执照信息有误、法人信息有误、店铺场景照有误、结算卡信息有误、不予进件 -->
				<view class="popup1-txt2" v-if="props.merchantInfo.rejection_type == 1">营业执照信息有误</view>
				<view class="popup1-txt2" v-if="props.merchantInfo.rejection_type == 2">法人信息有误</view>
				<view class="popup1-txt2" v-if="props.merchantInfo.rejection_type == 3">店铺场景照有误</view>
				<view class="popup1-txt2" v-if="props.merchantInfo.rejection_type == 4">结算卡信息有误</view>
				<view class="popup1-txt2" v-if="props.merchantInfo.rejection_type == 5">不予进件</view>
				<view class="popup1-txt3">
					{{props.merchantInfo.reason_for_rejection}}
				</view>
				<view class="btn-box">
					<view class="btn1" v-if="props.merchantInfo.rejection_type == 5" @click="editData">确认</view>
					<view class="btn1" v-else @click="editData">修改资料</view>
				</view>
			</view>
		</up-popup>
	</view>
</template>

<script lang="ts" setup>
	import { watch } from 'vue';

	const emit = defineEmits(['close', 'editDataHandel'])

	const props = defineProps({
		show: {
			type: Boolean,
			default: false
		},
		merchantInfo: {
			type: Object,
			default: {}
		}
	})

	watch(() => props.show, (val) => {
		if (val) {
			document.body.style.overflow = 'hidden'; // 禁止页面滚动
		} else {
			document.body.style.overflow = 'auto'; // 恢复页面滚动
		}
	})
	
	function stopRoll(){
		
	}

	/** 按钮 */
	function editData() {
		emit('editDataHandel', props.merchantInfo)
	}

	/** 打开弹窗 */
	function open() {
		
	}

	function close() {
		emit('close', false)
	}
</script>

<style lang="scss" scoped>
	.popupShow {
		overflow: hidden;
		position: fixed;
	}

	.flex-sty2 {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.popup1 {
		width: 582rpx;
		min-height: 500rpx;
		max-height: 888rpx;
		// max-height: 888rpx !important;
		box-sizing: border-box;
		padding: 40rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
		border-radius: 24rpx;
		border: 1rpx solid #EDF0F5;

		.popup1-txt1 {
			height: 48rpx;
			font-weight: 600;
			font-size: 32rpx;
			color: $uni-main-color;
			line-height: 48rpx;
			text-align: center;
			font-style: normal;
		}

		.popup1-txt2 {
			margin-top: 40rpx;
			height: 44rpx;
			font-weight: 600;
			font-size: 28rpx;
			color: #F39237;
			line-height: 44rpx;
			font-style: normal;
		}

		.popup1-txt3 {
			margin-top: 16rpx;
			height: calc(100% - 48rpx - 44rpx - 40rpx - 120rpx - 16rpx);
			overflow-y: auto;
			font-weight: 400;
			font-size: 24rpx;
			color: #575E65;
			line-height: 40rpx;
			font-style: normal;
		}

		.btn-box {
			position: absolute;
			left: 0;
			bottom: 40rpx;
			width: 100%;
			height: 120rpx;
			background: #FFFFFF;
			box-shadow: 0rpx -2rpx 4rpx 0rpx rgba(75, 80, 85, 0.06);
			box-sizing: border-box;
			padding: 0 40rpx;
			display: flex;
			align-items: center;
			justify-content: flex-end;

			.btn1 {
				width: 100%;
				height: 84rpx;
				line-height: 84rpx;
				text-align: center;
				background: $uni-primary;
				border-radius: 8rpx;
				font-weight: 600;
				font-size: 28rpx;
				color: #FFFFFF;
				font-style: normal;
			}
		}
	}
</style>