<template>
	<up-popup mode="bottom" :round="16" :show="show" :closeOnClickOverlay="false" closeable @close="cancel">
		<scroll-view class="popup" scroll-y @scrolltolower="onReachBottom">
			<view class="title" style="text-align: center">选择开户支行</view>

			<!-- 省市选择 -->
			<view class="Province">
				<view class="list-flex">
					<!-- 选择省份 -->
					<view class="list" @click="showOptions('province')">
						<view class="text">
							<text class="txt" :style="{ fontWeight: selectedProvince ? '600' : '400', color: selectedProvince ? '#1b7de1' : '#575e65', fontSize: '28rpx' }">
								{{ selectedProvince?.area_name || '省/市/自治区…' }}
							</text>
							<image src="@/static/images/sanjiao.png" mode="aspectFit" style="width: 44rpx; height: 44rpx"></image>
						</view>
					</view>

					<!-- 选择城市 -->
					<view class="list" @click="showOptions('city')" :class="{ disabled: !selectedProvince }" style="margin-left: 16rpx">
						<view class="text">
							<text class="txt" :style="{ fontWeight: selectedCity ? '600' : '400', color: selectedCity ? '#1b7de1' : !selectedProvince ? '#ccc' : '#575e65', fontSize: '28rpx' }">
								{{ selectedCity?.area_name || '市' }}
							</text>
							<image src="@/static/images/sanjiao.png" mode="aspectFit" style="width: 44rpx; height: 44rpx"></image>
						</view>
					</view>
				</view>
			</view>

			<!-- 搜索框 -->
			<view class="search">
				<up-search placeholder="请输入支行名称搜索" searchIconSize="22" :showAction="false" :clearabled="false" v-model="searchQuery" :disabled="!isSearchEnabled"></up-search>
			</view>

			<!-- 动态显示省市选项或支行列表 -->
			<view class="bank-list">
				<template v-if="currentSelection === 'province'">
					<!-- 显示省份列表 -->
					<view
						v-for="(province, index) in provinces"
						:key="index"
						class="bank-item"
						@click="selectProvinceOption(province)"
						:style="{
							backgroundColor: selectedProvince === province ? '#e5f2ff' : '',
							fontWeight: selectedProvince === province ? '600' : '400',
							color: selectedProvince === province ? '#1b7de1' : '#575e65',
							fontSize: '28rpx'
						}"
					>
						<span>{{ province.area_name }}</span>
					</view>
				</template>
				<template v-else-if="currentSelection === 'city' && selectedProvince">
					<!-- 显示城市列表 -->
					<view
						v-for="(city, index) in selectedProvince.children"
						:key="index"
						class="bank-item"
						@click="selectCityOption(city)"
						:style="{
							backgroundColor: selectedCity === city ? '#e5f2ff' : '',
							fontWeight: selectedCity === city ? '600' : '400',
							color: selectedCity === city ? '#1b7de1' : '#575e65',
							fontSize: '28rpx'
						}"
					>
						<span>{{ city.area_name }}</span>
					</view>
				</template>
				<template v-else>
					<!-- 显示支行列表 -->
					<view
						v-for="(branch, index) in branchList"
						:key="index"
						class="bank-item"
						@click="confirmSelection(branch)"
						:style="{
							backgroundColor: selectedBank === branch ? '#e5f2ff' : '',
							fontWeight: selectedBank === branch ? '600' : '400',
							color: selectedBank === branch ? '#1b7de1' : '#575e65',
							fontSize: '28rpx'
						}"
					>
						<span>{{ branch.branch_name }}</span>
					</view>
					<!-- 加载更多状态 -->
					<view style="padding-top: 8rpx">
						<up-loadmore :status="status" />
					</view>
				</template>
			</view>
		</scroll-view>
	</up-popup>
</template>

<script lang="ts" setup>
import { ref, watch, computed, onMounted, onBeforeUnmount } from 'vue';
import { getProvince, getBranch } from '@/api/debitCard';

const emit = defineEmits(['closeHandel', 'confirm']);
const props = defineProps({
	show: {
		type: Boolean,
		default: false
	}
});

// 数据
const provinces = ref([]);
const branchList = ref([]); // 存储支行列表数据
const searchQuery = ref('');
const selectedProvince = ref(null);
const selectedCity = ref(null);
const selectedBank = ref(''); // 当前选中的银行
const currentSelection = ref('bank'); // 默认展示银行列表

// 加载更多的相关状态
const status = ref('loadmore');
const page = ref(1);
const limit = 20;

// 是否启用搜索框
const isSearchEnabled = computed(() => true);

// 获取省市数据
async function fetchBranchData() {
	const response = await getProvince();
	if (response.code === 200) {
		provinces.value = response.data; // response.data 省市结构
	} else {
		uni.utils.toast(response.msg);
	}
}

// 页面加载时调用获取数据
onMounted(() => {
	// 获取并展示省市数据
	fetchBranchData();
	// 获取本地存储的银行信息
	const storedBank = uni.getStorageSync('selectedBank');
	selectedBank.value = storedBank || ''; // 获取本地存储的银行信息
	if (!selectedBank.value) {
		// 如果 selectedBank 为空，获取默认的 20 条支行数据
		fetchDefaultBranchList();
	} else {
		// 如果 selectedBank 存在，加载与之相关的支行列表
		fetchBranchList();
	}
	// 启动定时器监听本地存储变化
	startBankCheckInterval();
});

/** 请求默认的 20 条支行数据 */
async function fetchDefaultBranchList() {
	status.value = 'loading'; // 设置加载状态
	const response = await getBranch('', '', '', '', 1, limit); // 请求默认支行列表
	if (response.code === 200) {
		branchList.value = response.data; // 加载数据到支行列表
		status.value = response.data.length < limit ? 'nomore' : 'loadmore'; // 判断是否为最后一页
	} else {
		uni.utils.toast(response.msg);
	}
}

// 定时检查存储的 selectedBank 是否变化
let checkBankInterval = null;
function startBankCheckInterval() {
	checkBankInterval = setInterval(() => {
		const storedBank = uni.getStorageSync('selectedBank');
		if (storedBank !== selectedBank.value) {
			selectedBank.value = storedBank; // 更新选中的银行
			selectedProvince.value = null;
			selectedCity.value = null;
			branchList.value = []; // 清空省市选项和支行列表
			console.log('selectedBank 发生变化，清除所有选项');
		}
	}, 1000); // 每1秒检查一次
}

// 组件销毁时清除定时器
onBeforeUnmount(() => {
	if (checkBankInterval) {
		clearInterval(checkBankInterval);
		checkBankInterval = null;
	}
});

// 监听搜索输入框内容变化
watch(searchQuery, () => {
	page.value = 1;
	branchList.value = []; // 重置支行列表
	fetchBranchList();
});
watch(
	() => props.show,
	(newVal) => {
		if (newVal) {
			const storedBank = uni.getStorageSync('selectedBank');
			if (storedBank) {
				selectedBank.value = storedBank;
				page.value = 1; // 重置页码
				branchList.value = []; // 重置支行列表
				searchQuery.value = '';
				fetchBranchList(); // 请求支行数据
			}
		}
	}
);

// 监听页面滚动到底部事件
function onReachBottom() {
	if (status.value === 'nomore') return;
	page.value += 1;
	fetchBranchList();
}

// 显示省或市的选项
function showOptions(level: string) {
	currentSelection.value = level;
}

// 选择省份
function selectProvinceOption(province: any) {
	selectedProvince.value = province;
	selectedCity.value = null;
	currentSelection.value = 'bank'; // 切换回支行列表
	page.value = 1; // 重置页码
	branchList.value = []; // 重置支行列表
	fetchBranchList(); // 更新支行列表
}

// 选择城市
function selectCityOption(city: any) {
	selectedCity.value = city;
	currentSelection.value = 'bank'; // 切换回支行列表
	page.value = 1; // 重置页码
	branchList.value = []; // 重置支行列表
	fetchBranchList(); // 更新支行列表
}

/** 关闭弹窗，触发父组件的取消逻辑 */
function cancel() {
	emit('closeHandel');
}

/** 请求开户支行数据 */
async function fetchBranchList() {
	const bankName = selectedBank.value;
	const provinceName = selectedProvince.value?.area_name || '';
	const cityName = selectedCity.value?.area_name || '';
	const keywords = searchQuery.value;
	status.value = 'loading'; // 设置加载状态

	const response = await getBranch(bankName, provinceName, cityName, keywords, page.value, limit);
	if (response.code === 200) {
		branchList.value = [...branchList.value, ...response.data]; // 将新数据追加到列表
		if (response.data.length < limit) {
			status.value = 'nomore'; // 没有更多数据
		} else {
			status.value = 'loadmore'; // 还有数据可以加载
		}
	} else {
		uni.utils.toast(response.msg);
	}
}

/** 选择并确认开户支行 */
function confirmSelection(branch: any) {
	emit('confirm', branch); // 传递选中的支行信息给父组件
	cancel(); // 选择后关闭弹窗
}
</script>

<style lang="scss" scoped>
::v-deep .u-popup__content {
	width: 100% !important;
	border-radius: 24rpx 24rpx 0rpx 0rpx !important;
}
::v-deep .u-search__content {
	height: 96rpx;
	border-radius: 8rpx !important;
}
.popup {
	height: 1024rpx;
	padding: 40rpx 32rpx;
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
}
.title {
	font-weight: 600;
	font-size: 34rpx;
	color: #575e65;
	line-height: 48rpx;
}
.search {
	margin-top: 32rpx;
}
.Province {
	margin-top: 24rpx;
	.list-flex {
		display: flex;
		.list {
			width: 334rpx;
			height: 96rpx;
			background: #F0F2F5;
			border-radius: 8rpx;
			cursor: pointer;
			.text {
				display: flex;
				align-items: center;
				padding: 26rpx 24rpx;
				justify-content: space-between;
				font-weight: 400;
				font-size: 28rpx;
				color: #575e65;
				&.selected {
					font-weight: 600;
					color: #1b7de1; /* 选中项的文字颜色 */
				}
			}
		}
	}
}
.bank-list {
	flex: 1;
	overflow-y: auto;
	margin-top: 32rpx;
	.bank-item {
		position: relative;
		padding: 20rpx 24rpx;
		border-radius: 8rpx;
		margin-bottom: 16rpx;
		cursor: pointer;
		white-space: normal; // 允许换行
		word-break: break-all; // 长单词或字符流可以换行
		line-height: 44rpx; // 设置适合的行高，保证内容间距

		&.selected {
			background-color: #e5f2ff;
		}

		.text {
			padding-left: 8rpx;
			font-weight: 400;
			font-size: 28rpx;
			color: #575e65;
		}

		&::after {
			content: '';
			position: absolute;
			bottom: -8rpx;
			left: 0;
			right: 0;
			height: 1px;
			background-color: #dee2e8;
		}
	}
}
</style>
