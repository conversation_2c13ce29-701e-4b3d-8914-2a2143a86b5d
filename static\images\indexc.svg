<svg version="1.1" id="圖層_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 100 100" enable-background="new 0 0 100 100" xml:space="preserve" style="margin: initial; display: block; shape-rendering: auto; background: transparent;" preserveAspectRatio="xMidYMid" width="256" height="256"><g class="ldl-scale" style="transform-origin: 50% 50%; transform: rotate(0deg) scale(0.8, 0.8);"><g class="ldl-ani" style="transform-box: view-box;">
<g class="ldl-layer"><g class="ldl-ani" style="transform-box: view-box; opacity: 1; transform-origin: 50px 80px; transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1); animation: 1s linear -0.8875s infinite normal forwards running animate;"><path fill="none" stroke="#666666" stroke-width="4" stroke-miterlimit="10" d="M50.6,49c0.3-12.1-2-17.7-11.8-19.7S25,18.5,25,18.5
	" style="stroke: rgb(102, 102, 102);"></path></g></g>
<g class="ldl-layer"><g class="ldl-ani" style="transform-box: view-box; opacity: 1; transform-origin: 50px 80px; transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1); animation: 1s linear -0.925s infinite normal forwards running animate;"><g>
	<g class="ldl-layer"><g class="ldl-ani"><path fill="#C33737" d="M88.9,81.5H11.1c-2,0-3.6-1.6-3.6-3.6V40.5c0-2,1.6-3.6,3.6-3.6h77.8c2,0,3.6,1.6,3.6,3.6v37.3
		C92.5,79.9,90.9,81.5,88.9,81.5z" style="fill: rgb(195, 55, 55);"></path></g></g>
	<g class="ldl-layer"><g class="ldl-ani"><path fill="#F5E6C8" d="M40.3,50.3v-8c0-1.3-1.1-2.4-2.4-2.4H12.8c-1.3,0-2.4,1.1-2.4,2.4V76c0,1.3,1.1,2.4,2.4,2.4h74.4
		c1.3,0,2.4-1.1,2.4-2.4V52.7c0-1.3-1.1-2.4-2.4-2.4H40.3z" style="fill: rgb(245, 230, 200);"></path></g></g>
</g></g></g>
<g class="ldl-layer"><g class="ldl-ani" style="transform-box: view-box; opacity: 1; transform-origin: 50px 80px; transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1); animation: 1s linear -0.9625s infinite normal forwards running animate;"><g>
	<g class="ldl-layer"><g class="ldl-ani"><line fill="none" stroke="#333333" stroke-width="5" stroke-miterlimit="10" x1="25.8" y1="53.7" x2="25.8" y2="71.8" style="stroke: rgb(51, 51, 51);"></line></g></g>
	<g class="ldl-layer"><g class="ldl-ani"><line fill="none" stroke="#333333" stroke-width="5" stroke-miterlimit="10" x1="16.8" y1="62.7" x2="34.8" y2="62.7" style="stroke: rgb(51, 51, 51);"></line></g></g>
</g></g></g>
<g class="ldl-layer"><g class="ldl-ani" style="transform-box: view-box; opacity: 1; transform-origin: 50px 80px; transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1); animation: 1s linear -1s infinite normal forwards running animate;"><g>
	<g class="ldl-layer"><g class="ldl-ani"><circle fill="#333333" cx="60.2" cy="65.1" r="5.3" style="fill: rgb(51, 51, 51);"></circle></g></g>
	<g class="ldl-layer"><g class="ldl-ani"><circle fill="#333333" cx="75.7" cy="65.1" r="5.3" style="fill: rgb(51, 51, 51);"></circle></g></g>
</g></g></g>
</g></g><style type="text/css">@keyframes animate { 0.00% {transform: translate(0.00px,0.00px) rotate(0.00deg) scale(1.00, 1.00) skew(0deg, 0.00deg) ;opacity: 1.00;}2.00% {animation-timing-function: cubic-bezier(0.20,0.39,0.50,0.97);transform: translate(0.00px,-1.08px) rotate(0.00deg) ;}36.00% {animation-timing-function: cubic-bezier(0.50,0.03,0.80,0.61);transform: translate(0.00px,-10.00px) rotate(0.00deg) ;}72.00% {transform: translate(0.00px,-0.12px) rotate(0.00deg) ;}74.00% {animation-timing-function: cubic-bezier(0.24,0.43,0.48,0.91);transform: translate(0.00px,0.00px) rotate(0.00deg) scale(1.00, 0.95) ;}86.00% {animation-timing-function: cubic-bezier(0.53,0.08,0.77,0.57);transform: translate(0.00px,0.00px) rotate(0.00deg) scale(1.00, 0.80) ;}100.00% {animation-timing-function: cubic-bezier(0.53,0.08,0.77,0.57);transform: translate(0.00px,0.00px) rotate(0.00deg) scale(1.00, 1.00) ;} }</style><!-- [ldio] generated by https://loading.io --></svg>