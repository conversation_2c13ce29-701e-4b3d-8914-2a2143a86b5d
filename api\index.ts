
/**工作台  开户统计*/
export const statisticsApi = () => uni.$http.get('tenant_salesman.statistics/index');

/** 团队业绩排名 */
export const teamPerApi = (type:any) => uni.$http.get(`tenant_salesman.statistics/teamPerformanceRanking?type=${type}`);

/** 代办事项 */
export const toDoItemsApi = (pageNum:any,pageSize:any) => uni.$http.get(`tenant_salesman.to_do_items/index?pageNum=${pageNum}&pageSize=${pageSize}`);

/** 获取商户详情 */
export const getMerchantApi = (merchant_id:any) => uni.$http.get(`tenant_salesman.to_do_items/getMerchant?merchant_id=${merchant_id}`);

/** 电子签约 */
export const signApi = (data: any) => uni.$http.post('tenant_salesman.to_do_items/sign', data);

/** 绑定设备 */
export const bindDeviceApi = (data: any) => uni.$http.post('tenant_salesman.to_do_items/bindDevice', data);