<template>
  <up-popup :show="show" :round="10" mode="bottom">
    <view class="popup4">
      <view class="flex-sty">
        <view class="" style="width: 44rpx; height: 44rpx"></view>
        <view class="popup4-txt1">编辑图片</view>
        <image @click="closePopup" style="width: 44rpx; height: 44rpx" src="@/static/images/close.png" mode=""></image>
      </view>
      <view @click="deleteImage" class="popup4-txt2 margin-sty">删除</view>
      <view @click="reuploadImage" class="popup4-txt2">重新上传</view>
    </view>
  </up-popup>
</template>

<script setup>
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(['close', 'delete', 'reupload']);

const closePopup = () => {
  emits('close');
};

const deleteImage = () => {
  emits('delete');
};

const reuploadImage = () => {
  emits('reupload');
};
</script>

<style lang="scss" scoped>
.flex-sty {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.popup4 {
  width: 100%;
  height: 342rpx;
  background: #ffffff;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  box-sizing: border-box;
  padding: 40rpx 32rpx;

  .popup4-txt1 {
    font-weight: 600;
    font-size: 34rpx;
    color: $uni-base-color;
    font-style: normal;
  }

  .margin-sty {
    margin-top: 24rpx;
    color: #f73429;
    border-bottom: 1rpx solid #dee2e8;
  }

  .popup4-txt2 {
    width: 100%;
    height: 96rpx;
    line-height: 96rpx;
    text-align: center;
    font-weight: 500;
    font-size: 32rpx;

    font-style: normal;
  }
}
</style>
