<template>
	<view class="container">
		<!-- 状态栏占位 -->
		<view class="status-bar"></view>

		<!-- 背景区域 -->
		<view class="bg-gradient"></view>

		<!-- 内容区域 -->
		<view class="business-page">
			<view class="business-header">业务</view>

			<!-- 交易记录卡片 -->
			<view class="transaction-card">
				<view class="card-header">
					<text class="title">交易记录</text>
					<view class="view-all" @click="jumpTo('/pages/business/transaction')">
						查看全部
						<image src="@/static/images/rightTwo-icon.png" class="right-icon" mode="aspectFit"></image>
					</view>
				</view>

				<view class="amount-section">
					<view class="label">直属商家当月交易额</view>
					<view class="amount">
						<text class="currency">¥</text>
						<text class="number">{{ format.money2(monthlyAmount) }}</text>
					</view>
				</view>

				<!-- 分割线 -->
				<view style="height: 1rpx; background: #3296FC; margin-top: 18rpx; margin-bottom: 18rpx;"></view>

				<view class="yearly-amount">
					今年商家交易总额: <text class="currencyTwo">¥{{ format.money2(yearlyAmount) }}</text>
				</view>
			</view>

			<!-- 商家管理模块 -->
			<view class="merchant-section">
				<view class="section-header">
					<text class="title">商家管理</text>
					<view class="merchant-list" @click="jumpTo('/pages/business/merchant')">
						商家列表
						<image src="@/static/images/right-icon.png" class="right-icon" mode="aspectFit"></image>
					</view>
				</view>

				<view class="stats-grid">
					<view class="stat-item" @click="jumpTo('/pages/business/merchant')">
						<text class="label">商家数</text>
						<text class="number">{{ merchantStats.merchant_count }}</text>
					</view>
					<view class="stat-item" @click="jumpTo('/pages/business/merchant')">
						<text class="label">进件中</text>
						<text class="number">{{ merchantStats.incoming }}</text>
					</view>
					<view class="stat-item" @click="jumpTo('/pages/business/monthlyNew')">
						<text class="label">当月新增</text>
						<text class="number">{{ merchantStats.current_month_new }}</text>
					</view>
				</view>

				<view class="merchant-list">
					<view class="list-title">最新商家</view>
					<view class="merchant-item-list">
						<view v-for="(merchant, index) in recentMerchants" :key="merchant.merchant_id">
							<view class="merchant-item">
								<view class="merchant-info">
									<view class="info-row">
										<!-- 修改进件中状态判断 -->
										<view class="status" v-if="merchant.to_do_items_type >= 0 || merchant.to_do_items_type == -1">
											进件中</view>
										<view class="name">{{ truncateName(merchant.bse_lice_nm) }}</view>
									</view>
									<view class="contact">{{ merchant.s_legal_name }} {{ merchant.s_legal_phone }}</view>
								</view>
								<view class="action" @click="handleMerchantClick(merchant)">
									{{ merchant.to_do_items_type >= 0 || merchant.to_do_items_type == -1 ? '去进件' : '商家详情' }}
									<image src="@/static/images/right-icon.png" class="right-icon" mode="aspectFit">
									</image>
								</view>
							</view>
							<view class="divider" v-if="index != recentMerchants.length - 1"></view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 添加驳回弹窗 -->
		<!-- <PopperReject :show="showRejectPopper" :merchantInfo="currentMerchant" @close="showRejectPopper = false"
			@editDataHandel="handleEditData" /> -->

		<!-- 添加签约弹窗 -->
		<!-- <PopperSigning :show="showSigningPopper" :signCode="signCode" @close="showSigningPopper = false" /> -->
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { businessApi } from '@/api/business'
import format from '@/utils/format.ts'
import PopperReject from '@/pages/index/components/PopperReject.vue'
import PopperSigning from '@/pages/index/components/PopperSigning.vue'

// 页面跳转
function jumpTo (url) {
	uni.navigateTo({
		url: url
	});
}

// 交易数据
const monthlyAmount = ref('0.00')
const yearlyAmount = ref('0.00')

// 商家统计
const merchantStats = ref({
	merchant_count: 0,
	incoming: 0,
	current_month_new: 0
})

// 最新商家列表
const recentMerchants = ref([])

// 弹窗控制
const showRejectPopper = ref(false)
const showSigningPopper = ref(false)
const currentMerchant = ref({})
const signCode = ref('')

// 获取业务首页数据
async function getBusinessData () {
	try {
		const res = await businessApi()
		if (res.code == 200) {
			const data = res.data

			// 更新交易数据
			monthlyAmount.value = formatAmount(data.current_month_pay)
			yearlyAmount.value = formatAmount(data.current_year_pay)

			// 更新商家统计
			merchantStats.value = {
				merchant_count: data.merchant_count,
				incoming: data.incoming,
				current_month_new: data.current_month_new
			}

			// 更新最新商家列表
			recentMerchants.value = data.latest_merchant_list.map(item => ({
				id: item.id,
				merchant_id: item.merchant_id,
				bse_lice_nm: item.bse_lice_nm || '未命名商家',
				s_legal_name: item.s_legal_name || '未设置',
				s_legal_phone: formatPhone(item.s_legal_phone),
				save_page: item.save_page,
				to_do_items_type: item.to_do_items_type
			}))
		}
	} catch (error) {
		console.error('获取业务数据失败:', error)
		uni.showToast({
			title: '获取数据失败',
			icon: 'none'
		})
	}
}

// 格式化金额
function formatAmount (amount) {
	return amount.toLocaleString('zh-CN', {
		minimumFractionDigits: 2,
		maximumFractionDigits: 2
	})
}

// 格式化电话号码
function formatPhone (phone) {
	if (!phone) return ''
	return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')
}

// 页面加载时获取数据
onMounted(() => {
	getBusinessData()
})

// 处理商家名称，超过11个字符的加上省略号
function truncateName (name) {
	return name.length > 11 ? name.slice(0, 11) + '...' : name;
}

// 处理商家点击
function handleMerchantClick (merchant) {
	const merchant_id = merchant.id
	uni.setStorageSync('merchant_id', merchant_id)

	// // 判断是否显示弹窗
	// if (merchant.to_do_items_type == 4) {
	// 	// 显示驳回弹窗
	// 	currentMerchant.value = merchant
	// 	showRejectPopper.value = true
	// 	return
	// } else if (merchant.to_do_items_type == 5) {
	// 	// 显示签约弹窗
	// 	signCode.value = merchant.sign_code || '' // 假设签约二维码字段为 sign_code
	// 	showSigningPopper.value = true
	// 	return
	// }

	if (merchant.to_do_items_type == 4) {
		uni.navigateTo({
			url: '/pages/accomplish/accomplish?id=1'
		})
		return
	} else if (merchant.to_do_items_type == 5) {
		uni.navigateTo({
			url: '/pages/accomplish/accomplish?id=1'
		})
		return
	} else if (merchant.to_do_items_type == -1) {
		uni.navigateTo({
			url: '/pages/accomplish/accomplish?id=1'
		})
		return
	}

	// 其他状态的跳转逻辑保持不变
	if (merchant.to_do_items_type >= 0) {
		switch (merchant.save_page) {
			case 1:
				uni.navigateTo({
					url: '/pages/solutionSelection/solutionSelection?merchant_id=' + merchant_id
				})
				break
			case 2:
				uni.navigateTo({
					url: '/pages/identityCard/identityCard?merchant_id=' + merchant_id
				})
				break
			case 3:
				uni.navigateTo({
					url: '/pages/corporateIdentityCard/corporateIdentityCard?merchant_id=' + merchant_id
				})
				break
			case 4:
				uni.navigateTo({
					url: '/pages/shopScene/shopScene?merchant_id=' + merchant_id
				})
				break
			case 5:
				uni.navigateTo({
					url: '/pages/debitCard/debitCard?merchant_id=' + merchant_id
				})
				break
			default:
				// 如果没有进度，默认跳转到第一步
				uni.navigateTo({
					url: '/pages/solutionSelection/solutionSelection?merchant_id=' + merchant_id
				})
		}
	} else {
		jumpTo('/pages/business/merchantDetail?merchant_id=' + merchant.merchant_id)
	}
}

// 处理修改资料
function handleEditData (merchantInfo) {
	showRejectPopper.value = false
	// 根据驳回类型跳转到对应页面
	const merchant_id = merchantInfo.id
	switch (merchantInfo.rejection_type) {
		case 1: // 营业执照信息有误
			uni.navigateTo({
				url: '/pages/solutionSelection/solutionSelection?merchant_id=' + merchant_id
			})
			break
		case 2: // 法人信息有误
			uni.navigateTo({
				url: '/pages/identityCard/identityCard?merchant_id=' + merchant_id
			})
			break
		case 3: // 店铺场景照有误
			uni.navigateTo({
				url: '/pages/shopScene/shopScene?merchant_id=' + merchant_id
			})
			break
		case 4: // 结算卡信息有误
			uni.navigateTo({
				url: '/pages/debitCard/debitCard?merchant_id=' + merchant_id
			})
			break
		case 5: // 不予进件，关闭弹窗即可
			break
	}
}

onShow(() => {
	getBusinessData()
})

</script>

<style lang="scss" scoped>
.container {
	// min-height: 100vh;
	background: #F0F2F5;
	position: relative;
}

// 添加状态栏高度
.status-bar {
	height: var(--status-bar-height);
	width: 100%;
	position: relative;
	z-index: 2;
}

.bg-gradient {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 272rpx;
	background: linear-gradient(180deg, #B3D8FF 0%, #F0F2F5 100%);
	z-index: 1;
}

.business-page {
	position: relative;
	z-index: 2;
	padding: 32rpx;
	// min-height: 100vh;

	.business-header {
		font-weight: 600;
		font-size: 44rpx;
		color: #18191A;
		line-height: 60rpx;
	}
}

.transaction-card {
	margin-top: 24rpx;
	background: #1087FF;
	border-radius: 16rpx;
	border: 2rpx solid #3296FC;
	padding: 32rpx 24rpx;
	margin-bottom: 24rpx;

	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 24rpx;

		.title {
			font-weight: 600;
			font-size: 34rpx;
			color: #FFFFFF;
			line-height: 48rpx;
		}

		.view-all {
			font-weight: 400;
			font-size: 24rpx;
			color: #B3D8FF;
			line-height: 36rpx;
			display: flex;
			align-items: center;

			.right-icon {
				margin-left: 4rpx;
				opacity: 0.9;
			}
		}
	}

	.amount-section {
		margin-bottom: 24rpx;

		.label {
			font-weight: 400;
			font-size: 28rpx;
			color: #E5F2FF;
			line-height: 40rpx;
		}

		.amount {
			.currency {
				font-weight: 600;
				font-size: 30rpx;
				color: #FFFFFF;
				line-height: 40rpx;
				margin-right: 4rpx;
			}

			.number {
				font-weight: 600;
				font-size: 44rpx;
				color: #FFFFFF;
				line-height: 66rpx;
			}
		}
	}

	.yearly-amount {
		display: flex;
		align-items: center;
		font-weight: 400;
		font-size: 26rpx;
		color: #E5F2FF;
		line-height: 40rpx;

		.currencyTwo {
			font-weight: 500;
			font-size: 30rpx;
			color: #FFFFFF;
			margin-left: 6rpx;
			line-height: 40rpx;
		}
	}
}

.merchant-section {
	background: #fff;
	border-radius: 16rpx;
	padding: 32rpx 24rpx;
	box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.06);

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;

		.title {
			font-weight: 600;
			font-size: 34rpx;
			color: #18191A;
			line-height: 48rpx;
		}

		.merchant-list {
			font-weight: 400;
			font-size: 24rpx;
			color: #979B9F;
			line-height: 36rpx;
			display: flex;
			align-items: center;

			.right-icon {
				margin-left: 4rpx;
			}
		}
	}

	.stats-grid {
		display: flex;
		background-color: #f6f8fc;
		padding: 18rpx 24rpx;
		border-radius: 12rpx;

		.stat-item {
			flex: 1;

			.number {
				display: block;
				font-weight: 600;
				font-size: 34rpx;
				color: #1386FB;
				line-height: 48rpx;
			}

			.label {
				font-weight: 400;
				font-size: 26rpx;
				color: #575E65;
				line-height: 40rpx;
			}
		}
	}

	.merchant-list {
		.list-title {
			font-weight: 500;
			font-size: 28rpx;
			color: #18191A;
			line-height: 44rpx;
			margin-top: 24rpx;
		}

		.merchant-item-list {
			background: #F6F8FC;
			border-radius: 12rpx;
			margin-top: 16rpx;

			.merchant-item {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;
				padding: 24rpx 16rpx;

				.merchant-info {
					display: flex;
					flex-direction: column;

					.info-row {
						display: flex;
						align-items: center;

						.status {
							display: inline-flex;
							align-items: center;
							font-size: 24rpx;
							color: #FFFFFF;
							background: #F39237;
							padding: 4rpx 12rpx;
							border-radius: 6rpx;
							margin-right: 10rpx;
							line-height: 36rpx;
							font-weight: 400;
							height: 36rpx;
						}

						.name {
							font-weight: 500;
							font-size: 28rpx;
							color: #18191A;
							line-height: 44rpx;
							display: inline-flex;
							align-items: center;
							height: 44rpx;
						}
					}

					.contact {
						font-size: 26rpx;
						color: #979B9F;
						font-weight: 400;
						line-height: 40rpx;
						margin-top: 4rpx;
					}
				}

				.action {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 24rpx;
					color: #979B9F;
					line-height: 36rpx;

					.right-icon {
						width: 44rpx;
						height: 44rpx;
						margin-left: 8rpx;
					}
				}
			}

			.divider {
				height: 1rpx;
				background: #DEE2E8;
				margin: 0 16rpx; // 左右16rpx的边距
			}
		}
	}
}

.right-icon {
	width: 44rpx;
	height: 44rpx;
	line-height: 44rpx;
}
</style>