<template>
	<up-popup mode="bottom" :round="16" :show="show" :closeOnClickOverlay="false" closeable @close="cancel">
		<view class="popup">
			<view class="title" style="text-align: center">选择银行</view>
			<view class="search">
				<up-search placeholder="请输入名称搜索" searchIconSize="22" :showAction="false" :clearabled="false" v-model="searchQuery"></up-search>
			</view>
			<view class="bank-list">
				<view
					v-for="(bank, index) in filteredBanks"
					:key="index"
					:class="{ 'bank-item': true, selected: selectedBank === bank.bank_name }"
					@click="confirmSelection(bank.bank_name)"
				>
					<span class="text" :class="{ 'selected-text': selectedBank === bank.bank_name }">{{ bank.bank_name }}</span>
				</view>
			</view>
		</view>
	</up-popup>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue';
import { getBankBranch } from '@/api/debitCard';

const emit = defineEmits(['closeHandel', 'confirm']);
const props = defineProps({
	show: {
		type: Boolean,
		default: false
	}
});

const banks = ref([]); // 从后台获取的银行数据
const selectedBank = ref(''); // 当前选中的开户行
const searchQuery = ref(''); // 搜索框输入
let debounceTimeout: any;
const filteredBanks = ref([]); // 显示的银行列表

// 获取银行数据
async function fetchBanks(keywords = '') {
	const response = await getBankBranch(keywords);
	if (response.code === 200) {
		banks.value = response.data;
		filteredBanks.value = response.data; // 显示所有或匹配的银行
	}
}

// 初次加载时获取所有银行
onMounted(() => {
	fetchBanks(); // 默认传入空字符串，获取全部银行
});

// 监听搜索输入框，在输入停止后0.5秒更新银行列表
watch(searchQuery, (newQuery) => {
	clearTimeout(debounceTimeout);
	debounceTimeout = setTimeout(() => {
		performSearch(newQuery);
	}, 500);
});
// 执行模糊搜索
function performSearch(query) {
	fetchBanks(query); // 根据输入的关键词获取银行数据
}
/** 关闭弹窗，触发父组件的取消逻辑 */
function cancel() {
	emit('closeHandel'); // 通知父组件关闭弹窗
}

/** 选择并确认开户行 */
function confirmSelection(bankName) {
	selectedBank.value = bankName;
	emit('confirm', bankName); // 传递选中的开户行给父组件
	cancel(); // 选择后关闭弹窗
}
</script>

<style lang="scss" scoped>
::v-deep .u-popup__content {
	width: 100% !important;
	border-radius: 24rpx 24rpx 0rpx 0rpx !important;
}
::v-deep .u-search__content {
	height: 96rpx;
	border-radius: 8rpx !important;
}
.popup {
	height: 1024rpx;
	padding: 40rpx 32rpx;
	display: flex;
	flex-direction: column;
}
.title {
	font-weight: 600;
	font-size: 34rpx;
	color: #575e65;
	line-height: 48rpx;
}
.search {
	margin-top: 32rpx;
}
.bank-list {
	flex: 1;
	overflow-y: auto;
	margin-top: 32rpx;
	.bank-item {
		position: relative;
		height: 100rpx;
		line-height: 100rpx;
		padding: 0rpx 24rpx;
		border-radius: 8rpx;
		margin-bottom: 16rpx;
		&.selected {
			background-color: #e5f2ff;
		}
		.text {
			padding-left: 8rpx;
			font-weight: 400;
			font-size: 28rpx;
			color: #575e65;
			&.selected-text {
				font-weight: 600;
				color: #1b7de1;
			}
		}
		&::after {
			content: '';
			position: absolute;
			bottom: -8rpx;
			left: 0;
			right: 0;
			height: 1px;
			background-color: #dee2e8;
		}
	}
}
</style>
