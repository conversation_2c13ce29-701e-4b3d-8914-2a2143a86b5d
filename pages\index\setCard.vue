<!-- 更换结算卡 -->
<template>
  <view class="page-setCard-container">
    <!-- 导航栏 -->
    <navbar title="商户" />
    <view class="setCard-con-t">
      <view class="flex-sty2">
        <image style="width: 72rpx; height: 72rpx; margin-right: 24rpx" src="@/static/images/icon7.png" mode=""></image>
        <view class="setCard-con-t-r flex-sty1">
          <view class="flex-sty">
            <view class="setCard-txt1">{{ merchantList.store_name }}</view>
            <view class="setCard-txt2">{{ merchantList.merchant_type ? merchantList.merchant_type : '小微' }}</view>
          </view>
          <view class="flex-sty2">
            <image style="width: 22rpx; height: 28rpx" src="@/static/images/icon2.png" mode=""></image>
            <view class="setCard-txt3">
              {{ merchantList.store_contacts }}
              <text class="setCard-txt4" v-if="merchantList.store_tel">{{ merchantList.store_tel.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3') }}</text>
            </view>
          </view>
          <view class="flex-sty2">
            <image style="width: 32rpx; height: 32rpx" src="@/static/images/map.png" mode=""></image>
            <view class="setCard-txt5">{{ merchantList.merchant_address }}</view>
            <!-- <view class="setCard-txt5" v-if="merchantList.merArea">{{getAddressFromCodeString(merchantList.merArea)}}{{merchantList.merchant_address}}</view> -->
          </view>
        </view>
      </view>
    </view>
    <view class="setCard-txt6">商户详情</view>
    <view class="setCard-con-b">
      <view class="setCard-txt7">支付详情</view>
      <view class="setCard-txt8 flex-sty2">
        <view>费率模版</view>
        <view class="setCard-txt9">{{ merchantList.rate_template_name }}</view>
      </view>
      <view class="setCard-txt8 flex-sty2">
        <view>活动报名</view>
        <view class="setCard-txt9">{{ merchantList.activity_name ? merchantList.activity_name : '无活动' }}</view>
      </view>
      <view class="setCard-txt8 flex-sty2">
        <view>所属行业</view>
        <view class="setCard-txt9">{{ merchantList.industry_involved }}</view>
      </view>
      <view class="setCard-txt8 flex-sty2">
        <view>结算类型</view>
        <view class="setCard-txt9">{{ merchantList.settlement_account_type }}</view>
      </view>
      <view class="setCard-txt10" v-if="merchantList.merchant_img_door_shot">商户图片</view>
      <view class="img-box" v-if="merchantList.merchant_img_door_shot">
        <image class="img1" :src="baseURL + merchantList.merchant_img_door_shot" mode=""></image>
      </view>
      <view class="setCard-txt10 flex-sty2" v-else>
        <view>商户图片</view>
        <view class="setCard-txt9">无</view>
      </view>
    </view>
    <!-- 设备信息 -->
    <view class="" v-if="merchantList.device && merchantList.device.length > 0">
      <view class="setCard-txt6">设备信息</view>
      <view class="setCard-con-b-b" v-for="(item, index) in merchantList.device" :key="item.id">
        <view class="setCard-txt8 flex-sty2">
          <view class="txt1">设备类型</view>
          <view class="setCard-txt9">{{ item.device_type }}- {{ item.model }}</view>
        </view>
				<view class="setCard-txt8 flex-sty2" v-if="item.device_sn">
				  <view class="txt1">设备SN</view>
				  <view class="setCard-txt9">{{ item.device_sn }}</view>
				</view>
				<view class="setCard-txt8 flex-sty2" v-else>
				  <view class="txt1">设备二维码</view>
				  <view class="setCard-txt9">{{ item.codes }}</view>
				</view>
        <view class="setCard-txt8 flex-sty2">
          <view class="txt1">绑定时间</view>
          <view class="setCard-txt9">{{ item.create_time }}</view>
        </view>
        <view class="setCard-txt8 flex-sty2">
          <view class="txt1">设备状态</view>
          <view class="setCard-txt9">{{ item.status_name }}</view>
        </view>
      </view>
    </view>
    <view class="" style="height: 168rpx"></view>

    <!-- 按钮 -->
    <view class="btn-box">
      <view class="btn1" @click="clickBind">绑定设备</view>
      <view class="btn1" @click="jumpTo">更换结算卡</view>
    </view>

    <!-- 绑定设备 -->
    <up-popup :show="show" mode="center" :round="10" closeable @close="show = false" :closeOnClickOverlay="false">
      <view class="popup1">
        <view class="flex-sty">
          <view class="" style="width: 44rpx;"></view>
          <view class="txt1">绑定设备</view>
					<view class="" style="width: 44rpx;"></view>
        </view>
				<view class="txt3" style="margin-top: 16rpx;">请扫码或输入</view>
				<view class="txt3">设备收款码ID、设备SN号绑定</view>
				<view class="" style="margin-top: 32rpx;">
					<up-input v-model="code" placeholder="请输入收款码号或设备SN号" border="surround">
						<template #suffix>
							<image @click="scanHandel" src="@/static/images/saoyisao.png" mode="aspectFit" style="width: 44rpx; height: 44rpx"></image>
						</template>
					</up-input>
				</view>
        <view class="btn1" :class="code ? '' : 'btn2'" @click="okHandel">确定</view>
      </view>
    </up-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, inject } from 'vue';
import qrcode from '@/utils/qrcode.js';
import useUserStore from '@/store/user';
import navbar from '@/components/navbar/navbar.vue';
import { areaList } from '@/utils/wangpu_address.js';
import { getMerchantApi, bindDeviceApi } from '@/api/index';

const baseURL = inject('baseURL');
const userStore = useUserStore();

/** 商户id */
let merchant_id = ref(null);

// 根据地址码和地址列表获取文字地址
function getAddressText(addressCode: any, areas: any) {
  for (let area of areas) {
    if (area.value === addressCode) {
      return area.text;
    }
    if (area.children) {
      const text = getAddressText(addressCode, area.children);
      if (text) {
        return text;
      }
    }
  }
  return null;
}

// 根据地址字符串获取文字地址
function getAddressFromCodeString(addressString: any) {
  const addressCodes = addressString.split(',');
  let textAddress = addressCodes.map((code) => getAddressText(code, areaList));
  return textAddress.join('');
}

/** 商户详情 */
let merchantList = ref({});

/** 获取商户详情 */
async function otgetMerchantApi() {
  const res = await getMerchantApi(merchant_id.value);
  merchantList.value = res.data;
}

/** 跳转结算卡页面 */
function jumpTo() {
  uni.navigateTo({
    url: '/pages/replaceDebitCard/replaceDebitCard'
  });
}

let show = ref(false);
let code = ref('');
/** 绑定设备按钮 */
function clickBind() {
  code.value = '';
  show.value = true;
}
/** 扫一扫按钮 */
function scanHandel() {
  /*#ifdef APP-PLUS*/
  uni.scanCode({
    success: async function (res) {
			 if (res.result.startsWith(`https://dev.server.keyupay.com/`) || res.result.startsWith(`https://server.keyupay.com/`)) {
				 res.result = res.result + '&m=1';
				 await request(res.result);
			 }else{
				 code.value = res.result
			 }
    }
  });
  /*#endif*/

  /*#ifdef H5*/
  scanCode();
  /*#endif*/
}
/** h5的扫码 */
function scanCode() {
  // 调用uni提供的调用相机api
  uni.chooseImage({
    sizeType: ['original'],
    sourceType: ['camera'],
    count: 1,
    success: function (res) {
      const tempFilePaths = res.tempFilePaths[0]; // 获取到二维码图片的链接
      qrcode.decode(tempFilePaths); // 解析二维码图片
      qrcode.callback = async function (res1) {
        // 解析成功
        if (res1 == 'error decoding QR Code') {
          uni.utils.toast('识别二维码失败，请重新上传');
        }else if (res1.startsWith(`https://dev.server.keyupay.com/`) || res1.startsWith(`https://server.keyupay.com/`)) {
					// 解析成功返回二维码链接
					res1 = res1 + '&m=1';
					await request(res1)
				} else {
          // 解析成功返回二维码链接
					code.value = res1;
        }
      };
    },
    fail: () => {
      uni.utils.toast('识别二维码失败，请重新上传');
    }
  });
}

/** 请求接口 */
function request(url) {
  uni.request({
    url: url,
    header: {
      token: userStore.token || ''
    },
    success: (res) => {
      if (res.data.code == 200) {
        code.value = res.data.data.code_data;
      } else {
        uni.utils.toast(res.data.msg);
      }
    }
  });
}

/** 确定按钮 */
async function okHandel() {
  if (!code.value) return uni.utils.toast('请填写设备信息');
  const data = {
    sn: code.value,
    store_id: merchantList.value.store_id,
    merchant_id: merchantList.value.merchant_id
  };
  const res = await bindDeviceApi(data);
  if (res.code == 200) {
    uni.utils.toast(res.msg);
    setTimeout(() => {
      show.value = false;
      otgetMerchantApi();
    }, 1000);
  } else {
    uni.utils.toast(res.msg);
  }
}

onMounted(() => {
  merchant_id.value = uni.getStorageSync('merchant_id');
  otgetMerchantApi();
});
</script>

<style lang="scss" scoped>
.txt1{
	width: 140rpx;
}

::v-deep .u-input {
  height: 96rpx;
  box-sizing: border-box;
  background: #FFFFFF;
  border-radius: 8rpx;
  border: none;
}

::v-deep .uni-input-placeholder{
	font-weight: 400;
	font-size: 26rpx;
	color: #9DA5AD;
}

::v-deep .u-icon__icon{
	color: #9DA5AD !important;
}

.flex-sty {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-sty1 {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.flex-sty2 {
  display: flex;
  align-items: center;
}

.page-setCard-container {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 32rpx;

  .setCard-con-t {
    width: 100%;
    height: 220rpx;
    background: #ffffff;
    box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
    border-radius: 16rpx;
    border: 1rpx solid #edf0f5;
    box-sizing: border-box;
    padding: 32rpx;

    .setCard-con-t-r {
      width: calc(100% - 72rpx - 24rpx);
      height: 100%;
    }

    .setCard-txt1 {
      width: 80%;
      height: 44rpx;
      font-weight: 600;
      font-size: 30rpx;
      color: $uni-main-color;
      line-height: 44rpx;
      font-style: normal;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .setCard-txt2 {
      width: 128rpx;
      height: 48rpx;
      line-height: 48rpx;
      background: #F0F2F5;
      border-radius: 2rpx;
      border: 1rpx solid #dee2e8;
      text-align: center;
      font-weight: 400;
      font-size: 24rpx;
      color: $uni-base-color;
      font-style: normal;
    }

    .setCard-txt3 {
      margin-left: 8rpx;
      margin-right: 16rpx;
      height: 40rpx;
      font-weight: 500;
      font-size: 24rpx;
      color: $uni-main-color;
      line-height: 40rpx;
      font-style: normal;
    }

    .setCard-txt4 {
      height: 40rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: $uni-main-color;
      line-height: 40rpx;
      font-style: normal;
    }

    .setCard-txt5 {
      margin-left: 8rpx;
      width: 90%;
      height: 40rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: $uni-main-color;
      line-height: 40rpx;
      font-style: normal;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .setCard-txt6 {
    margin-top: 56rpx;
    margin-bottom: 24rpx;
    height: 48rpx;
    font-weight: 600;
    font-size: 34rpx;
    color: $uni-main-color;
    line-height: 48rpx;
    font-style: normal;
  }

  .setCard-con-b {
    width: 100%;
    height: 766rpx;
    background: #ffffff;
    box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
    border-radius: 16rpx;
    border: 1rpx solid #edf0f5;
    box-sizing: border-box;
    padding: 40rpx 32rpx;

    .setCard-txt7 {
      height: 68rpx;
      font-weight: 500;
      font-size: 30rpx;
      color: $uni-main-color;
      line-height: 68rpx;
      font-style: normal;
      border-bottom: 1rpx solid #d8d8d8;
    }

    .setCard-txt8 {
      width: 100%;
      height: 76rpx;
      line-height: 76rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: $uni-base-color;
      font-style: normal;
    }

    .setCard-txt9 {
      margin-left: 32rpx;
      width: 70%;
      font-weight: 400;
      font-size: 28rpx;
      color: $uni-main-color;
      font-style: normal;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .setCard-txt10 {
      margin-top: 32rpx;
      margin-bottom: 16rpx;
      height: 44rpx;
      line-height: 44rpx;
      font-weight: 500;
      font-size: 30rpx;
      color: $uni-main-color;
      font-style: normal;
    }

    .img-box {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .img1 {
        width: 48%;
        height: 200rpx;
        margin-top: 24rpx;
      }
    }
  }

  .setCard-con-b-b {
    width: 100%;
    height: 380rpx;
    background: #ffffff;
    box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
    border-radius: 16rpx;
    border: 1rpx solid #edf0f5;
    box-sizing: border-box;
    padding: 40rpx 32rpx;
    margin-bottom: 20rpx;

    .setCard-txt7 {
      height: 68rpx;
      font-weight: 500;
      font-size: 30rpx;
      color: $uni-main-color;
      line-height: 68rpx;
      font-style: normal;
      border-bottom: 1rpx solid #d8d8d8;
    }

    .setCard-txt8 {
      width: 100%;
      height: 76rpx;
      line-height: 76rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: $uni-base-color;
      font-style: normal;
    }

    .setCard-txt9 {
      margin-left: 32rpx;
      width: 70%;
      font-weight: 400;
      font-size: 28rpx;
      color: $uni-main-color;
      font-style: normal;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .setCard-txt10 {
      margin-top: 32rpx;
      margin-bottom: 16rpx;
      height: 44rpx;
      line-height: 44rpx;
      font-weight: 500;
      font-size: 30rpx;
      color: $uni-main-color;
      font-style: normal;
    }
  }

  .btn-box {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 136rpx;
    background: #fff;
    box-sizing: border-box;
    padding: 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .btn1 {
      width: 45%;
      height: 88rpx;
      line-height: 88rpx;
      text-align: center;
      background: $uni-primary;
      border-radius: 8rpx;
      font-weight: 600;
      font-size: 26rpx;
      color: #ffffff;
      font-style: normal;
    }
  }

  .popup1 {
    width: 582rpx;
    height: 464rpx;
    background: #ffffff;
    border-radius: 24rpx;
    border: 1rpx solid #edf0f5;
    box-sizing: border-box;
    padding: 40rpx 32rpx 32rpx 32rpx;

    .txt1 {
      height: 48rpx;
      font-weight: 600;
      font-size: 32rpx;
      color: #18191a;
      line-height: 48rpx;
      font-style: normal;
    }

    .scan-box {
      margin-top: 32rpx;
      width: 100%;
      height: 84rpx;
      background: #e5f2ff;
      border-radius: 8rpx;
      box-sizing: border-box;
      padding: 20rpx 24rpx;
    }

    .txt2 {
      margin-left: 16rpx;
      font-weight: 500;
      font-size: 30rpx;
      color: #1386fb;
      font-style: normal;
    }
		
		.txt3{
			font-weight: 400;
			font-size: 24rpx;
			color: #9DA5AD;
			line-height: 40rpx;
			text-align: center;
		}

    .btn1 {
      margin-top: 32rpx;
      width: 100%;
      height: 88rpx;
      line-height: 88rpx;
      background: $uni-primary;
      text-align: center;
      border-radius: 8rpx;
      font-weight: 600;
      font-size: 26rpx;
      color: #ffffff;
      font-style: normal;
    }
		.btn2{
			background: #7ABCFF;
		}
  }
}
</style>
