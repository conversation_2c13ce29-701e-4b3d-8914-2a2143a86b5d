<template>
	<view class="page-merchant-con">
		<view class="">
			<!-- 导航栏 -->
			<navbar title="新增商户" titleColor="#fff" backGroundColor="#1386fb" />
			<!-- 背景图 -->
			<view class="bgImg"></view>
			<!-- 用户信息 -->
			<view class="userMessage">
				<view class="message">
					<view class="title">您的基本信息</view>
					<view class="form">
						<up-form labelPosition="left" :model="form" :rules="rules" errorType="toast" ref="form1">
							<up-form-item prop="store_name" borderBottom>
								<view class="text" style="width: 30%">
									商户名称
									<span class="required">*</span>
								</view>
								<up-input border="none" placeholder="请输入您的商户名称" v-model="form.store_name"></up-input>
							</up-form-item>
							<up-form-item prop="address" borderBottom>
								<view class="text" style="width: 30%">
									店铺地址
									<span class="required">*</span>
								</view>
								<uni-data-picker type="region" clear-icon @change="onRegionChange"
									v-slot:default="{ data, error, options }" :localdata="regionData" :value="selectedAddress"
									:show="pickerActive">
									<view class="picker-box" style="z-index: 99 !important;">
										<view v-if="truncatedAddress">{{ truncatedAddress }}</view>
										<view class="clerk-txt1" v-if="!truncatedAddress">请选择地址</view>
										<!-- <up-input border="none" placeholder="请选择地址" disabled v-model="truncatedAddress"></up-input> -->
										<image src="@/static/images/right.svg" mode="aspectFit" style="height: 32rpx; width: 32rpx"></image>
									</view>
								</uni-data-picker>
							</up-form-item>
							<up-form-item prop="merchant_address" borderBottom>
								<view class="text" style="width: 30%">
									详细地址
									<span class="required">*</span>
								</view>
								<up-input border="none" placeholder="详细地址，如1层101室" v-model="form.merchant_address"></up-input>
								<image src="@/static/images/dingwei.png" mode="aspectFit" style="height: 44rpx; width: 44rpx"
									@click="getPositioning"></image>
							</up-form-item>
							<up-form-item prop="s_legal_name" borderBottom>
								<view class="text" style="width: 30%">
									联系人姓名
									<span class="required">*</span>
								</view>
								<up-input border="none" placeholder="请输入联系人姓名" v-model="form.s_legal_name"></up-input>
							</up-form-item>
							<up-form-item prop="s_legal_phone" borderBottom>
								<view class="text" style="width: 30%">
									联系电话
									<span class="required">*</span>
								</view>
								<up-input border="none" placeholder="请输入联系电话" v-model="form.s_legal_phone"></up-input>
							</up-form-item>
						</up-form>
					</view>
				</view>
			</view>
			<!-- 上传图片 -->
			<view class="uploadImgBox">
				<view class="upload">
					<view class="title">上传商户图片</view>
					<view class="message">建议上传门头照或商户LOGO，大小不超过5M</view>
					<view class="uploading" @longpress="url && openPopup('merchant')">
						<image v-if="url" class="charter" :src="baseURL + url" mode="aspectFit"></image>
						<image v-else class="charter" src="" mode="aspectFit"></image>
						<image v-if="!url" class="addIcon" src="@/static/images/addIcon.png" mode="aspectFit" @click="uploadImage">
						</image>
						<view class="btnBox" :class="{ uploaded: isUploaded, failed: uploadFailed }" @click="uploadImage">
							<view class="btn" v-if="isUploaded">上传成功</view>
							<view class="btn" v-else-if="uploadFailed">无法识别,请重新上传</view>
							<view class="btn" v-else>商户图片</view>
						</view>
					</view>
				</view>
			</view>
			<view class="hint">
				<view class="img" style="display: flex">
					<image src="@/static/images/zhengque.png" mode="aspectFit" style="width: 32rpx; height: 32rpx"></image>
					<text class="text">仅使用于商户信息审核，隐私信息严格保密</text>
				</view>
			</view>
			<view class="bottomBox">
				<view class="box">
					<view class="confirm" :style="{ backgroundColor: isFormValid ? '#1386fb' : '#b3d8ff' }" @click="confirm">
						<view class="text">确定</view>
					</view>
				</view>
			</view>
			<!-- 图片长按弹窗组件 -->
			<PoperImg :show="isPopupVisible" @close="closePopup" @delete="deleteImage" @reupload="reuploadImage" />
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, reactive, computed, ComponentInternalInstance, getCurrentInstance, inject, onMounted, onBeforeUnmount } from 'vue';
	import navbar from '@/components/navbar/navbar.vue';
	import { areaList } from '@/utils/wangpu_address.js';
	import { addMerchantApi, editMerchantApi, getMerchantApi, getLocation } from '@/api/merchant';
	import useUserStore from '@/store/user';
	import PoperImg from '@/components/PoperImg.vue';
	import { onLoad } from '@dcloudio/uni-app';

	const pickerActive = ref(false);

	/** 点击获取定位按钮渲染输入框内的函数 */
	const getPositioning = async (event : any) => {
		event.stopPropagation(); // 阻止事件冒泡
		longitude.value = uni.getStorageSync('longitude');
		latitude.value = uni.getStorageSync('latitude');
		if (!longitude.value && !longitude.value) {
			otgetLocation();
		}
		otgetLocation();

		if (longitude.value == '' && longitude.value == '') {
			uni.utils.toast('无法获取位置，请确认授权定位！')
			return true;
		}
		const res = await getLocation({ longitude: longitude.value, latitude: latitude.value });
		if (res.code == '200') {
			const address = res.data; // 假设响应中有地址信息字段
			// 更新 form.merchant_address
			form.merchant_address = address; // 根据实际响应数据更新
		} else {
			uni.utils.toast('无法获取位置，请确认授权定位！')
			return true;
		}

	};
	onBeforeUnmount(() => {
		handleBeforeUnload();
	});

	onLoad((options : any) => {
		if (options) {
			pay_code.value = options.pay_code;
			if (pay_code.value) {
				uni.setStorageSync('merchant_id', '')
			}
		}
		otgetLocation();
	});

	onMounted(async () => {
		otgetLocation();
		const merchantId = uni.getStorageSync('merchant_id');
		if (merchantId) {
			const response = await getMerchantApi(merchantId);
			if (response.code === 200) {
				const data = response.data;
				selectedAddress.value = data.merArea;
				areaList.map((item) => {
					if (data.merArea.length > 2) {
						if (data.merArea[0] == item.value) {
							let ivalue = item.children.find((item) => item.value == data.merArea[1]);
							let ivalue2 = ivalue.children.find((item) => item.value == data.merArea[2]);
							displayAddress.value += item.text;
							displayAddress.value += ivalue.text;
							displayAddress.value += ivalue2.text;
							form.province = item.text;
							form.city = ivalue.text;
							form.area = ivalue2.text;
						}
					} else {
						if (data.merArea[0] == item.value) {
							let ivalue = item.children.find((item) => item.value == data.merArea[1]);
							displayAddress.value += item.text;
							displayAddress.value += ivalue.text;
							form.city = item.text;
							form.area = ivalue.text;
						}
					}
				});
				form.area_code = selectedAddress.value;
				form.address = displayAddress.value;
				// 将获取到的数据填充到表单中
				form.store_name = data.store_name || '';
				form.province = form.province || '';
				form.merchant_address = data.address || '';
				form.s_legal_name = data.store_contacts || '';
				form.s_legal_phone = data.s_legal_phone || '';
				form.merchant_logo = data.merchant_logo || '';

				// 更新图片 URL 和上传状态
				url.value = data.merchant_logo || '';
				isUploaded.value = !!data.merchant_logo;
			} else {
				uni.showToast({ title: response.msg, icon: 'none' });
			}
		}
	});

	/** 进页面默认获取位置信息 */
	function otgetLocation() {
		uni.getLocation({
			type: 'gcj02', // 返回可以用于uni.openLocation的经纬度
			success: function (res) {
				console.log(res);
				/** 把位置信息存储到本地 */
				uni.setStorageSync('longitude', res.longitude);
				uni.setStorageSync('latitude', res.latitude);
				longitude.value = res.longitude;
				latitude.value = res.latitude;
			}
		});

	}

	const handleBeforeUnload = async () => {
		if (uni.getStorageSync('merchant_id')) {
			(form.temp_save = 1),
				// 调用修改接口
				await editMerchantApi(form).then((res : any) => { });
		} else {
			(form.temp_save = 1),
				// 调用新增接口
				await addMerchantApi(form).then((res : any) => {
					if (res.code == 200) {
						uni.setStorageSync('merchant_id', res.data.merchant_id);
					}
				});
		}
	};

	const baseURL = inject('baseURL');

	const userStore = useUserStore();
	interface IComponent {
		show : Function;
	}

	const selectedAddress = ref([]); // 用于存储选择的地址数组
	const displayAddress = ref(''); // 用于显示选择的地址字符串
	const province = ref(''); // 省份
	const city = ref(''); // 市
	const area = ref(''); // 区
	const url = ref('');
	const pay_code = ref('');
	const isUploaded = ref(false); // 是否上传成功
	const uploadFailed = ref(false); // 追踪图片是否上传失败或无法识别
	const { proxy } = getCurrentInstance() as ComponentInternalInstance;
	const form1 = ref<IComponent>({} as IComponent);
	const longitude = ref();
	const latitude = ref();

	// 省市区数据
	const regionData = areaList;

	// 商户信息的表单字段
	const form = reactive({
		merchant_id: uni.getStorageSync('merchant_id'),
		store_name: '',
		address: '',
		province: '',
		city: '',
		area: '',
		codes: '',
		area_code: [],
		merchant_address: '',
		s_legal_name: '',
		temp_save: 0,
		s_legal_phone: '',
		merchant_logo: ''
	});

	// 校验数据
	const rules = {
		store_name: [{ required: true, message: '请输入商户名称', trigger: 'blur' }],
		address: [{ required: true, message: '请选择地址', trigger: ['change', 'blur'] }],
		merchant_address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
		s_legal_name: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
		s_legal_phone: [
			{ required: true, message: '请输入联系电话', trigger: 'blur' },
			// 正则判断手机号是否正确
			{
				pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
				// 正则检验前先将值转为字符串
				transform(value : any) {
					return String(value);
				},
				message: '请输入正确的联系电话'
			}
		]
	};

	const onRegionChange = (event : any) => {
		displayAddress.value = '';
		selectedAddress.value = [];
		event.detail.value.map((item : any) => {
			displayAddress.value += item.text;
			selectedAddress.value.push(item.value);
		});
		if (event.detail.value.length == 3) {
			province.value = event.detail.value[0].text;
			city.value = event.detail.value[1].text;
			area.value = event.detail.value[2].text;
			form.province = province.value;
			form.city = city.value;
			form.area = area.value;
		} else {
			city.value = event.detail.value[0].text;
			area.value = event.detail.value[1].text;
			form.city = city.value;
			form.area = area.value;
		}
		form.area_code = selectedAddress.value;
		form.address = displayAddress.value;
	};

	// 地址截断显示
	const truncatedAddress = computed(() => {
		if (displayAddress.value.length > 7) {
			return displayAddress.value.slice(0, 7) + '...';
		}
		return displayAddress.value;
	});

	// 计算属性：检查表单是否有效
	const isFormValid = computed(() => {
		return form.store_name && form.address && form.merchant_address && form.s_legal_name && form.s_legal_phone;
	});

	// 弹窗相关状态和方法
	const isPopupVisible = ref(false); // 控制弹窗显示状态
	const currentImageType = ref(''); // 当前操作的图片类型

	// 打开弹窗
	const openPopup = (type : any) => {
		currentImageType.value = type;
		isPopupVisible.value = true;
	};

	// 关闭弹窗
	const closePopup = () => {
		isPopupVisible.value = false;
		currentImageType.value = '';
	};

	// 删除图片
	const deleteImage = () => {
		if (currentImageType.value === 'merchant') {
			url.value = '';
			isUploaded.value = false;
			uploadFailed.value = false;
		}
		closePopup();
	};

	// 重新上传图片
	const reuploadImage = () => {
		if (currentImageType.value === 'merchant') {
			url.value = ''; // 清空图片地址
			form.merchant_logo = ''; // 清空表单中的图片路径
			isUploaded.value = false; // 重置上传成功状态
			uploadFailed.value = false; // 重置上传失败状态
			uploadImage(); // 重新上传图片
		}
		closePopup();
	};

	// 上传商户图片按钮
	const uploadImage = async () => {
		uni.chooseImage({
			sourceType: ['album', 'camera'], // 从相册选择
			count: 1,
			success: (chooseImageRes) => {
				const tempFilePaths = chooseImageRes.tempFilePaths;
				uni.uploadFile({
					url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
					filePath: tempFilePaths[0],
					name: 'file',
					formData: { type: '', id: '' },
					header: {
						token: userStore.token
					},
					success: (res) => {
						const data = JSON.parse(res.data);
						if (data.code !== 200) {
							uni.showToast({ title: '上传失败!', icon: 'none' });
							uploadFailed.value = true; // 标记上传失败
							return;
						}
						url.value = data.path;
						form.merchant_logo = url.value;
						isUploaded.value = true; // 标记上传成功
						uploadFailed.value = false; // 重置上传失败状态
					}
				});
			}
		});
	};

	// 确认按钮
	const confirm = async () => {
		const longitude = uni.getStorageSync('longitude') || ''; // 获取经度，如果没有则为空
		const latitude = uni.getStorageSync('latitude') || ''; // 获取纬度，如果没有则为空

		form1.value.validate().then(async () => {
			if (uni.getStorageSync('merchant_id')) {
				if (pay_code.value) {
					form.codes = pay_code.value;
				}
				// 调用修改接口
				await editMerchantApi({
					...form,
					longitude, // 添加经度信息
					latitude // 添加纬度信息
				}).then((res : any) => {
					if (res.code == 200) {
						uni.showToast({ title: res.msg, icon: 'none' });
						setTimeout(() => {
							uni.redirectTo({
								url: '/pages/solutionSelection/solutionSelection'
							});
						}, 1000);
					} else {
						uni.showToast({ title: res.msg, icon: 'none' });
					}
				});
			} else {
				// 从本地存储中获取code_data
				const storedCodeData = uni.getStorageSync('code_data');
				if (storedCodeData) {
					form.codes = storedCodeData;
				}
				if (pay_code.value) {
					form.codes = pay_code.value;
				}
				// 调用新增接口
				await addMerchantApi({
					...form,
					longitude, // 添加经度信息
					latitude // 添加纬度信息
				}).then((res : any) => {
					console.log(longitude);
					console.log(latitude);
					console.log(res);
					if (res.code == 200) {
						uni.setStorageSync('merchant_id', res.data.merchant_id);
						uni.showToast({ title: res.msg, icon: 'none' });
						setTimeout(() => {
							uni.redirectTo({
								url: '/pages/solutionSelection/solutionSelection'
							});
						}, 1000);
					} else {
						uni.showToast({ title: res.msg, icon: 'none' });
					}
				});
			}
		});
	};
</script>

<style lang="scss">
	::v-deep .u-alert__content__desc {
		color: #f39237;
		font-size: 24rpx;
		font-weight: 400;
	}

	::v-deep .u-alert__content__title {
		font-size: 28rpx;
		color: #f39237;
	}

	::v-deep .u-alert {
		border-radius: 8rpx;
	}

	.clerk-txt1 {
		font-size: 28rpx;
		font-weight: 400;
		color: #9da5ad;
	}

	// ::v-deep .uni-input-input {
	//   margin-left: 64rpx;
	// }
	::v-deep .uni-input-wrapper {
		background-color: #ffffff !important;
	}

	::v-deep .uni-input-placeholder {
		// margin-left: 64rpx;
		font-size: 28rpx;
		font-weight: 400;
		color: #9da5ad;
	}

	::v-deep .u-form-item__body {
		border-bottom: 1rpx solid #ededed;
	}

	.page-merchant-con {
		width: 100%;
		height: 100%;
		padding-bottom: 232rpx;

		.popup {
			height: 1120rpx;
			background-color: #ffffff;
			border-radius: 24rpx 24rpx 0rpx 0rpx !important;

			.titleBox {
				display: flex;
				margin-left: 304rpx;
				margin-top: 40rpx;
			}

			.list {
				height: calc(100% - 92rpx); // Adjust the height as needed
				overflow-y: scroll;
				padding: 0 32rpx;

				.listEvery {
					height: 92rpx;
					margin-top: 24rpx;
					line-height: 92rpx;

					.every {
						font-size: 28rpx;
						color: #18191a;
						padding: 0 32rpx;
						border-bottom: 1rpx solid #edf0f5;

						&.selected {
							background-color: #e5f2ff;
							border-radius: 8rpx;
						}
					}
				}
			}
		}

		.bgImg {
			width: 100%;
			height: 612rpx;
			background-image: url('@/static/images/beijing.png');
			background-size: cover;
			background-position: center;
			position: fixed;
			top: 0;
			left: 0;
		}

		.userMessage {
			width: 686rpx;
			height: 612rpx;
			background-color: #fff;
			box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
			border: 1rpx solid #edf0f5;
			border-radius: 24rpx;
			margin-top: 384rpx;
			position: relative;
			z-index: 1;
			margin-left: 32rpx;

			.message {
				padding: 0 32rpx;

				.title {
					font-weight: 600;
					font-size: 40rpx;
					color: #18191a;
					margin-top: 40rpx;
				}

				.form {
					margin-top: 24rpx;
				}
			}
		}

		.required {
			color: red;
		}

		.uploadImgBox {
			width: 686rpx;
			margin-left: 32rpx;
			height: 464rpx;
			background-color: #fff;
			margin-top: 24rpx;
			box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
			border-radius: 24rpx;
			border: 1rpx solid #edf0f5;
			position: relative;

			.upload {
				padding: 0 32rpx;
				margin-top: 40rpx;

				.title {
					font-weight: 600;
					font-size: 30rpx;
					color: #18191a;
				}

				.message {
					font-weight: 400;
					font-size: 24rpx;
					color: #9da5ad;
				}

				.uploading {
					position: relative;

					.charter {
						width: 304rpx;
						height: 200rpx;
						background-color: #f9fafb;
						margin-top: 32rpx;
					}

					.addIcon {
						position: absolute;
						top: 50%;
						left: 25%;
						transform: translate(-50%, -50%);
						width: 104rpx;
						height: 104rpx;
					}
				}

				.btnBox {
					width: 304rpx;
					height: 64rpx;
					background-color: #c1dffe;
					border-radius: 0rpx 0rpx 8rpx 8rpx;

					&.uploaded {
						background-color: #aadb9a;

						/* 背景颜色 */
						.btn {
							color: #198703;
							/* 文字颜色改变 */
						}
					}

					&.failed {
						background-color: #e6b9b5;

						.btn {
							color: #b91810;
						}
					}

					.btn {
						line-height: 64rpx;
						text-align: center;
						font-size: 26rpx;
						color: #0056ad;
					}
				}
			}
		}

		.hint {
			margin-left: 32rpx;
			margin-top: 16rpx;

			.text {
				color: #575e65;
				font-size: 24rpx;
				font-weight: 400;
			}
		}

		.bottomBox {
			position: fixed;
			box-shadow: 0rpx -2rpx 4rpx 0rpx rgba(75, 80, 85, 0.06);
			background: #ffffff;
			margin-top: 58rpx;
			width: 100%;
			height: 172rpx;
			bottom: 0;
			left: 0;
			padding: 0 32rpx;

			// z-index: 1;
			.box {
				display: flex;

				.confirm {
					width: 686rpx;
					height: 88rpx;
					background-color: #b3d8ff;
					border-radius: 8rpx;
					margin-top: 24rpx;
					margin-right: 16rpx;

					.text {
						line-height: 88rpx;
						text-align: center;
						color: #ffffff;
						font-weight: 500;
						font-size: 26rpx;
					}
				}
			}
		}
	}

	.picker-box {
		display: flex;
		align-items: center;
		justify-content: space-between;

		.address {
			font-weight: 400;
			font-size: 28rpx;
			color: #1386fb;
		}
	}
</style>