<template>
	<view class="my" @touchmove.stop.prevent="stopRoll">
	<up-popup :show="props.show" mode="center" round="10" @open="open">
		<view class="popup2">
			<view class="flex-sty2">
				<view class="" style="width: 44rpx;height: 44rpx;"> </view>
				<view class="popup2-txt1">商户签约</view>
				<image @click="close" style="width:44rpx;height:44rpx;" src="@/static/images/close.png" mode=""></image>
			</view>
			<view class="img-box">
				<image style="width: 398rpx;height: 398rpx;" src="@/static/images/img-border.png" mode=""></image>
				<image class="code-img" :src="signCode" mode=""></image>
			</view>
			<view class="btn-box">
				<view class="btn1">商户扫一扫签约</view>
			</view>
		</view>
	</up-popup>
	</view>
</template>

<script lang="ts" setup>
	import { watch } from 'vue';
	const emit = defineEmits(['close'])

	const props = defineProps({
		show: {
			type: Boolean,
			default: false
		},
		signCode:{
			type: String,
			default: ''
		}
	})
	
	watch(() => props.show, (val) => {
		if (val) {
			document.body.style.overflow = 'hidden'; // 禁止页面滚动
		} else {
			document.body.style.overflow = 'auto'; // 恢复页面滚动
		}
	})
	
	function stopRoll(){
		
	}
	
	/** 打开弹窗 */
	function open() {
	
	}
	
	function close() {
		emit('close', false)
	}
</script>

<style lang="scss" scoped>
	.popupShow {
		overflow: hidden;
		position: fixed;
	}

	.flex-sty2 {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.popup2{
		width: 582rpx;
		height: 678rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75,80,85,0.04);
		border-radius: 24rpx;
		border: 1rpx solid #EDF0F5;
		box-sizing: border-box;
		padding: 40rpx;
		
		.popup2-txt1{
			height: 48rpx;
			font-weight: 600;
			font-size: 32rpx;
			color: $uni-main-color;
			line-height: 48rpx;
			text-align: center;
			font-style: normal;
		}
		
		.img-box{
			position: relative;
			margin-top: 32rpx;
			width: 100%;
			height: 398rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.code-img{
				position: absolute;
				left: 50%;
				top: 50%;
				width: 310rpx;
				height: 310rpx;
				transform: translate(-50%,-50%);
			}
		}
		
		.btn-box{
			margin-top: 32rpx;
			width: 100%;
			height: 64rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.btn1{
				width: 398rpx;
				height: 64rpx;
				line-height: 64rpx;
				background: #EDF0F5;
				border-radius: 8rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: #18191A;
				text-align: center;
				font-style: normal;
			}
		}
	}

</style>