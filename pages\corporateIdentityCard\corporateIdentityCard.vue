<template>
	<view class="">
		<!-- 导航栏 -->
		<navbar title="开通支付" backGroundColor="#fff" />
		<!-- 步骤条 -->
		<StepBar :currentStep="2" />
	</view>
	<view class="content">
		<view class="schemeSelection">
			<view class="title">上传身份证</view>
			<p class="hint">请上传真实有效信息，以下信息将用于各类申请</p>
		</view>
		<view class="uploading" style="height: 1000rpx">
			<view class="uploadingMessage">
				<view class="title">请上传身份证</view>
				<view class="text">请上传结算人身份证正反面，大小不超过5M</view>
			</view>
			<view class="uploadingImages">
				<view class="uploadBox" @longpress="idCardFrontUrl && openPopup('idCardFront')">
					<image v-if="idCardFrontUrl" class="charter" :src="baseURL + idCardFrontUrl" mode="aspectFit"></image>
					<image v-else class="charter" src="@/static/images/renxiang.png" mode="aspectFit"
						@click="uploadIdCardFrontImage"></image>
					<view class="btnBox" :class="{ uploaded: isIdCardFrontUploaded, failed: idCardFrontUploadFailed }"
						@click="uploadIdCardFrontImage">
						<view class="btn" v-if="isIdCardFrontUploaded">上传成功</view>
						<view class="btn" v-else-if="idCardFrontUploadFailed">无法识别,请重新上传</view>
						<view class="btn" v-else>上传人像页</view>
					</view>
				</view>
				<view class="uploadBox" @longpress="idCardBackUrl && openPopup('idCardBack')">
					<image v-if="idCardBackUrl" class="charter" :src="baseURL + idCardBackUrl" mode="aspectFit"></image>
					<image v-else class="charter" src="@/static/images/guohui.png" mode="aspectFit"
						@click="uploadIdCardBackImage"></image>
					<view class="btnBox" :class="{ uploaded: isIdCardBackUploaded, failed: idCardBackUploadFailed }"
						@click="uploadIdCardBackImage">
						<view class="btn" v-if="isIdCardBackUploaded">上传成功</view>
						<view class="btn" v-else-if="idCardBackUploadFailed">无法识别,请重新上传</view>
						<view class="btn" v-else>上传国徽页</view>
					</view>
				</view>
			</view>
			<view class="uploadingMessage">
				<view class="title">请上传法人手持身份证</view>
				<view class="text">请上传法人手持身份证正面，大小不超过5M</view>
			</view>
			<view class="uploadingImages" style="justify-content: center">
				<view class="uploadBox" @click="uploadHandheldIdCardImage"
					@longpress="handheldIdCardUrl && openPopup('handheld')">
					<image class="charter" v-if="handheldIdCardUrl" :src="baseURL + handheldIdCardUrl" mode="aspectFit"></image>
					<image class="charter" v-else src="@/static/images/renxiang.png" mode="aspectFit"></image>
					<view class="btnBox" :class="{ uploaded: isHandheldIdCardUploaded, failed: handheldIdCardUploadFailed }">
						<view class="btn" v-if="isHandheldIdCardUploaded">上传成功</view>
						<view class="btn" v-else-if="handheldIdCardUploadFailed">上传失败!</view>
						<view class="btn" v-else>上传法人手持身份证</view>
					</view>
				</view>
			</view>
		</view>
		<view class="require">
			<view class="title">拍摄要求</view>
			<view class="imgList">
				<view class="img1">
					<image src="@/static/images/biaozhun.png" mode="widthFix" style="width: 160rpx"></image>
					<view class="text">
						<image src="@/static/images/duihao.png" mode="widthFix" style="width: 32rpx"></image>
						<view class="word">标准</view>
					</view>
				</view>
				<view class="img2">
					<image src="@/static/images/queshi.png" mode="widthFix" style="width: 160rpx"></image>
					<view class="text">
						<image src="@/static/images/cuowu.png" mode="widthFix" style="width: 32rpx"></image>
						<view class="word">边角缺失</view>
					</view>
				</view>
				<view class="img3">
					<image src="@/static/images/mohu.png" mode="widthFix" style="width: 160rpx"></image>
					<view class="text">
						<image src="@/static/images/cuowu.png" mode="widthFix" style="width: 32rpx"></image>
						<view class="word">照片模糊</view>
					</view>
				</view>
				<view class="img4">
					<image src="@/static/images/fanguang.png" mode="widthFix" style="width: 160rpx"></image>
					<view class="text">
						<image src="@/static/images/cuowu.png" mode="widthFix" style="width: 32rpx"></image>
						<view class="word">反光强烈</view>
					</view>
				</view>
			</view>
		</view>
		<view class="messageBox">
			<view class="message">
				<view class="title">身份证基本信息</view>
				<view class="form">
					<up-form labelPosition="left" :model="model1" :rules="rules" ref="form1">
						<up-form-item prop="name" borderBottom ref="item1">
							<view class="text" style="width: 30%">
								姓名
								<span class="required">*</span>
							</view>
							<up-input border="none" placeholder="上传后自动识别或手动输入" v-model="picDataFront.name" disabledColor="#fff" />
						</up-form-item>
						<up-form-item prop="number" borderBottom ref="item1">
							<view class="text" style="width: 30%">
								身份证号
								<span class="required">*</span>
							</view>
							<up-input border="none" placeholder="上传后自动识别或手动输入" v-model="picDataFront.number" disabledColor="#fff" />
						</up-form-item>
						<up-form-item prop="valid_from" borderBottom ref="item1">
							<view class="text" style="width: 30%">
								生效日期
								<span class="required">*</span>
							</view>
							<up-input border="none" placeholder="上传后自动识别或手动输入" v-model="picDataBack.valid_from"
								disabledColor="#fff" />
						</up-form-item>
						<up-form-item prop="valid_to" borderBottom ref="item1">
							<view class="text" style="width: 30%">
								有效期至
								<span class="required">*</span>
							</view>
							<up-input border="none" placeholder="上传后自动识别或手动输入" v-model="picDataBack.valid_to" disabledColor="#fff" />
						</up-form-item>
					</up-form>
				</view>
			</view>
		</view>
		<view class="bottomBox">
			<view class="box">
				<view class="backBox" @click="back">
					<view class="text">上一步</view>
				</view>
				<view class="nextBox" :class="{ active: allUploaded }" @click="next">
					<view class="text">下一步</view>
				</view>
			</view>
		</view>

		<view class="" style="width: 100%; height: 226rpx"></view>

		<!-- 图片长摁弹窗组件 -->
		<PoperImg :show="isPopupVisible" @close="closePopup" @delete="deleteImage" @reupload="reuploadImage" />
	</view>
</template>

<script setup>
	import {
		ref,
		reactive,
		inject,
		onMounted,
		onBeforeUnmount,
		computed
	} from 'vue';
	import navbar from '@/components/navbar/navbar.vue';
	import StepBar from '@/components/steps.vue';
	import PoperImg from '@/components/PoperImg.vue';
	import useUserStore from '@/store/user';
	import {
		getMerchantApi
	} from '@/api/merchant';
	import {
		saveMerchantLegalPersonApi
	} from '@/api/corporateIdentitryCard';
	import {
		verifyCardId
	} from '@/utils/rules';

	/** 获取位置信息 */
	function otgetLocation() {
		uni.getLocation({
			type: 'gcj02', // 返回可以用于uni.openLocation的经纬度
			success: function(res) {
				/** 把位置信息存储到本地 */
				uni.setStorageSync('longitude', res.longitude);
				uni.setStorageSync('latitude', res.latitude);
			},
			fail: function(err) {
				console.log('获取位置信息失败：' + JSON.stringify(err));
			}
		});
	}

	const baseURL = inject('baseURL'); // 注入baseURL
	const userStore = useUserStore(); // 使用用户存储
	const statusBarHeight = uni.getStorageSync('statusBarHeight'); // 获取状态栏高度

	const merchant_id = ref(''); // 商户ID
	const idCardFrontUrl = ref(''); // 身份证正面图片URL
	const idCardBackUrl = ref(''); // 身份证反面图片URL
	const isIdCardFrontUploaded = ref(false); // 身份证正面是否上传成功
	const isIdCardBackUploaded = ref(false); // 身份证反面是否上传成功
	const idCardFrontUploadFailed = ref(false); // 身份证正面上传是否失败
	const idCardBackUploadFailed = ref(false); // 身份证反面上传是否失败

	const handheldIdCardUrl = ref(''); // 手持身份证图片URL
	const isHandheldIdCardUploaded = ref(false); // 手持身份证是否上传成功
	const handheldIdCardUploadFailed = ref(false); // 手持身份证上传是否失败

	const picDataFront = ref({}); // 存储身份证正面识别的数据
	const picDataBack = ref({}); // 存储身份证反面识别的数据

	const isPopupVisible = ref(false); // 控制弹窗显示状态
	const currentImageType = ref(''); // 当前操作的图片类型

	const allUploaded = computed(() => {
		return isIdCardFrontUploaded.value && isIdCardBackUploaded.value && isHandheldIdCardUploaded.value;
	});

	onBeforeUnmount(async () => {
		handleBeforeUnload();
	});

	onMounted(async () => {
		otgetLocation();
		// 获取商户ID并请求商户数据
		merchant_id.value = uni.getStorageSync('merchant_id');
		if (merchant_id.value) {
			await fetchMerchantData(merchant_id.value);
		}
	});

	const handleBeforeUnload = async () => {
		// 用户正在离开页面
		const data = {
			id: merchant_id.value,
			merchant_img_corporate_id_card_front: idCardFrontUrl.value || '',
			merchant_img_corporate_id_card_obverse: idCardBackUrl.value || '',
			name: picDataFront.value.name || '',
			number: picDataFront.value.number || '',
			crp_start_dt: picDataBack.value.valid_from || '',
			temp_save: 1,
			crp_exp_dt: picDataBack.value.valid_to || '',
			merchant_img_holding_photo: handheldIdCardUrl.value || '' // 添加手持身份证图片URL
		};
		await saveMerchantLegalPersonApi(data);
	};

	// 获取商户数据函数
	const fetchMerchantData = async (merchantId) => {
		const response = await getMerchantApi(merchantId);
		if (response.code === 200) {
			const data = response.data;
			idCardFrontUrl.value = data.merchant_img_corporate_id_card_front;
			idCardBackUrl.value = data.merchant_img_corporate_id_card_obverse;
			handheldIdCardUrl.value = data.merchant_img_legal_holding_id_photo; // 获取手持身份证图片URL

			picDataFront.value = {
				name: data.crp_nm,
				number: data.crp_id
			};
			picDataBack.value = {
				valid_from: data.crp_start_dt,
				valid_to: data.crp_exp_dt
			};

			isIdCardFrontUploaded.value = !!idCardFrontUrl.value;
			isIdCardBackUploaded.value = !!idCardBackUrl.value;
			isHandheldIdCardUploaded.value = !!handheldIdCardUrl.value;
		}
	};

	// 打开弹窗函数
	const openPopup = (type) => {
		currentImageType.value = type;
		isPopupVisible.value = true;
	};

	// 关闭弹窗函数
	const closePopup = () => {
		isPopupVisible.value = false;
		currentImageType.value = '';
	};

	// 删除图片函数
	const deleteImage = () => {
		if (currentImageType.value === 'idCardFront') {
			idCardFrontUrl.value = '';
			isIdCardFrontUploaded.value = false;
			idCardFrontUploadFailed.value = false;
		} else if (currentImageType.value === 'idCardBack') {
			idCardBackUrl.value = '';
			isIdCardBackUploaded.value = false;
			idCardBackUploadFailed.value = false;
		} else if (currentImageType.value === 'handheld') {
			handheldIdCardUrl.value = '';
			isHandheldIdCardUploaded.value = false;
			handheldIdCardUploadFailed.value = false;
		}
		closePopup();
	};

	// 重新上传图片
	const reuploadImage = () => {
		if (currentImageType.value === 'idCardFront') {
			idCardFrontUrl.value = ''; // 清空图片地址
			isIdCardFrontUploaded.value = false; // 重置上传状态
			idCardFrontUploadFailed.value = false; // 重置失败状态
			picDataFront.value = {}; // 清空身份证正面识别数据
			uploadIdCardFrontImage();
		} else if (currentImageType.value === 'idCardBack') {
			idCardBackUrl.value = ''; // 清空图片地址
			isIdCardBackUploaded.value = false; // 重置上传状态
			idCardBackUploadFailed.value = false; // 重置失败状态
			picDataBack.value = {}; // 清空身份证反面识别数据
			uploadIdCardBackImage();
		} else if (currentImageType.value === 'handheld') {
			handheldIdCardUrl.value = ''; // 清空图片地址
			isHandheldIdCardUploaded.value = false; // 重置上传状态
			handheldIdCardUploadFailed.value = false; // 重置失败状态
			uploadHandheldIdCardImage();
		}
		closePopup();
	};

	// 上传身份证正面图片函数
	const uploadIdCardFrontImage = async () => {
		uni.chooseImage({
			sourceType: ['album', 'camera'],
			count: 1,
			success: (chooseImageRes) => {
				const tempFilePaths = chooseImageRes.tempFilePaths;
				uni.uploadFile({
					url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						type: 'idcardFrontPic',
						id: merchant_id.value
					},
					header: {
						token: userStore.token
					},
					success: (res) => {
						const data = JSON.parse(res.data);
						if (data.code !== 200) {
							uni.showToast({
								title: data.msg,
								icon: 'none'
							});
							idCardFrontUploadFailed.value = true;
							return;
						}
						idCardFrontUrl.value = data.path;
						picDataFront.value = data.data;
						isIdCardFrontUploaded.value = true;
						idCardFrontUploadFailed.value = false;
						if (picDataBack.value !== '' && picDataFront.value !== '') {
							showAlert.value = true;
						}
					}
				});
			}
		});
	};

	// 上传身份证反面图片函数
	const uploadIdCardBackImage = async () => {
		uni.chooseImage({
			sourceType: ['album', 'camera'],
			count: 1,
			success: (chooseImageRes) => {
				const tempFilePaths = chooseImageRes.tempFilePaths;
				uni.uploadFile({
					url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						type: 'idcardBackPic',
						id: merchant_id.value
					},
					header: {
						token: userStore.token
					},
					success: (res) => {
						const data = JSON.parse(res.data);
						if (data.code !== 200) {
							uni.showToast({
								title: data.msg,
								icon: 'none'
							});
							idCardBackUploadFailed.value = true;
							return;
						}
						idCardBackUrl.value = data.path;
						picDataBack.value = data.data;
						isIdCardBackUploaded.value = true;
						idCardBackUploadFailed.value = false;
						if (picDataBack.value !== '' && picDataFront.value !== '') {
							showAlert.value = true;
						}
					}
				});
			}
		});
	};

	// 上传手持身份证图片函数
	const uploadHandheldIdCardImage = async () => {
		uni.chooseImage({
			sourceType: ['album', 'camera'],
			count: 1,
			success: (chooseImageRes) => {
				const tempFilePaths = chooseImageRes.tempFilePaths;
				uni.uploadFile({
					url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						type: '',
						id: ''
					},
					header: {
						token: userStore.token
					},
					success: (res) => {
						const data = JSON.parse(res.data);
						if (data.code !== 200) {
							uni.showToast({
								title: data.msg,
								icon: 'none'
							});
							handheldIdCardUploadFailed.value = true;
							return;
						}
						handheldIdCardUrl.value = data.path;
						isHandheldIdCardUploaded.value = true;
						handheldIdCardUploadFailed.value = false;
					}
				});
			}
		});
	};

	// 上一步
	const back = async () => {
		const data = {
			id: merchant_id.value,
			merchant_img_corporate_id_card_front: idCardFrontUrl.value || '',
			merchant_img_corporate_id_card_obverse: idCardBackUrl.value || '',
			merchant_img_holding_photo: handheldIdCardUrl.value || '', // 添加手持身份证图片URL
			name: picDataFront.value.name || '',
			number: picDataFront.value.number || '',
			crp_start_dt: picDataBack.value.valid_from || '',
			temp_save: 1,
			crp_exp_dt: picDataBack.value.valid_to || '',
			longitude: uni.getStorageSync('longitude') || '', // 获取经度，如果没有则为空
			latitude: uni.getStorageSync('latitude') || '' // 获取纬度，如果没有则为空
		};
		await saveMerchantLegalPersonApi(data);
		uni.redirectTo({
			url: '/pages/identityCard/identityCard'
		});
	};

	// 下一步按钮函数
	const next = async () => {
		// 添加手持身份证的校验逻辑
		if (!handheldIdCardUrl.value) {
			uni.showToast({
				title: '请上传法人手持身份证照片',
				icon: 'none'
			});
			return;
		}
		if (isIdCardFrontUploaded.value && isIdCardBackUploaded.value) {
			const dateFormats = [/^(\d{4})-(\d{1,2})-(\d{1,2})$/, /^(\d{4})年(\d{1,2})月(\d{1,2})日$/];
			const isValidDate = (date) => {
				// 如果有效期为“长期”，直接通过校验
				if (date === '长期') {
					return true;
				}
				// 否则继续使用日期格式进行校验
				return dateFormats.some((format) => format.test(date));
			};
			if (!isValidDate(picDataBack.value.valid_to)) {
				uni.showToast({
					title: '身份证有效期格式不正确，请按照xxxx-xx-xx或xxxx年xx月xx日或长期格式填写',
					icon: 'none'
				});
				return;
			}
			const currentDate = new Date();
			const validToDateStr = picDataBack.value.valid_to.replace(/年|月/g, '-').replace(/日/g, '');
			// 如果不是“长期”，需要检查日期是否小于当前日期
			if (validToDateStr !== '长期') {
				const validToDate = new Date(validToDateStr);
				if (validToDate <= currentDate) {
					uni.showToast({
						title: '身份证有效期不能小于当前日期',
						icon: 'none'
					});
					return;
				}
			}
			const data = {
				id: merchant_id.value,
				merchant_img_corporate_id_card_front: idCardFrontUrl.value,
				merchant_img_corporate_id_card_obverse: idCardBackUrl.value,
				name: picDataFront.value.name,
				number: picDataFront.value.number,
				crp_start_dt: picDataBack.value.valid_from,
				temp_save: 0,
				crp_exp_dt: picDataBack.value.valid_to,
				merchant_img_holding_photo: handheldIdCardUrl.value, // 添加手持身份证图片URL
				longitude: uni.getStorageSync('longitude') || '', // 获取经度，如果没有则为空
				latitude: uni.getStorageSync('latitude') || '' // 获取纬度，如果没有则为空
			};
			const res = await saveMerchantLegalPersonApi(data);
			if (res.code === 200) {
				// 将 save_page 存储为 3
				uni.setStorageSync('save_page', 3);
				uni.showToast({
					title: res.msg,
					icon: 'none'
				});
				uni.redirectTo({
					url: '/pages/shopScene/shopScene'
				});
			} else {
				uni.showToast({
					title: res.msg,
					icon: 'none'
				});
			}
		} else {
			uni.showToast({
				title: '请上传完整的身份证照片',
				icon: 'none'
			});
		}
	};
</script>

<style lang="scss" scoped>
	::v-deep .u-alert__content__desc {
		color: #f39237;
		font-size: 24rpx;
		font-weight: 400;
	}

	::v-deep .u-alert__content__title {
		font-size: 28rpx;
		color: #f39237;
	}

	::v-deep .u-alert {
		border-radius: 8rpx;
	}

	::v-deep .uni-input-input {
		// margin-left: 64rpx;
	}

	::v-deep .uni-input-placeholder {
		// margin-left: 64rpx;
		font-size: 28rpx;
		font-weight: 400;
		color: #9da5ad;
	}

	::v-deep .u-form-item__body {
		border-bottom: 1rpx solid #ededed;
	}

	.content {
		width: 100%;
		height: 100%;
		padding-top: 164rpx;

		.steps {
			width: 100%;
			height: 164rpx;
			background-color: #ffffff;
		}

		.schemeSelection {
			padding: 0 32rpx;
			margin-top: 56rpx;

			.title {
				font-size: 44rpx;
				margin-bottom: 8rpx;
			}

			.hint {
				font-size: 24rpx;
				color: #9da5ad;
			}
		}

		.uploading {
			width: 686rpx;
			height: 512rpx;
			background-color: #ffffff;
			margin-left: 32rpx;
			margin-top: 32rpx;
			box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
			border: 1rpx solid #edf0f5;
			border-radius: 24rpx;

			.uploadingMessage {
				padding: 0 32rpx;
				margin-top: 40rpx;

				.title {
					font-weight: 500;
					font-size: 30rpx;
					color: #18191a;
				}

				.text {
					font-weight: 400;
					font-size: 24rpx;
					color: #9da5ad;
					margin-top: 8rpx;
				}
			}

			.uploadingImages {
				display: flex;
				justify-content: space-between;
				margin-top: 24rpx;
				padding: 0 32rpx;

				.uploadBox {
					width: 304rpx;

					.charter {
						width: 100%;
						height: 256rpx;
						background-color: #f9fafb;
					}

					.btnBox {
						width: 100%;
						height: 64rpx;
						background-color: #c1dffe;
						border-radius: 0rpx 0rpx 8rpx 8rpx;

						&.uploaded {
							background-color: #aadb9a;

							/* 背景颜色 */
							.btn {
								color: #198703;
								/* 文字颜色改变 */
							}
						}

						&.failed {
							background-color: #e6b9b5;

							.btn {
								color: #b91810;
							}
						}

						.btn {
							line-height: 64rpx;
							text-align: center;
							font-size: 26rpx;
							color: #0056ad;
						}
					}
				}
			}
		}

		.require {
			margin-top: 56rpx;
			padding: 0 32rpx;

			.title {
				font-weight: 500;
				font-size: 30rpx;
				color: #18191a;
			}

			.imgList {
				display: flex;
				justify-content: space-between;
				margin-top: 16rpx;
			}

			.text {
				display: flex;
				justify-content: center;

				.word {
					font-size: 24rpx;
					font-weight: 400;
					color: #575e65;
				}
			}
		}

		.messageBox {
			height: 506rpx;
			background: #fff;
			box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
			border-radius: 8rpx;
			border: 1rpx solid #edf0f5;
			margin-bottom: 64rpx;
			margin: 0 32rpx;
			margin-top: 56rpx;

			.message {
				padding: 40rpx 32rpx;
			}

			.form {
				margin-top: 16rpx;

				.text {
					font-weight: 400;
					font-size: 28rpx;
					color: #575e65;

					.required {
						color: red;
					}
				}
			}
		}

		.bottomBox {
			position: fixed;
			box-shadow: 0rpx -2rpx 4rpx 0rpx rgba(75, 80, 85, 0.06);
			background: #ffffff;
			margin-top: 58rpx;
			width: 100%;
			height: 172rpx;
			bottom: 0;
			left: 0;
			padding: 0 32rpx;

			.box {
				display: flex;

				.backBox {
					width: 218rpx;
					height: 88rpx;
					border: 2rpx solid #1b7de1;
					border-radius: 8rpx;
					margin-top: 24rpx;
					margin-right: 16rpx;

					.text {
						line-height: 88rpx;
						text-align: center;
						color: #1386fb;
						font-weight: 600;
						font-size: 26rpx;
					}
				}

				.nextBox {
					width: 452rpx;
					height: 94rpx;
					background: #dee2e8;
					border-radius: 8rpx;
					margin-top: 24rpx;

					&.active {
						background: #1386fb;

						.text {
							color: #ffffff;
						}
					}

					.text {
						line-height: 94rpx;
						text-align: center;
						color: #9da5ad;
						font-size: 26rpx;
						font-weight: 500;
					}
				}
			}
		}

		.nextBox.active {
			background: #1386fb;
		}

		.nextBox.active .text {
			color: #ffffff;
		}
	}
</style>