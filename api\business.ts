/** 业务 */
export const businessApi = () => uni.$http.get('tenant_salesman.business/index');

/** 商家列表 */
export const statisticsApi = (params:any) => uni.$http.get('tenant_salesman.business/merchantlist',params);

/** 当月新增 */
export const monthlyNewApi = (params:any) => uni.$http.get('tenant_salesman.business/curmonthmerchantlist',params);






/** 商家详情 */
export const merchantDetailApi = (merchant_id:any) => uni.$http.get(`tenant_salesman.merchantdetail/index?merchant_id=${merchant_id}`);

/** 商家交易记录 */
export const merchantTraderecordApi = (merchant_id:any) => uni.$http.get(`tenant_salesman.merchantdetail/traderecord?merchant_id=${merchant_id}`);

/** 直属商家交易明细 */
export const tradeDetailApi = () => uni.$http.get('tenant_salesman.merchantdetail/tradeDetail');

/** 解绑设备 */
export const unBindDeviceApi = (sn: any, device_type: any) => uni.$http.get(`merchant_app.stores/unBindDevice?sn=${sn}&device_type=${device_type}`);

/** 解绑二维码 */
export const delCodeApi = (code: any, store_id: any) => uni.$http.get(`merchant_app.stores/delCode?code=${code}&store_id=${store_id}`);