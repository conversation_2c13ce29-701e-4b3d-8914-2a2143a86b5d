<template>
	<view class="page-team-container">
		<!-- 导航栏 -->
		<navbar title="团队" />
		<view class="page-team-container-t flex-sty">
			<view class="page-team-t-l flex-sty1">
				<view class="">
					<view class="txt1">团队本月目标开户</view>
					<view class="txt2">{{statisticsList.team_monthly_tasks}}</view>
				</view>
				<view class="page-team-t-l-t"></view>
				<view class="">
					<view class="txt1">团队本月开户</view>
					<view class="txt2">{{statisticsList.team_monthly_count_merchant}}</view>
				</view>
			</view>
			<view class="page-team-t-r">
				<l-circularProgress :fontShow="false" bgColor="rgba(255,255,255,0.2)" :lineWidth="16" :boxWidth="boxWidth"
					:boxHeight="boxHeight" progressColor="#fff" fontColor="#1386FB" gradualColor="#1386FB"
					:percent="statisticsList.team_proportion">
					<view class="text">
						<view class="txt3">{{statisticsList.team_proportion}}%</view>
						<view class="">目标完成度</view>
					</view>
				</l-circularProgress>
			</view>
		</view>
		<view class="team-txt">团队成员</view>
		<view class="" style="padding-bottom: 100rpx;">
			<TeamItem1 v-for="item in teamMemberList" :key="item" :item="item" />
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, reactive, computed, onMounted } from 'vue';
	import { onLoad } from "@dcloudio/uni-app"
	import navbar from '@/components/navbar/navbar.vue';
	import TeamItem1 from '@/pages/team/components/TeamItem1.vue'
	import { teamListIndexApi,teamMemberListApi } from '@/api/team'

	const boxWidth = ref(150)
	const boxHeight = ref(150)

	let statisticsList = ref({})
	/** 团队 上部统计 */
	async function otteamListIndexApi() {
		const res = await teamListIndexApi(team_id.value)
		if (res.code == 200) {
			statisticsList.value = res.data
		} else {
			uni.utils.toast(res.msg)
		}
	}


	/** 页面传递过来的 team_id*/
	let team_id = ref(null)
	/** 成员列表 */
	let teamMemberList = ref([])
	async function otteamMemberListApi(){
		const res = await teamMemberListApi(team_id.value)
		if(res.code == 200){
			teamMemberList.value = res.data
		}else{
			uni.utils.toast(res.msg)
		}
	}
	
	onLoad((option) => {
		team_id.value = option.team_id
	})

	onMounted(() => {
		otteamListIndexApi()
		otteamMemberListApi()
	})
</script>

<style lang="scss" scoped>
	::v-deep .circular-container .circular-progress {
		z-index: 0;
	}

	.flex-sty {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.flex-sty1 {
		display: flex;
		flex-direction: column;
		justify-content: space-around;
	}

	.page-team-container {
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		padding: 32rpx;

		.page-team-container-t {
			width: 100%;
			height: 328rpx;
			background: linear-gradient(180deg, #1352FB 0%, #1386FB 100%);
			border-radius: 24rpx;
			box-sizing: border-box;
			padding: 32rpx 40rpx;
		}

		.page-team-t-l {
			width: 48%;
			height: 100%;

			.page-team-t-l-t {
				width: 100%;
				height: 1rpx;
				background: #FFFFFF;
				opacity: 0.25;
			}

			.txt1 {
				width: 100%;
				height: 44rpx;
				font-weight: 600;
				font-size: 28rpx;
				color: #FFFFFF;
				line-height: 44rpx;
				font-style: normal;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			.txt2 {
				width: 100%;
				height: 56rpx;
				font-weight: 600;
				font-size: 40rpx;
				color: #FFFFFF;
				line-height: 56rpx;
				font-style: normal;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
		}

		.page-team-t-r {
			.text {
				position: absolute;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
				height: 40rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: #FFFFFF;
				line-height: 40rpx;
				font-style: normal;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				.txt3 {
					height: 56rpx;
					font-weight: 600;
					font-size: 40rpx;
					color: #FFFFFF;
					line-height: 56rpx;
					font-style: normal;
				}
			}
		}

		.team-txt {
			height: 48rpx;
			font-weight: 600;
			font-size: 34rpx;
			color: #18191A;
			line-height: 48rpx;
			font-style: normal;
			margin-top: 56rpx;
			margin-bottom: 32rpx;
		}
	}
</style>