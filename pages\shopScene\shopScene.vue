<template>
	<view class="">
		<!-- 导航栏 -->
		<navbar title="开通支付" backGroundColor="#fff" />
		<!-- 步骤条 -->
		<StepBar :currentStep="3" />
	</view>
	<view class="content" :style="{ marginTop: statusBarHeight + 'px' }">
		<view class="schemeSelection">
			<view class="title">上传商户场景照</view>
			<p class="hint">以下信息请至少上传一张图片</p>
		</view>
		<view class="uploading">
			<!-- 上传门头 -->
			<view class="uploadingMessage">
				<view class="title">上传门头</view>
				<view class="" style="display: flex; justify-content: space-between; flex-wrap: wrap">
					<!-- 显示已上传的图片，支持长按删除 -->
					<view class="img" v-for="(url, index) in headerUrls" :key="'header-' + index"
						@longpress="url && openPopup('header', index)">
						<image v-if="url" class="charter" :src="baseURL + url" mode="aspectFit"></image>
						<view class="btnBox" :class="{ uploaded: headerUploadStatuses[index], failed: headerUploadFailed[index] }"
							:style="{ height: headerUploadStatuses[index] ? '64rpx' : '0px' }">
							<view class="btn" v-if="headerUploadStatuses[index]">上传成功</view>
							<view class="btn" v-else-if="headerUploadFailed[index]">上传失败，请重新上传</view>
						</view>
					</view>
					<!-- 上传按钮继续存在，直到上传的图片达到2张时隐藏 -->
					<view v-if="canUploadHeader" class="img">
						<image class="charter" src="" mode="aspectFit"></image>
						<image class="addIcon" src="@/static/images/addIcon.png" mode="aspectFit"
							@click="uploadHeaderImage(headerUrls.length)"></image>
						<view class="btnBox">
							<view class="btn">上传图片</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 上传商户收银台 -->
			<view class="uploadingMessage">
				<view class="title">商户收银台</view>
				<view class="" style="display: flex; justify-content: space-between; flex-wrap: wrap">
					<!-- 显示已上传的图片，支持长按删除 -->
					<view class="img" v-for="(url, index) in cashierUrls" :key="'cashier-' + index"
						@longpress="url && openPopup('cashier', index)">
						<image v-if="url" class="charter" :src="baseURL + url" mode="aspectFit"></image>
						<view class="btnBox" :class="{ uploaded: cashierUploadStatuses[index], failed: cashierUploadFailed[index] }"
							:style="{ height: url ? '64rpx' : '0px' }">
							<view class="btn" v-if="cashierUploadStatuses[index]">上传成功</view>
							<view class="btn" v-else-if="cashierUploadFailed[index]">上传失败，请重新上传</view>
						</view>
					</view>
					<!-- 上传按钮继续存在，直到上传的图片达到2张时隐藏 -->
					<view v-if="canUploadCashier" class="img">
						<image class="charter" src="" mode="aspectFit"></image>
						<image class="addIcon" src="@/static/images/addIcon.png" mode="aspectFit"
							@click="uploadCashierImage(cashierUrls.length)"></image>
						<view class="btnBox">
							<view class="btn">上传图片</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 上传店内场景 -->
			<view class="uploadingMessage">
				<view class="title">店内场景</view>
				<view class="" style="display: flex; justify-content: space-between; flex-wrap: wrap">
					<!-- 显示已上传的图片，支持长按删除 -->
					<view class="img" v-for="(url, index) in indoorUrls" :key="'indoor-' + index"
						@longpress="url && openPopup('indoor', index)">
						<image v-if="url" class="charter" :src="baseURL + url" mode="aspectFit"></image>
						<view class="btnBox" :class="{ uploaded: indoorUploadStatuses[index], failed: indoorUploadFailed[index] }"
							:style="{ height: url ? '64rpx' : '0px' }">
							<view class="btn" v-if="indoorUploadStatuses[index]">上传成功</view>
							<view class="btn" v-else-if="indoorUploadFailed[index]">上传失败，请重新上传</view>
						</view>
					</view>
					<!-- 上传按钮继续存在，直到上传的图片达到3张时隐藏 -->
					<view v-if="canUploadIndoor" class="img">
						<image class="charter" src="" mode="aspectFit"></image>
						<image class="addIcon" src="@/static/images/addIcon.png" mode="aspectFit"
							@click="uploadIndoorImage(indoorUrls.length)"></image>
						<view class="btnBox">
							<view class="btn">上传图片</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 上传店外场景 -->
			<view class="uploadingMessage">
				<view class="title">店外场景</view>
				<view class="" style="display: flex; justify-content: space-between; flex-wrap: wrap">
					<!-- 显示已上传的图片，支持长按删除 -->
					<view class="img" v-for="(url, index) in outdoorUrls" :key="'outdoor-' + index"
						@longpress="url && openPopup('outdoor', index)">
						<image v-if="url" class="charter" :src="baseURL + url" mode="aspectFit"></image>
						<view class="btnBox" :class="{ uploaded: outdoorUploadStatuses[index], failed: outdoorUploadFailed[index] }"
							:style="{ height: url ? '64rpx' : '0px' }">
							<view class="btn" v-if="outdoorUploadStatuses[index]">上传成功</view>
							<view class="btn" v-else-if="outdoorUploadFailed[index]">上传失败，请重新上传</view>
						</view>
					</view>
					<!-- 上传按钮继续存在，直到上传的图片达到1张时隐藏 -->
					<view v-if="canUploadOutdoor" class="img">
						<image class="charter" src="" mode="aspectFit"></image>
						<image class="addIcon" src="@/static/images/addIcon.png" mode="aspectFit"
							@click="uploadOutdoorImage(outdoorUrls.length)"></image>
						<view class="btnBox">
							<view class="btn">上传图片</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 上传面签照 -->
			<view class="uploadingMessage" v-if="currentGangway == 'changsha_bank'">
				<view class="title">面签照</view>
				<view class="" style="display: flex; justify-content: space-between; flex-wrap: wrap">
					<!-- 显示已上传的图片，支持长按删除 -->
					<view class="img" v-for="(url, index) in photoRefereeMerchUrls" :key="'photoRefereeMerch-' + index"
						@longpress="url && openPopup('photoRefereeMerch', index)">
						<image v-if="url" class="charter" :src="baseURL + url" mode="aspectFit"></image>
						<view class="btnBox"
							:class="{ uploaded: photoRefereeMerchUploadStatuses[index], failed: photoRefereeMerchUploadFailed[index] }"
							:style="{ height: url ? '64rpx' : '0px' }">
							<view class="btn" v-if="photoRefereeMerchUploadStatuses[index]">上传成功</view>
							<view class="btn" v-else-if="photoRefereeMerchUploadFailed[index]">上传失败，请重新上传</view>
						</view>
					</view>
					<!-- 上传按钮，限制为最多上传1张 -->
					<view v-if="canUploadPhotoRefereeMerch" class="img">
						<image class="charter" src="" mode="aspectFit"></image>
						<image class="addIcon" src="@/static/images/addIcon.png" mode="aspectFit"
							@click="uploadPhotoRefereeMerchImage(photoRefereeMerchUrls.length)"></image>
						<view class="btnBox">
							<view class="btn">上传图片</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="" style="width: 100%; height: 226rpx"></view>

		<view class="bottomBox">
			<view class="box">
				<view class="backBox" @click="back">
					<view class="text">上一步</view>
				</view>
				<view class="nextBox" :class="{ active: allUploaded }" @click="next">
					<view class="text">下一步</view>
				</view>
			</view>
		</view>
		<!-- 图片长摁弹窗组件 -->
		<PoperImg :show="isPopupVisible" @close="closePopup" @delete="deleteImage" @reupload="reuploadImage" />
	</view>
</template>

<script setup>
import { ref, reactive, computed, inject, onMounted, onBeforeUnmount } from 'vue';
import navbar from '@/components/navbar/navbar.vue';
import StepBar from '@/components/steps.vue';
import PoperImg from '@/components/PoperImg.vue';
import useUserStore from '@/store/user';
import { updateShopScene } from '@/api/shopScene';
import { getMerchantApi } from '@/api/merchant';

/** 获取位置信息 */
function otgetLocation () {
	uni.getLocation({
		type: 'gcj02', // 返回可以用于uni.openLocation的经纬度
		success: function (res) {
			/** 把位置信息存储到本地 */
			uni.setStorageSync('longitude', res.longitude);
			uni.setStorageSync('latitude', res.latitude);
		},
		fail: function (err) {
			console.log('获取位置信息失败：' + JSON.stringify(err));
		}
	});
}

const baseURL = inject('baseURL');
const userStore = useUserStore();
const statusBarHeight = uni.getStorageSync('statusBarHeight');

const merchant_id = ref('');
const isPopupVisible = ref(false);
const currentImageType = ref('');
const currentImageIndex = ref(0);
const currentGangway = ref(''); // 长沙银行通道字段

// 限制每个部分的最大上传数量
const maxHeaderImages = 2; // 门头最多上传2张图片
const maxCashierImages = 2; // 收银台最多上传2张图片
const maxIndoorImages = 3; // 店铺场景最多上传3张图片
const maxOutdoorImages = 1; // 店外场景最多上传1张图片

// 定义图片上传数据
const headerUrls = reactive(['', '']);
const headerUploadStatuses = reactive([false, false]);
const headerUploadFailed = reactive([false, false]);

const cashierUrls = reactive(['', '']);
const cashierUploadStatuses = reactive([false, false]);
const cashierUploadFailed = reactive([false, false]);

const indoorUrls = reactive(['', '', '', '']);
const indoorUploadStatuses = reactive([false, false, false]);
const indoorUploadFailed = reactive([false, false, false]);

const outdoorUrls = reactive(['', '']);
const outdoorUploadStatuses = reactive([false]);
const outdoorUploadFailed = reactive([false]);

// 面签照的上传限制
const maxPhotoRefereeMerchImages = 1; // 面签照最多上传1张图片
const photoRefereeMerchUrls = reactive(['', '']);
const photoRefereeMerchUploadStatuses = reactive([false]);
const photoRefereeMerchUploadFailed = reactive([false]);

// 判断是否已上传完成所有必要的图片
const allUploaded = computed(() => {
	const isChangshaBank = currentGangway.value === 'changsha_bank';
	return (
		headerUrls.some((url) => url) &&
		cashierUrls.some((url) => url) &&
		indoorUrls.some((url) => url) &&
		outdoorUrls.some((url) => url) &&
		(!isChangshaBank || photoRefereeMerchUrls.some((url) => url))
	);
});

// 动态计算是否可以继续上传
const canUploadHeader = computed(() => headerUrls.filter((url) => !!url).length < maxHeaderImages);
const canUploadCashier = computed(() => cashierUrls.filter((url) => !!url).length < maxCashierImages);
const canUploadIndoor = computed(() => indoorUrls.filter((url) => !!url).length < maxIndoorImages);
const canUploadOutdoor = computed(() => outdoorUrls.filter((url) => !!url).length < maxOutdoorImages);
const canUploadPhotoRefereeMerch = computed(() => photoRefereeMerchUrls.filter((url) => !!url).length < maxPhotoRefereeMerchImages);

// 获取商户数据
const fetchMerchantData = async (merchantId) => {
	const response = await getMerchantApi(merchantId);
	if (response.code === 200) {
		const data = response.data;
		headerUrls[0] = data.merchant_img_door_shot || '';
		headerUrls[1] = data.merchant_img_door_shot_2 || '';
		cashierUrls[0] = data.merchant_img_checkout_counter || '';
		cashierUrls[1] = data.merchant_img_checkout_counter_2 || '';
		indoorUrls[0] = data.merchant_img_shop_interior || '';
		indoorUrls[1] = data.merchant_img_shop_interior_2 || '';
		indoorUrls[2] = data.merchant_img_shop_interior_3 || '';
		outdoorUrls[0] = data.merchant_img_store_photo || '';
		photoRefereeMerchUrls[0] = data.merchant_img_photoRefereeMerch || ''; // 添加面签照数据

		// 更新上传状态
		headerUploadStatuses[0] = !!headerUrls[0];
		headerUploadStatuses[1] = !!headerUrls[1];
		cashierUploadStatuses[0] = !!cashierUrls[0];
		cashierUploadStatuses[1] = !!cashierUrls[1];
		indoorUploadStatuses[0] = !!indoorUrls[0];
		indoorUploadStatuses[1] = !!indoorUrls[1];
		indoorUploadStatuses[2] = !!indoorUrls[2];
		outdoorUploadStatuses[0] = !!outdoorUrls[0];
		photoRefereeMerchUploadStatuses[0] = !!photoRefereeMerchUrls[0]; // 更新面签照上传状态
		currentGangway.value = data.current_gangway;
	}
};

onBeforeUnmount(async () => {
	handleBeforeUnload();
});

onMounted(async () => {
	otgetLocation();
	merchant_id.value = uni.getStorageSync('merchant_id');
	if (merchant_id.value) {
		await fetchMerchantData(merchant_id.value);
	}
});

const handleBeforeUnload = async () => {
	// 用户正在离开页面时保存数据
	const data = {
		merchant_img_door_shot: headerUrls.find((url) => url) || null,
		merchant_img_door_shot_2: headerUrls.find((url, index) => index !== headerUrls.indexOf(headerUrls.find((url) => url))) || null,
		merchant_img_checkout_counter: cashierUrls.find((url) => url) || null,
		merchant_img_checkout_counter_2: cashierUrls.find((url, index) => index !== cashierUrls.indexOf(cashierUrls.find((url) => url))) || null,
		merchant_img_shop_interior: indoorUrls.find((url) => url) || null,
		merchant_img_shop_interior_2: indoorUrls.find((url, index) => index !== indoorUrls.indexOf(indoorUrls.find((url) => url))) || null,
		merchant_img_shop_interior_3: indoorUrls.find((url, index) => index !== indoorUrls.indexOf(indoorUrls.find((url) => url))) || null,
		merchant_img_store_photo: outdoorUrls.find((url) => url) || null,
		merchant_img_photoRefereeMerch: photoRefereeMerchUrls.find((url) => url) || null,
		temp_save: 1,
		id: merchant_id.value
	};
	await updateShopScene(data);
};

// 上传图片逻辑
const uploadImage = async (urlRef, isUploadedRef, uploadFailedRef, index, type) => {
	uni.chooseImage({
		sourceType: ['album', 'camera'],
		count: 1,
		success: (chooseImageRes) => {
			const tempFilePaths = chooseImageRes.tempFilePaths;
			uni.uploadFile({
				url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
				filePath: tempFilePaths[0],
				name: 'file',
				formData: { type: '', id: merchant_id.value },
				header: {
					token: userStore.token
				},
				success: (res) => {
					const data = JSON.parse(res.data);
					if (data.code !== 200) {
						uni.showToast({ title: data.msg, icon: 'none' });
						uploadFailedRef[index] = true;
						return;
					}
					urlRef[index] = data.path;
					isUploadedRef[index] = true;
					uploadFailedRef[index] = false;
				}
			});
		}
	});
};

// 上传门头图片
const uploadHeaderImage = (index) => {
	if (canUploadHeader.value) {
		uploadImage(headerUrls, headerUploadStatuses, headerUploadFailed, index, 'header');
	} else {
		uni.showToast({ title: '最多上传2张门头图片', icon: 'none' });
	}
};

// 上传收银台图片
const uploadCashierImage = (index) => {
	if (canUploadCashier.value) {
		uploadImage(cashierUrls, cashierUploadStatuses, cashierUploadFailed, index, 'cashier');
	} else {
		uni.showToast({ title: '最多上传2张收银台图片', icon: 'none' });
	}
};

// 上传店内场景图片
const uploadIndoorImage = (index) => {
	if (canUploadIndoor.value) {
		uploadImage(indoorUrls, indoorUploadStatuses, indoorUploadFailed, index, 'indoor');
	} else {
		uni.showToast({ title: '最多上传3张店内场景图片', icon: 'none' });
	}
};

// 上传店外图片
const uploadOutdoorImage = (index) => {
	if (canUploadOutdoor.value) {
		uploadImage(outdoorUrls, outdoorUploadStatuses, outdoorUploadFailed, index, 'outdoor');
	} else {
		uni.showToast({ title: '最多上传1张店外图片', icon: 'none' });
	}
};

// 上传面签照图片
const uploadPhotoRefereeMerchImage = (index) => {
	if (canUploadPhotoRefereeMerch.value) {
		uploadImage(photoRefereeMerchUrls, photoRefereeMerchUploadStatuses, photoRefereeMerchUploadFailed, index, 'photoRefereeMerch');
	} else {
		uni.showToast({ title: '最多上传1张面签照', icon: 'none' });
	}
};

// 处理图片的长按操作
const openPopup = (type, index) => {
	currentImageType.value = type;
	currentImageIndex.value = index;
	isPopupVisible.value = true;
};

const closePopup = () => {
	isPopupVisible.value = false;
	currentImageType.value = '';
	currentImageIndex.value = 0;
};

const deleteImage = () => {
	const index = currentImageIndex.value;
	if (currentImageType.value === 'header') {
		headerUrls.splice(index, 1); // 删除指定索引的图片
		headerUploadStatuses.splice(index, 1);
		headerUploadFailed.splice(index, 1);
	} else if (currentImageType.value === 'cashier') {
		cashierUrls.splice(index, 1);
		cashierUploadStatuses.splice(index, 1);
		cashierUploadFailed.splice(index, 1);
	} else if (currentImageType.value === 'indoor') {
		indoorUrls.splice(index, 1); // 删除指定索引的图片
		indoorUploadStatuses.splice(index, 1);
		indoorUploadFailed.splice(index, 1);
	} else if (currentImageType.value === 'outdoor') {
		outdoorUrls.splice(index, 1); // 删除指定索引的图片
		outdoorUploadStatuses.splice(index, 1);
		outdoorUploadFailed.splice(index, 1);
	} else if (currentImageType.value === 'photoRefereeMerch') {
		photoRefereeMerchUrls.splice(index, 1); // 删除指定索引的面签照
		photoRefereeMerchUploadStatuses.splice(index, 1);
		photoRefereeMerchUploadFailed.splice(index, 1);
	}
	closePopup(); // 关闭弹窗
};

const reuploadImage = () => {
	const index = currentImageIndex.value;
	if (currentImageType.value === 'header') {
		uploadHeaderImage(index);
	} else if (currentImageType.value === 'cashier') {
		uploadCashierImage(index);
	} else if (currentImageType.value === 'indoor') {
		uploadIndoorImage(index);
	} else if (currentImageType.value === 'outdoor') {
		uploadOutdoorImage(index);
	} else if (currentImageType.value === 'photoRefereeMerch') {
		uploadPhotoRefereeMerchImage(index); // 重新上传面签照
	}
	closePopup();
};

// 下一步按钮的逻辑
const next = async () => {
	if (allUploaded.value) {
		const data = {
			merchant_img_door_shot: headerUrls.find((url) => url) || null,
			merchant_img_door_shot_2: headerUrls.find((url, index) => index !== headerUrls.indexOf(headerUrls.find((url) => url))) || null,
			merchant_img_checkout_counter: cashierUrls.find((url) => url) || null,
			merchant_img_checkout_counter_2: cashierUrls.find((url, index) => index !== cashierUrls.indexOf(cashierUrls.find((url) => url))) || null,
			merchant_img_shop_interior: indoorUrls.find((url) => url) || null,
			merchant_img_shop_interior_2: indoorUrls.find((url, index) => index !== indoorUrls.indexOf(indoorUrls.find((url) => url))) || null,
			merchant_img_shop_interior_3: indoorUrls.find((url, index) => index !== indoorUrls.indexOf(indoorUrls.find((url) => url))) || null,
			merchant_img_store_photo: outdoorUrls.find((url) => url) || null,
			merchant_img_photoRefereeMerch: photoRefereeMerchUrls.find((url) => url) || null,
			temp_save: 0,
			id: merchant_id.value,
			longitude: uni.getStorageSync('longitude') || '', // 获取经度
			latitude: uni.getStorageSync('latitude') || '' // 获取纬度
		};
		const res = await updateShopScene(data);
		if (res.code === 200) {
			// 将 save_page 存储为 4
			uni.setStorageSync('save_page', 4);
			uni.redirectTo({
				url: '/pages/debitCard/debitCard'
			});
		} else {
			uni.showToast({ title: res.msg, icon: 'none' });
		}
	} else {
		uni.showToast({ title: '请上传所有图片', icon: 'none' });
	}
};

// 上一步按钮的逻辑
const back = async () => {
	const data = {
		merchant_img_door_shot: headerUrls.find((url) => url) || null,
		merchant_img_door_shot_2: headerUrls.find((url, index) => index !== headerUrls.indexOf(headerUrls.find((url) => url))) || null,
		merchant_img_checkout_counter: cashierUrls.find((url) => url) || null,
		merchant_img_checkout_counter_2: cashierUrls.find((url, index) => index !== cashierUrls.indexOf(cashierUrls.find((url) => url))) || null,
		merchant_img_shop_interior: indoorUrls.find((url) => url) || null,
		merchant_img_shop_interior_2: indoorUrls.find((url, index) => index !== indoorUrls.indexOf(indoorUrls.find((url) => url))) || null,
		merchant_img_shop_interior_3: indoorUrls.find((url, index) => index !== indoorUrls.indexOf(indoorUrls.find((url) => url))) || null,
		merchant_img_store_photo: outdoorUrls.find((url) => url) || null,
		temp_save: 1,
		id: merchant_id.value,
		longitude: uni.getStorageSync('longitude') || '', // 获取经度
		latitude: uni.getStorageSync('latitude') || '' // 获取纬度
	};
	await updateShopScene(data);
	uni.redirectTo({
		url: '/pages/corporateIdentityCard/corporateIdentityCard'
	});
};
</script>

<style lang="scss" scoped>
.content {
	width: 100%;
	height: 100%;
	padding-top: 164rpx !important;

	.steps {
		width: 100%;
		height: 164rpx;
		background-color: #ffffff;
	}

	.schemeSelection {
		padding: 0 32rpx;
		margin-top: 56rpx;

		.title {
			font-size: 44rpx;
			margin-bottom: 8rpx;
		}

		.hint {
			font-size: 24rpx;
			color: #9da5ad;
		}
	}

	.uploading {
		width: 686rpx;
		height: auto;
		background-color: #ffffff;
		margin-left: 32rpx;
		margin-top: 32rpx;
		padding-bottom: 32rpx;
		box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
		border: 1rpx solid #edf0f5;
		border-radius: 24rpx;

		.uploadingMessage {
			padding: 0 32rpx;
			margin-top: 40rpx;

			.title {
				font-weight: 500;
				font-size: 30rpx;
				color: #18191a;
			}

			.text {
				font-weight: 400;
				font-size: 24rpx;
				color: #9da5ad;
				margin-top: 8rpx;
			}

			.img {
				position: relative;

				.charter {
					width: 304rpx;
					height: 200rpx;
					background-color: #f9fafb;
					margin-top: 32rpx;
				}

				.addIcon {
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					width: 104rpx;
					height: 104rpx;
				}
			}

			.btnBox {
				width: 304rpx;
				height: 64rpx;
				background-color: #c1dffe;
				border-radius: 0rpx 0rpx 8rpx 8rpx;

				&.uploaded {
					background-color: #aadb9a;

					/* 背景颜色 */
					.btn {
						color: #198703;
						/* 文字颜色改变 */
					}
				}

				&.failed {
					background-color: #e6b9b5;

					.btn {
						color: #b91810;
					}
				}

				.btn {
					line-height: 64rpx;
					text-align: center;
					font-size: 26rpx;
					color: #0056ad;
				}
			}
		}
	}

	.bottomBox {
		position: fixed;
		box-shadow: 0rpx -2rpx 4rpx 0rpx rgba(75, 80, 85, 0.06);
		background: #ffffff;
		margin-top: 58rpx;
		width: 100%;
		height: 172rpx;
		bottom: 0;
		left: 0;
		padding: 0 32rpx;

		.box {
			display: flex;

			.backBox {
				width: 218rpx;
				height: 88rpx;
				border: 2rpx solid #1b7de1;
				border-radius: 8rpx;
				margin-top: 24rpx;
				margin-right: 16rpx;

				.text {
					line-height: 88rpx;
					text-align: center;
					color: #1386fb;
					font-weight: 600;
					font-size: 26rpx;
				}
			}

			.nextBox {
				width: 452rpx;
				height: 94rpx;
				background: #dee2e8;
				border-radius: 8rpx;
				margin-top: 24rpx;

				&.active {
					background: #1386fb;

					.text {
						color: #ffffff;
					}
				}

				.text {
					line-height: 94rpx;
					text-align: center;
					color: #9da5ad;
					font-size: 26rpx;
					font-weight: 500;
				}
			}
		}
	}

	.nextBox.active {
		background: #1386fb;
	}

	.nextBox.active .text {
		color: #ffffff;
	}
}
</style>
