// app版本相关信息
/*#ifdef APP-PLUS*/
import { defineStore } from 'pinia';
import { ref } from 'vue';
import { getLastAppVersion } from '@/api/user';
//1.定义Store
const useVersionStore = defineStore(
	'versionCtr',
	() => {
		/**APP当前版本号*/
		const version = ref(null)
		/** 接口获取的版本号*/
		const new_version = ref(null)
		/** 更新信息*/
		const content = ref(null)
		/** apk的下载地址*/
		const down_url = ref(null)
		/** 是否强制更新*/
		const is_update = ref(null)

		/**检查更新 */
		function checkUpdate() {
			plus.runtime.getProperty(plus.runtime.appid, function (wgtinfo) {
				if (wgtinfo.version) {
					version.value = wgtinfo.version
					uni.getSystemInfo({
						success(res) {
							if (res.osName == 'ios') {
								platformIOS()
							} else {
								platform()
							}
						}
					})
				}
			});
		}

		async function platform() {
			const res : any = await getLastAppVersion(1)
			if (res.code == 200) {
				new_version.value = res.data.new_version
				content.value = res.data.content
				down_url.value = res.data.url
				is_update.value = res.data.is_update
			}
			if (new_version.value != version.value) {
				/** 判断是否强制更新 */
				if (is_update.value == 1) {
					appdownLoadForce()
				}
				if (is_update.value == 0) {
					uni.showModal({
						title: "发现新版本",
						content: "确认下载更新",
						success: (res) => {
							if (res.confirm == true) {
								appdownLoad();
							} else {
								return false
							}
						}
					})
				}
			}
		}

		function appdownLoad() {
			uni.showLoading({
				title: '安装包下载中……',
				mask: true
			})
			uni.downloadFile({
				url: down_url.value, //app 的下载地址
				success: (downloadResult) => {
					uni.hideLoading();
					if (downloadResult.statusCode === 200) {
						uni.showModal({
							title: '',
							content: '下载成功，是否允许安装新版本？',
							confirmText: '确定',
							success: function (res) {
								if (res.confirm == true) {
									plus.runtime.install( //安装
										downloadResult.tempFilePath, {
										force: true
									},
										function (res) {
											uni.utils.toast('更新成功，重启中');
											plus.runtime.restart();
										}
									);
								}
							}
						});
					}
				}
			});
		}
		function appdownLoadForce() {
			uni.showLoading({
				title: '安装包下载中……',
				mask: true
			})
			uni.downloadFile({
				url: down_url.value, //app 的下载地址
				success: (downloadResult) => {
					uni.hideLoading();
					if (downloadResult.statusCode === 200) {
						plus.runtime.install( //安装
							downloadResult.tempFilePath, { force: true }, function (res) {
								uni.utils.toast('更新成功，重启中');
								plus.runtime.restart();
							}
						);
					} else {
						uni.utils.toast('下载失败')
					}
				}
			});
		}

		async function platformIOS() {
			const res : any = await getLastAppVersion(2)
			if (res.code == 200) {
				new_version.value = res.data.new_version
				content.value = res.data.content
				down_url.value = res.data.url
				is_update.value = res.data.is_update
			}
			if (new_version.value != version.value) {
				/** 判断是否强制更新 */
				if (is_update.value == 1) {
					appdownLoadIOSForce()
				}
				if (is_update.value == 0) {
					uni.showModal({
						title: "发现新版本",
						content: "确认下载更新",
						success: (res) => {
							if (res.confirm == true) {
								appdownLoadIOS();
							} else {
								return false
							}
						}
					})
				}
			}
		}
		function appdownLoadIOS() {
			uni.showLoading({
				title: '安装包下载中……',
				mask: true
			})
			uni.downloadFile({
				url: down_url.value, //服务器 app 的下载地址
				success: (downloadResult) => {
					uni.hideLoading();
					if (downloadResult.statusCode === 200) {
						uni.showModal({
							title: '',
							content: '下载成功，是否允许安装新版本？',
							confirmText: '确定',
							success: function (res) {
								if (res.confirm == true) {
									plus.runtime.install( //安装
										downloadResult.tempFilePath, {
										force: true
									},
										function (res) {
											uni.utils.toast('更新成功，重启中');
											plus.runtime.restart();
										}
									);
								}
							}
						});
					}
				}
			});
		}
		function appdownLoadIOSForce() {
			uni.showLoading({
				title: '安装包下载中……',
				mask: true
			})
			uni.downloadFile({
				url: down_url.value, //服务器 app 的下载地址
				success: (downloadResult) => {
					uni.hideLoading();
					if (downloadResult.statusCode === 200) {
						plus.runtime.install( //安装
							downloadResult.tempFilePath, { force: true }, function (res) {
								uni.utils.toast('更新成功，重启中');
								plus.runtime.restart();
							}
						);
					} else {
						uni.utils.toast('下载失败')
					}
				}
			});
		}

		/* ---------- */

		/**关于页面检查更新 */
		function checkUpdate1() {
			plus.runtime.getProperty(plus.runtime.appid, function (wgtinfo) {
				if (wgtinfo.version) {
					version.value = wgtinfo.version
					uni.getSystemInfo({
						success(res) {
							if (res.osName == 'ios') {
								platformIOS1()
							} else {
								platform1()
							}
						}
					})
				}
			});
		}

		async function platformIOS1() {
			const res : any = await getLastAppVersion(2)
			if (res.code == 200) {
				new_version.value = res.data.new_version
				content.value = res.data.content
				down_url.value = res.data.url
				is_update.value = res.data.is_update
			}
			if (new_version.value != version.value) {
				appdownLoadIOSForce()
			}else{
				uni.utils.toast('已是最新版本')
			}
		}

		async function platform1() {
			const res : any = await getLastAppVersion(1)
			if (res.code == 200) {
				new_version.value = res.data.new_version
				content.value = res.data.content
				down_url.value = res.data.url
				is_update.value = res.data.is_update
			}
			if (new_version.value != version.value) {
				appdownLoadForce()
			}else{
				uni.utils.toast('已是最新版本')
			}
		}

		return {
			checkUpdate,
			version,
			new_version,
			content,
			down_url,
			is_update,
			checkUpdate1
		}
	}
)
//2.默认导出
export default useVersionStore;