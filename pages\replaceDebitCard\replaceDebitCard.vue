<template>
  <view class="">
    <!-- 导航栏 -->
    <navbar title="更换结算卡" backGroundColor="#fff" :isAutoBack="true" />
  </view>
  <view class="content">
    <view class="schemeSelection">
      <view class="title">
        结算类型
        <span class="required">*</span>
      </view>
      <view class="elect">
        <view class="list" v-for="(item, index) in settlementTypes" :key="index" :class="{ active: selectedType === index }" @click="selectType(index,item)">
          <text class="text">{{ item }}</text>
          <image v-if="selectedType === index" src="@/static/images/gouxuan.png" class="icon"></image>
        </view>
      </view>
    </view>
    <view v-if="selectedName === '对私法人账户' || selectedName === '对私非法人账户'" class="schemeSelection">
      <view class="" style="display: flex; justify-content: space-between">
        <view class="title">上传结算卡</view>
      </view>
      <p class="hint">请上传真实有效信息，以下信息将用于各类申请</p>
    </view>
    <view v-if="selectedName === '对私非法人账户'" class="uploading">
      <view class="uploadingMessage">
        <view class="title">请上传结算卡</view>
        <view class="text">请上传结算卡正反面，大小不超过2M</view>
      </view>
      <view class="uploadingImages">
        <view class="uploadBox" @longpress="frontImageUrl && openPopup('front')">
          <image class="charter" v-if="frontImageUrl" :src="baseURL + frontImageUrl" mode="aspectFit"></image>
          <image class="charter" v-else src="@/static/images/jiesuanka.png" mode="aspectFit" @click="uploadFrontImage"></image>
          <view class="btnBox" :class="{ uploaded: isFrontImageUploaded, failed: frontImageUploadFailed }" @click="uploadFrontImage">
            <view class="btn" v-if="isFrontImageUploaded">上传成功</view>
            <view class="btn" v-else-if="frontImageUploadFailed">上传失败!</view>
            <view class="btn" v-else>上传结算卡正面</view>
          </view>
        </view>
        <view class="uploadBox" @longpress="backImageUrl && openPopup('back')">
          <image class="charter" v-if="backImageUrl" :src="baseURL + backImageUrl" mode="aspectFit"></image>
          <image class="charter" v-else src="@/static/images/jiesuanka.png" mode="aspectFit" @click="uploadBackImage"></image>
          <view class="btnBox" :class="{ uploaded: isBackImageUploaded, failed: backImageUploadFailed }" @click="uploadBackImage">
            <view class="btn" v-if="isBackImageUploaded">上传成功</view>
            <view class="btn" v-else-if="backImageUploadFailed">上传失败!</view>
            <view class="btn" v-else>上传结算卡反面</view>
          </view>
        </view>
      </view>
    </view>
    <view v-if="selectedName === '对公账户'" class="schemeSelection">
      <view class="" style="display: flex; justify-content: space-between">
        <view class="title">上传开户许可证</view>
      </view>
      <p class="hint">请上传真实有效信息，以下信息将用于各类申请</p>
    </view>
    <view v-if="selectedName === '对公账户'" class="uploading" style="height: 450rpx">
      <view class="uploadingImages" style="justify-content: center">
        <view style="width: 410rpx; margin-top: 40rpx" class="uploadBox" @longpress="merchantImgPermitImageUrl && openPopup('merchantImgPermit')">
          <image class="charter" v-if="merchantImgPermitImageUrl" :src="baseURL + merchantImgPermitImageUrl" mode="aspectFit"></image>
          <image class="charter" v-else src="@/static/images/jiesuanka.png" mode="aspectFit" @click="uploadmerchantImgPermitImage"></image>
          <view class="btnBox" :class="{ uploaded: ********************************, failed: merchantImgPermitImageUploadFailed }" @click="uploadmerchantImgPermitImage">
            <view class="btn" v-if="********************************">上传成功</view>
            <view class="btn" v-else-if="merchantImgPermitImageUploadFailed">上传失败!</view>
            <view class="btn" v-else>开户许可证</view>
          </view>
        </view>
      </view>
    </view>
    <view v-if="selectedName === '对私非法人账户'" class="uploading" style="height: 1000rpx">
      <view class="uploadingMessage">
        <view class="title">请上传结算卡</view>
        <view class="text">请上传结算卡正反面，大小不超过2M</view>
      </view>
      <view class="uploadingImages">
        <view class="uploadBox" @longpress="frontImageUrl && openPopup('front')">
          <image class="charter" v-if="frontImageUrl" :src="baseURL + frontImageUrl" mode="aspectFit"></image>
          <image class="charter" v-else src="@/static/images/jiesuanka.png" mode="aspectFit" @click="uploadFrontImage"></image>
          <view class="btnBox" :class="{ uploaded: isFrontImageUploaded, failed: frontImageUploadFailed }" @click="uploadFrontImage">
            <view class="btn" v-if="isFrontImageUploaded">上传成功</view>
            <view class="btn" v-else-if="frontImageUploadFailed">上传失败!</view>
            <view class="btn" v-else>上传结算卡正面</view>
          </view>
        </view>
        <view class="uploadBox" @longpress="backImageUrl && openPopup('back')">
          <image class="charter" v-if="backImageUrl" :src="baseURL + backImageUrl" mode="aspectFit"></image>
          <image class="charter" v-else src="@/static/images/jiesuanka.png" mode="aspectFit" @click="uploadBackImage"></image>
          <view class="btnBox" :class="{ uploaded: isBackImageUploaded, failed: backImageUploadFailed }" @click="uploadBackImage">
            <view class="btn" v-if="isBackImageUploaded">上传成功</view>
            <view class="btn" v-else-if="backImageUploadFailed">上传失败!</view>
            <view class="btn" v-else>上传结算卡反面</view>
          </view>
        </view>
      </view>
      <view class="uploadingMessage">
        <view class="title">请上传授权结算书</view>
        <view class="text">请上传授权予益科技结算书，大小不超过2M</view>
      </view>
      <view class="uploadingImages" style="justify-content: center">
        <view class="uploadBox" @longpress="authorizationImageUrl && openPopup('authorization')">
          <image class="charter" v-if="authorizationImageUrl" :src="baseURL + authorizationImageUrl" mode="aspectFit"></image>
          <image class="charter" v-else src="@/static/images/jiesuanka.png" mode="aspectFit" @click="uploadAuthorizationImage"></image>
          <view class="btnBox" :class="{ uploaded: isAuthorizationImageUploaded, failed: authorizationImageUploadFailed }" @click="uploadAuthorizationImage">
            <view class="btn" v-if="isAuthorizationImageUploaded">上传成功</view>
            <view class="btn" v-else-if="authorizationImageUploadFailed">上传失败!</view>
            <view class="btn" v-else>上传授权结算书</view>
          </view>
        </view>
      </view>
    </view>
    <view class="require">
      <view class="title">拍摄要求</view>
      <view class="imgList">
        <view class="img1">
          <image src="@/static/images/biaozhun.png" mode="widthFix" style="width: 160rpx"></image>
          <view class="text">
            <image src="@/static/images/duihao.png" mode="widthFix" style="width: 32rpx"></image>
            <view class="word">标准</view>
          </view>
        </view>
        <view class="img2">
          <image src="@/static/images/queshi.png" mode="widthFix" style="width: 160rpx"></image>
          <view class="text">
            <image src="@/static/images/cuowu.png" mode="widthFix" style="width: 32rpx"></image>
            <view class="word">边角缺失</view>
          </view>
        </view>
        <view class="img3">
          <image src="@/static/images/mohu.png" mode="widthFix" style="width: 160rpx"></image>
          <view class="text">
            <image src="@/static/images/cuowu.png" mode="widthFix" style="width: 32rpx"></image>
            <view class="word">照片模糊</view>
          </view>
        </view>
        <view class="img4">
          <image src="@/static/images/fanguang.png" mode="widthFix" style="width: 160rpx"></image>
          <view class="text">
            <image src="@/static/images/cuowu.png" mode="widthFix" style="width: 32rpx"></image>
            <view class="word">反光强烈</view>
          </view>
        </view>
      </view>
    </view>
    <view class="messageBox">
      <view class="message">
        <view class="title">账户信息</view>
        <view class="form">
          <up-form labelPosition="left" :model="model1" :rules="rules" ref="form1">
            <up-form-item prop="settlement_account_name" borderBottom>
              <view class="text" style="width: 40%">
                账户名
                <span class="required">*</span>
              </view>
              <up-input border="none" placeholder="请输入账户名" v-model="form.settlement_account_name"></up-input>
            </up-form-item>
            <up-form-item prop="settlement_card_number" borderBottom>
              <view class="text" style="width: 40%">
                账户号
                <span class="required">*</span>
              </view>
              <up-input border="none" placeholder="上传后自动识别或手动输入" v-model="form.settlement_card_number"></up-input>
            </up-form-item>
            <up-form-item prop="settlement_bank" borderBottom>
              <view class="text" style="width: 40%">
                开户行
                <span class="required">*</span>
              </view>
              <up-input border="none" placeholder="上传后自动识别或手动输入" v-model="form.settlement_bank"></up-input>
            </up-form-item>
            <up-form-item prop="settlement_open_subbank" borderBottom>
              <view class="text" style="width: 40%">
                开户支行
                <span class="required">*</span>
              </view>
              <cus-selects
                :data="subbankOptions"
                v-model="form.settlement_open_subbank"
                :bank="form.settlement_bank"
                :city="cityName"
                :filterable="true"
                :value="form.settlement_open_subbank"
                :searchType="1"
                @change="change"
              ></cus-selects>
            </up-form-item>
            <up-form-item prop="settlement_bank_tel" borderBottom>
              <view class="text" style="width: 40%">
                银行预留手机号
                <span class="required">*</span>
              </view>
              <up-input border="none" placeholder="请输入银行预留手机号" v-model="form.settlement_bank_tel"></up-input>
            </up-form-item>
          </up-form>
        </view>
      </view>
    </view>
    <view v-if="selectedName === '对私非法人账户'" class="schemeSelection">
      <view class="" style="display: flex; justify-content: space-between">
        <view class="title">上传结算人身份证</view>
      </view>
      <p class="hint">请上传真实有效信息，以下信息将用于各类申请</p>
    </view>
    <view v-if="selectedName === '对私非法人账户'" class="uploading" style="height: 1000rpx">
      <view class="uploadingMessage">
        <view class="title">请上传身份证</view>
        <view class="text">请上传结算人身份证正反面，大小不超过2M</view>
      </view>
      <view class="uploadingImages">
        <view class="uploadBox" @longpress="idCardFrontUrl && openPopup('idCardFront')">
          <image v-if="idCardFrontUrl" class="charter" :src="baseURL + idCardFrontUrl" mode="aspectFit"></image>
          <image v-else class="charter" src="@/static/images/renxiang.png" mode="aspectFit" @click="uploadIdCardFrontImage"></image>
          <view class="btnBox" :class="{ uploaded: isIdCardFrontUploaded, failed: idCardFrontUploadFailed }" @click="uploadIdCardFrontImage">
            <view class="btn" v-if="isIdCardFrontUploaded">上传成功</view>
            <view class="btn" v-else-if="idCardFrontUploadFailed">无法识别,请重新上传</view>
            <view class="btn" v-else>上传人像页</view>
          </view>
        </view>
        <view class="uploadBox" @longpress="idCardBackUrl && openPopup('idCardBack')">
          <image v-if="idCardBackUrl" class="charter" :src="baseURL + idCardBackUrl" mode="aspectFit"></image>
          <image v-else class="charter" src="@/static/images/guohui.png" mode="aspectFit" @click="uploadIdCardBackImage"></image>
          <view class="btnBox" :class="{ uploaded: isIdCardBackUploaded, failed: idCardBackUploadFailed }" @click="uploadIdCardBackImage">
            <view class="btn" v-if="isIdCardBackUploaded">上传成功</view>
            <view class="btn" v-else-if="idCardBackUploadFailed">无法识别,请重新上传</view>
            <view class="btn" v-else>上传国徽页</view>
          </view>
        </view>
      </view>
      <view class="uploadingMessage">
        <view class="title">请上传结算人手持身份证</view>
        <view class="text">请上传结算人身份证正面，大小不超过2M</view>
      </view>
      <view class="uploadingImages" style="justify-content: center">
        <view class="uploadBox" @click="uploadHandheldIdCardImage" @longpress="handheldIdCardUrl && openPopup('handheld')">
          <image class="charter" v-if="handheldIdCardUrl" :src="baseURL + handheldIdCardUrl" mode="aspectFit"></image>
          <image class="charter" v-else src="@/static/images/renxiang.png" mode="aspectFit"></image>
          <view class="btnBox" :class="{ uploaded: isHandheldIdCardUploaded, failed: handheldIdCardUploadFailed }">
            <view class="btn" v-if="isHandheldIdCardUploaded">上传成功</view>
            <view class="btn" v-else-if="handheldIdCardUploadFailed">上传失败!</view>
            <view class="btn" v-else>上传结算人手持身份证</view>
          </view>
        </view>
      </view>
    </view>
    <view class="messageBox" v-if="selectedName === '对私非法人账户'">
      <view class="message">
        <view class="title">结算人信息</view>
        <view class="form">
          <up-form labelPosition="left" :model="model1" :rules="rules" ref="form1">
            <up-form-item prop="name" borderBottom>
              <view class="text" style="width: 50%">
                结算人姓名
                <span class="required">*</span>
              </view>
              <up-input border="none" placeholder="上传后自动识别或手动输入" v-model="picDataFront.name"></up-input>
            </up-form-item>
            <up-form-item prop="number" borderBottom>
              <view class="text" style="width: 50%">
                结算人身份证号
                <span class="required">*</span>
              </view>
              <up-input border="none" placeholder="上传后自动识别或手动输入" v-model="picDataFront.number"></up-input>
            </up-form-item>
            <up-form-item prop="valid_from" borderBottom>
              <view class="text" style="width: 50%">
                结算人身份证生效日期
                <span class="required">*</span>
              </view>
              <up-input border="none" placeholder="上传后自动识别或手动输入" v-model="picDataBack.valid_from"></up-input>
            </up-form-item>
            <up-form-item prop="valid_to" borderBottom>
              <view class="text" style="width: 50%">
                结算人身份证有效期
                <span class="required">*</span>
              </view>
              <up-input border="none" placeholder="上传后自动识别或手动输入" v-model="picDataBack.valid_to"></up-input>
            </up-form-item>
          </up-form>
        </view>
      </view>
    </view>

    <view class="bottomBox">
      <view class="box">
        <view class="nextBox" :class="{ active: allUploaded }" @click="submit">
          <view class="text">提交</view>
        </view>
      </view>
    </view>

    <view class="" style="width: 100%; height: 226rpx"></view>

    <!-- 图片长摁弹窗组件 -->
    <view class="longPress">
      <PoperImg :show="isPopupVisible" @close="closePopup" @delete="deleteImage" @reupload="reuploadImage" />
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, inject, onMounted, computed, onBeforeUnmount, watch } from 'vue';
import navbar from '@/components/navbar/navbar.vue';
import PoperImg from '@/components/PoperImg.vue';
import qrcode from '@/utils/qrcode.js';
import useUserStore from '@/store/user';
import { submitDebitCard, getBankBranch } from '@/api/debitCard';
import { getMerchantApi } from '@/api/merchant';
import { verifyCardId } from '@/utils/rules';
/** 获取位置信息 */
function otgetLocation() {
  uni.getLocation({
    type: 'gcj02', // 返回可以用于uni.openLocation的经纬度
    success: function (res) {
      /** 把位置信息存储到本地 */
      uni.setStorageSync('longitude', res.longitude);
      uni.setStorageSync('latitude', res.latitude);
    },
    fail: function (err) {
      console.log('获取位置信息失败：' + JSON.stringify(err));
    }
  });
}
const settlementTypes = ref(['对私法人账户', '对私非法人账户', '对公账户']);

const fetchMerchantData = async (merchantId) => {
  const response = await getMerchantApi(merchantId);
  if (response.code === 200) {
    const data = response.data;
	if (data.current_gangway == 'icbc_bank') {
		settlementTypes.value = ['对私法人账户', '对公账户'];
	}
    frontImageUrl.value = data.merchant_img_bank_card || '';
    backImageUrl.value = data.merchant_img_bank_card_back || '';
    authorizationImageUrl.value = data.merchant_img_authorization_statement || '';
    (merchantImgPermitImageUrl.value = data.merchant_img_permit_for_bank_account || ''), (isFrontImageUploaded.value = !!frontImageUrl.value);
    isBackImageUploaded.value = !!backImageUrl.value;
    isAuthorizationImageUploaded.value = !!authorizationImageUrl.value;
    form.settlement_account_name = data.settlement_account_name || '';
    form.settlement_card_number = data.settlement_card_number || '';
    form.settlement_bank = data.settlement_bank || '';
    form.settlement_bank_code = data.settlement_bank_code || '';
    form.settlement_open_subbank = data.settlement_open_subbank || '';
    cityName.value = data.city_name;
    handheldIdCardUrl.value = data.merchant_img_holding_photo || '';
    form.settlement_bank_tel = data.settlement_bank_tel || '';
	selectedName.value = data.settlement_account_type == '00' ? '对公账户' : (data.settlement_account_type == '02' ? '对私非法人账户' : '对私法人账户');
    selectedType.value = data.settlement_account_type_to || 0;
    idCardFrontUrl.value = data.merchant_img_principal_id_card_front;
    idCardBackUrl.value = data.merchant_img_principal_id_card_obverse;
    picDataFront.value = {
      name: data.crp_nm,
      number: data.crp_id
    };
    picDataBack.value = {
      valid_from: data.crp_start_dt,
      valid_to: data.s_legal_exp_dt
    };
    isIdCardFrontUploaded.value = !!idCardFrontUrl.value;
    isIdCardBackUploaded.value = !!idCardBackUrl.value;
    showAlert.value = isIdCardFrontUploaded.value && isIdCardBackUploaded.value;
  }
};

const form = reactive({
  settlement_bank: '',
  settlement_open_subbank: '',
  settlement_account_name: '',
  settlement_bank_code: '',
  settlement_card_number: '',
  settlement_bank_tel: ''
});

const change = (e) => {
  form.settlement_open_subbank = e.label;
  form.settlement_bank_code = e.value;
};

onBeforeUnmount(async () => {
  handleBeforeUnload();
});

onMounted(async () => {
  otgetLocation();
  merchant_id.value = uni.getStorageSync('merchant_id');
  if (merchant_id.value) {
    await fetchMerchantData(merchant_id.value);
  }
});

const baseURL = inject('baseURL');

/** 获取顶部状态栏高度 */
const statusBarHeight = uni.getStorageSync('statusBarHeight');

const selectedType = ref(0);
const selectedName = ref(); //结算账户方式名称
const merchant_id = ref(''); // 商户添加页面传过来的id
const idCardFrontUrl = ref(''); // 身份证正面图片URL
const idCardBackUrl = ref(''); // 身份证反面图片URL
const isIdCardFrontUploaded = ref(false); // 身份证正面是否上传成功
const isIdCardBackUploaded = ref(false); // 身份证反面是否上传成功
const idCardFrontUploadFailed = ref(false); // 身份证正面上传是否失败
const idCardBackUploadFailed = ref(false); // 身份证反面上传是否失败
const handheldIdCardUrl = ref(''); // 手持身份证图片URL
const isHandheldIdCardUploaded = ref(false); // 手持身份证是否上传成功
const handheldIdCardUploadFailed = ref(false); // 手持身份证上传是否失败

const picDataFront = ref({}); // 存储身份证正面识别的数据
const picDataBack = ref({}); // 存储身份证反面识别的数据

const userStore = useUserStore();

const cityName = ref('');

function selectType(index,item) {
  selectedType.value = index;
  selectedName.value = item;
  resetFormAndImages(); // 清空表单和图片数据
  // 清空身份证信息表单字段
  idCardFrontUrl.value = '';
  idCardBackUrl.value = '';
  isIdCardFrontUploaded.value = false;
  isIdCardBackUploaded.value = false;
  idCardFrontUploadFailed.value = false;
  idCardBackUploadFailed.value = false;
  picDataFront.value = {
    name: '',
    number: ''
  };
  picDataBack.value = {
    valid_from: '',
    valid_to: ''
  };
}

function resetFormAndImages() {
  frontImageUrl.value = '';
  backImageUrl.value = '';
  authorizationImageUrl.value = '';
  isFrontImageUploaded.value = false;
  isBackImageUploaded.value = false;
  isAuthorizationImageUploaded.value = false;
  frontImageUploadFailed.value = false;
  backImageUploadFailed.value = false;
  authorizationImageUploadFailed.value = false;
  merchantImgPermitImageUrl.value = '';
  ********************************.value = false;
  merchantImgPermitImageUploadFailed.value = false;
  form.settlement_account_name = '';
  form.settlement_card_number = '';
  form.settlement_bank_code = '';
  form.settlement_bank = '';
  form.settlement_open_subbank = '';
  form.settlement_bank_tel = '';
}

const frontImageUrl = ref('');
const backImageUrl = ref('');
const authorizationImageUrl = ref('');
const isFrontImageUploaded = ref(false); // 结算卡正面上传成功状态
const isBackImageUploaded = ref(false); // 结算卡反面上传成功状态
const isAuthorizationImageUploaded = ref(false); // 授权结算书上传成功状态
const frontImageUploadFailed = ref(false); // 结算卡正面上传失败状态
const backImageUploadFailed = ref(false); // 结算卡反面上传失败状态
const authorizationImageUploadFailed = ref(false); // 授权结算书上传失败状态
const merchantImgPermitImageUploadFailed = ref(false);
const ******************************** = ref(false);
const merchantImgPermitImageUrl = ref('');
const isPopupVisible = ref(false); // 控制弹窗显示状态
const currentImageType = ref(''); // 当前操作的图片类型

const isBindingPopupVisible = ref(false); // 控制绑定付款码弹窗显示状态
const isBindingPopupFacility = ref(false); // 控制绑定设备弹窗显示状态

const showAlert = ref(false); // 显示提示框

const validateDate = (date) => {
	// 如果有效期为“长期”，直接通过校验
	if (date === '长期') {
	  return true;
	}
  // 修改正则表达式以仅匹配有效的日期格式
  const regex = /^(\d{4}-\d{1,2}-\d{1,2}|\d{4}年\d{1,2}月\d{1,2}日)$/;
  if (!regex.test(date)) return false;

  // 验证日期是否合法
  if (date.includes('年')) {
    date = date.replace(/(\d{4})年(\d{1,2})月(\d{1,2})日/, '$1-$2-$3');
  }

  const [year, month, day] = date.split('-').map(Number);
  const dateObj = new Date(year, month - 1, day);
  return dateObj.getFullYear() === year && dateObj.getMonth() + 1 === month && dateObj.getDate() === day;
};

// 打开弹窗
const openPopup = (type) => {
  currentImageType.value = type;
  isPopupVisible.value = true;
};

// 关闭弹窗
const closePopup = () => {
  isPopupVisible.value = false;
  currentImageType.value = '';
};

// 删除图片
const deleteImage = () => {
  if (currentImageType.value === 'front') {
    frontImageUrl.value = '';
    isFrontImageUploaded.value = false;
    frontImageUploadFailed.value = false;
  } else if (currentImageType.value === 'back') {
    backImageUrl.value = '';
    isBackImageUploaded.value = false;
    backImageUploadFailed.value = false;
  } else if (currentImageType.value === 'authorization') {
    authorizationImageUrl.value = '';
    isAuthorizationImageUploaded.value = false;
    authorizationImageUploadFailed.value = false;
  } else if (currentImageType.value === 'merchantImgPermit') {
    merchantImgPermitImageUrl.value = '';
    ********************************.value = false;
    merchantImgPermitImageUploadFailed.value = false;
  } else if (currentImageType.value === 'idCardFront') {
    idCardFrontUrl.value = '';
    isIdCardFrontUploaded.value = false;
    idCardFrontUploadFailed.value = false;
  } else if (currentImageType.value === 'idCardBack') {
    idCardBackUrl.value = '';
    isIdCardBackUploaded.value = false;
    idCardBackUploadFailed.value = false;
  } else if (currentImageType.value === 'handheld') {
    handheldIdCardUrl.value = '';
    isHandheldIdCardUploaded.value = false;
    handheldIdCardUploadFailed.value = false;
  }
  closePopup();
};

// 重新上传图片
const reuploadImage = () => {
  if (currentImageType.value === 'front') {
    uploadFrontImage();
  } else if (currentImageType.value === 'back') {
    uploadBackImage();
  } else if (currentImageType.value === 'authorization') {
    uploadAuthorizationImage();
  } else if (currentImageType.value === 'merchantImgPermit') {
    uploadmerchantImgPermitImage();
  } else if (currentImageType.value === 'idCardFront') {
    uploadIdCardFrontImage();
  } else if (currentImageType.value === 'idCardBack') {
    uploadIdCardBackImage();
  } else if (currentImageType.value === 'handheld') {
    uploadHandheldIdCardImage();
  }
  closePopup();
};

// 上传结算卡正面按钮
const uploadFrontImage = async () => {
  uni.chooseImage({
    sourceType: ['album', 'camera'], // 从相册选择
    count: 1,
    success: (chooseImageRes) => {
      const tempFilePaths = chooseImageRes.tempFilePaths;
      uni.uploadFile({
        url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
        filePath: tempFilePaths[0],
        name: 'file',
        formData: { type: 'bankCardFrontPic', id: '' },
        header: {
          token: userStore.token
        },
        success: (res) => {
          const data = JSON.parse(res.data);
          if (data.code !== 200) {
            uni.showToast({ title: '上传失败!', icon: 'none' });
            frontImageUploadFailed.value = true; // 标记上传失败
            return;
          }
          frontImageUrl.value = data.path;
          form.settlement_card_number = data.data.card_number;
          form.settlement_bank = data.data.bank_name;
          isFrontImageUploaded.value = true; // 标记上传成功
          frontImageUploadFailed.value = false; // 重置上传失败状态
        }
      });
    }
  });
};

// 上传结算卡反面按钮
const uploadBackImage = async () => {
  uni.chooseImage({
    sourceType: ['album', 'camera'], // 从相册选择
    count: 1,
    success: (chooseImageRes) => {
      const tempFilePaths = chooseImageRes.tempFilePaths;
      uni.uploadFile({
        url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
        filePath: tempFilePaths[0],
        name: 'file',
        formData: { type: '', id: '' },
        header: {
          token: userStore.token
        },
        success: (res) => {
          const data = JSON.parse(res.data);
          if (data.code !== 200) {
            uni.showToast({ title: '上传失败!', icon: 'none' });
            backImageUploadFailed.value = true; // 标记上传失败
            return;
          }
          backImageUrl.value = data.path;
          isBackImageUploaded.value = true; // 标记上传成功
          backImageUploadFailed.value = false; // 重置上传失败状态
        }
      });
    }
  });
};

// 上传授权结算书按钮
const uploadAuthorizationImage = async () => {
  uni.chooseImage({
    sourceType: ['album', 'camera'], // 从相册选择
    count: 1,
    success: (chooseImageRes) => {
      const tempFilePaths = chooseImageRes.tempFilePaths;
      uni.uploadFile({
        url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
        filePath: tempFilePaths[0],
        name: 'file',
        formData: { type: '', id: '' },
        header: {
          token: userStore.token
        },
        success: (res) => {
          const data = JSON.parse(res.data);
          if (data.code !== 200) {
            uni.showToast({ title: '上传失败!', icon: 'none' });
            authorizationImageUploadFailed.value = true; // 标记上传失败
            return;
          }
          authorizationImageUrl.value = data.path;
          isAuthorizationImageUploaded.value = true; // 标记上传成功
          authorizationImageUploadFailed.value = false; // 重置上传失败状态
        }
      });
    }
  });
};

// 上传开户许可证按钮
const uploadmerchantImgPermitImage = async () => {
  uni.chooseImage({
    sourceType: ['album', 'camera'], // 从相册选择
    count: 1,
    success: (chooseImageRes) => {
      const tempFilePaths = chooseImageRes.tempFilePaths;
      uni.uploadFile({
        url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
        filePath: tempFilePaths[0],
        name: 'file',
        formData: { type: '', id: '' },
        header: {
          token: userStore.token
        },
        success: (res) => {
          const data = JSON.parse(res.data);
          if (data.code !== 200) {
            uni.showToast({ title: '上传失败!', icon: 'none' });
            merchantImgPermitImageUploadFailed.value = true; // 标记上传失败
            return;
          }
          merchantImgPermitImageUrl.value = data.path;
          ********************************.value = true; // 标记上传成功
          merchantImgPermitImageUploadFailed.value = false; // 重置上传失败状态
        }
      });
    }
  });
};

// 上传身份证正面图片函数
const uploadIdCardFrontImage = async () => {
  uni.chooseImage({
    sourceType: ['album', 'camera'],
    count: 1,
    success: (chooseImageRes) => {
      const tempFilePaths = chooseImageRes.tempFilePaths;
      uni.uploadFile({
        url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
        filePath: tempFilePaths[0],
        name: 'file',
        formData: { type: 'idcardFrontPic', id: merchant_id.value },
        header: { token: userStore.token },
        success: (res) => {
          const data = JSON.parse(res.data);
          if (data.code !== 200) {
            uni.showToast({ title: data.msg, icon: 'none' });
            idCardFrontUploadFailed.value = true;
            return;
          }
          idCardFrontUrl.value = data.path;
          picDataFront.value = data.data;
          isIdCardFrontUploaded.value = true;
          idCardFrontUploadFailed.value = false;
          if (picDataBack.value !== '' && picDataFront.value !== '') {
            showAlert.value = true;
          }
        }
      });
    }
  });
};

// 上传身份证反面图片函数
const uploadIdCardBackImage = async () => {
  uni.chooseImage({
    sourceType: ['album', 'camera'],
    count: 1,
    success: (chooseImageRes) => {
      const tempFilePaths = chooseImageRes.tempFilePaths;
      uni.uploadFile({
        url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
        filePath: tempFilePaths[0],
        name: 'file',
        formData: { type: 'idcardBackPic', id: merchant_id.value },
        header: { token: userStore.token },
        success: (res) => {
          const data = JSON.parse(res.data);
          if (data.code !== 200) {
            uni.showToast({ title: data.msg, icon: 'none' });
            idCardBackUploadFailed.value = true;
            return;
          }
          idCardBackUrl.value = data.path;
          picDataBack.value = data.data;
          isIdCardBackUploaded.value = true;
          idCardBackUploadFailed.value = false;
          if (picDataBack.value !== '' && picDataFront.value !== '') {
            showAlert.value = true;
          }
        }
      });
    }
  });
};

// 上传手持身份证图片函数
const uploadHandheldIdCardImage = async () => {
  uni.chooseImage({
    sourceType: ['album', 'camera'],
    count: 1,
    success: (chooseImageRes) => {
      const tempFilePaths = chooseImageRes.tempFilePaths;
      uni.uploadFile({
        url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
        filePath: tempFilePaths[0],
        name: 'file',
        formData: { type: '', id: '' },
        header: { token: userStore.token },
        success: (res) => {
          const data = JSON.parse(res.data);
          if (data.code !== 200) {
            uni.showToast({ title: data.msg, icon: 'none' });
            handheldIdCardUploadFailed.value = true;
            return;
          }
          handheldIdCardUrl.value = data.path;
          isHandheldIdCardUploaded.value = true;
          handheldIdCardUploadFailed.value = false;
        }
      });
    }
  });
};

// 上一步按钮函数
const back = async () => {
  const data = {
    merchant_id: uni.getStorageSync('merchant_id') || '',
    settlement_bank: form.settlement_bank,
    settlement_open_subbank: form.settlement_open_subbank,
    settlement_account_name: form.settlement_account_name,
    settlement_bank_code: form.settlement_bank_code,
    settlement_card_number: form.settlement_card_number,
    settlement_bank_tel: form.settlement_bank_tel,
	settlement_account_type: selectedName.value === '对私法人账户' ? '01' : selectedName.value === '对私非法人账户' ? '02' : '00',
	merchant_img_authorization_statement: selectedName.value === '对私非法人账户' ? authorizationImageUrl.value : '',
	merchant_img_permit_for_bank_account: selectedName.value === '对公账户' ? merchantImgPermitImageUrl.value : '',
    merchant_img_bank_card: frontImageUrl.value,
    merchant_img_bank_card_back: backImageUrl.value || '',
    merchant_img_principal_id_card_front: idCardFrontUrl.value,
    merchant_img_principal_id_card_obverse: idCardBackUrl.value,
    temp_save: 1,
    merchant_img_holding_photo: handheldIdCardUrl.value, // 添加手持身份证图片URL
    s_legal_name: picDataFront.value.name, // 结算人姓名
    s_legal_id: picDataFront.value.number, // 结算人身份证号
    s_legal_start_dt: picDataBack.value.valid_from, // 结算人身份证生效日期
    s_legal_exp_dt: picDataBack.value.valid_to, // 结算人身份证有效期
    longitude: uni.getStorageSync('longitude') || '', // 获取经度，如果没有则为空
    latitude: uni.getStorageSync('latitude') || '' // 获取纬度，如果没有则为空
  };
  uni.redirectTo({
    url: '/pages/shopScene/shopScene'
  });
};

// 计算属性：检查所有上传状态和表单状态
const allUploaded = computed(() => {
  // 完整验证逻辑
	if (selectedName.value === '对私法人账户') {
    return (
      frontImageUrl.value &&
      // backImageUrl.value &&
      form.settlement_account_name &&
      form.settlement_card_number &&
      form.settlement_bank &&
      form.settlement_open_subbank &&
      form.settlement_bank_tel
    );
  }
  if (selectedName.value === '对私非法人账户') {
    return (
      frontImageUrl.value &&
      // backImageUrl.value &&
      authorizationImageUrl.value &&
      idCardFrontUrl.value &&
      idCardBackUrl.value &&
      handheldIdCardUrl.value &&
      form.settlement_account_name &&
      form.settlement_card_number &&
      form.settlement_bank &&
      form.settlement_open_subbank &&
      form.settlement_bank_tel &&
      picDataFront.value.name &&
      picDataFront.value.number &&
      picDataBack.value.valid_from &&
      validateDate(picDataBack.value.valid_to) &&
      new Date(picDataBack.value.valid_to) > new Date()
    );
  }
  if (selectedName.value === '对公账户') {
    return (
      merchantImgPermitImageUrl.value &&
      form.settlement_account_name &&
      form.settlement_card_number &&
      form.settlement_bank &&
      form.settlement_open_subbank &&
      form.settlement_bank_tel
    );
  }
  return false;
});

// 提交按钮函数
const submit = async () => {
  // 校验结算类型是否已选择
  if (!selectedType.value && selectedType.value !== 0) {
    uni.showToast({ title: '请选择结算类型', icon: 'none' });
    return;
  }

  if (selectedName.value === '对私法人账户') {
    // 对私法人账户
    if (!frontImageUrl.value) {
      uni.showToast({ title: '请上传结算卡正面图片', icon: 'none' });
      return;
    }

    // 校验账户信息
    if (selectedName.value === '对私法人账户' || selectedName.value === '对公账户') {
      if (!form.settlement_account_name || !form.settlement_card_number || !form.settlement_bank || !form.settlement_open_subbank || !form.settlement_bank_tel) {
        uni.showToast({ title: '请填写完整的账户信息', icon: 'none' });
        return;
      }
    }
  } else if (selectedName.value === '对私非法人账户') {
    // 对私非法人账户
    if (!authorizationImageUrl.value || !idCardFrontUrl.value || !idCardBackUrl.value || !handheldIdCardUrl.value) {
      uni.showToast({ title: '请上传所有必需的授权书和身份证图片', icon: 'none' });
      return;
    }
    if (!picDataFront.value.name || !picDataFront.value.number || !picDataBack.value.valid_from || !picDataBack.value.valid_to) {
      uni.showToast({ title: '请填写完整的结算人信息', icon: 'none' });
      return;
    }
	// 校验身份证号逻辑
	const isValid = verifyCardId(picDataFront.value.number)
	if(!isValid) return uni.utils.toast('请输入正确的身份证号')
    if (!validateDate(picDataBack.value.valid_to) || new Date(picDataBack.value.valid_to) <= new Date()) {
      uni.showToast({
        title: '结算人身份证有效期格式不正确，请按照xxxx-xx-xx或xxxx年xx月xx日格式填写，并且有效期不能小于当前日期',
        icon: 'none'
      });
      return;
    }
  } else if (selectedName.value === '对公账户') {
    // 对公账户
    if (!merchantImgPermitImageUrl.value) {
      uni.showToast({ title: '请上传必需的开户许可证图片', icon: 'none' });
      return;
    }
    if (!form.settlement_account_name || !form.settlement_card_number || !form.settlement_bank || !form.settlement_open_subbank || !form.settlement_bank_tel) {
      uni.showToast({ title: '请填写完整的账户信息', icon: 'none' });
      return;
    }
  }

  const data = {
    merchant_id: uni.getStorageSync('merchant_id') || '',
    settlement_bank: form.settlement_bank,
    settlement_open_subbank: form.settlement_open_subbank,
    settlement_account_name: form.settlement_account_name,
    settlement_bank_code: form.settlement_bank_code,
    settlement_card_number: form.settlement_card_number,
    settlement_bank_tel: form.settlement_bank_tel,
	settlement_account_type: selectedName.value === '对私法人账户' ? '01' : selectedName.value === '对私非法人账户' ? '02' : '00',
	merchant_img_authorization_statement: selectedName.value === '对私非法人账户' ? authorizationImageUrl.value : '',
	merchant_img_permit_for_bank_account: selectedName.value === '对公账户' ? merchantImgPermitImageUrl.value : '',
    merchant_img_bank_card: frontImageUrl.value,
    merchant_img_bank_card_back: backImageUrl.value || '',
    merchant_img_principal_id_card_front: idCardFrontUrl.value,
    merchant_img_principal_id_card_obverse: idCardBackUrl.value,
    temp_save: 0,
    merchant_img_holding_photo: handheldIdCardUrl.value, // 添加手持身份证图片URL
    s_legal_name: picDataFront.value.name, // 结算人姓名
    s_legal_id: picDataFront.value.number, // 结算人身份证号
    s_legal_start_dt: picDataBack.value.valid_from, // 结算人身份证生效日期
    s_legal_exp_dt: picDataBack.value.valid_to, // 结算人身份证有效期
    longitude: uni.getStorageSync('longitude') || '', // 获取经度，如果没有则为空
    latitude: uni.getStorageSync('latitude') || '' // 获取纬度，如果没有则为空
  };

  await submitDebitCard(data).then((res) => {
    if (res.code === 200) {
      uni.showToast({ title: res.msg, icon: 'none' });
      uni.reLaunch({
        url: '/pages/accomplish/accomplish'
      });
    } else {
      uni.showToast({ title: res.msg, icon: 'none' });
    }
  });
};
</script>

<style lang="scss" scoped>
.longPress {
  ::v-deep .u-popup__content {
    width: 100%;
    height: 440rpx;
    box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
    border-radius: 24rpx;
  }
}
::v-deep .u-popup__content {
  width: 582rpx;
  height: 440rpx;
  box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
  border-radius: 24rpx;
}

::v-deep .uni-input-input {
  // margin-left: 64rpx;
}
::v-deep .uni-input-placeholder {
  // margin-left: 64rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #9da5ad !important;
}

.content {
  padding-top: 1rpx;
  width: 100%;
  height: calc(100% - 500rpx);

  .schemeSelection {
    padding: 0 32rpx;
    margin-top: 56rpx;

    .title {
      font-weight: 500;
      font-size: 34rpx;
      color: #18191a;
      margin-bottom: 8rpx;
      .required {
        color: red;
      }
    }
    .elect {
      width: 686rpx;
      height: 176rpx;
      border-radius: 8rpx;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: center;
      .list {
        width: 336rpx;
        height: 80rpx;
        background: #ffffff;
        border-radius: 8rpx;
        margin-top: 16rpx;
        text-align: center;
        line-height: 80rpx;
        position: relative;
        .text {
          font-weight: 500;
          font-size: 28rpx;
          color: #575e65;
        }
        &.active {
          background: #e5f2ff;
          border: 1rpx solid #1386fb;
          .text {
            color: #1386fb;
          }
          .icon {
            display: block;
          }
        }
        .icon {
          position: absolute;
          right: 8rpx;
          bottom: 8rpx;
          width: 48rpx;
          height: 48rpx;
          display: none;
        }
      }
    }
    .hint {
      font-size: 24rpx;
      color: #9da5ad;
    }
  }
  .uploading {
    width: 686rpx;
    height: 528rpx;
    background-color: #ffffff;
    margin-left: 32rpx;
    margin-top: 32rpx;
    box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
    border: 1rpx solid #edf0f5;
    border-radius: 24rpx;
    .uploadingMessage {
      padding: 0 32rpx;
      margin-top: 40rpx;
      .title {
        font-weight: 500;
        font-size: 30rpx;
        color: #18191a;
      }
      .text {
        font-weight: 400;
        font-size: 24rpx;
        color: #9da5ad;
        margin-top: 8rpx;
      }
    }
    .uploadingImages {
      display: flex;
      justify-content: space-between;
      margin-top: 24rpx;
      padding: 0 32rpx;
      .uploadBox {
        width: 304rpx;
        .charter {
          width: 100%;
          height: 256rpx;
          background-color: #f9fafb;
        }
        .btnBox {
          width: 100%;
          height: 64rpx;
          background-color: #c1dffe;
          border-radius: 0rpx 0rpx 8rpx 8rpx;
          &.uploaded {
            background-color: #aadb9a; /* 背景颜色 */
            .btn {
              color: #198703; /* 文字颜色改变 */
            }
          }
          &.failed {
            background-color: #e6b9b5;
            .btn {
              color: #b91810;
            }
          }
          .btn {
            line-height: 64rpx;
            text-align: center;
            font-size: 26rpx;
            color: #0056ad;
          }
        }
      }
    }
  }
  .require {
    margin-top: 56rpx;
    padding: 0 32rpx;
    .title {
      font-weight: 500;
      font-size: 30rpx;
      color: #18191a;
    }
    .imgList {
      display: flex;
      justify-content: space-between;
      margin-top: 16rpx;
    }
    .text {
      display: flex;
      justify-content: center;
      .word {
        font-size: 24rpx;
        font-weight: 400;
        color: #575e65;
      }
    }
  }
  .messageBox {
    // height: 556rpx;
    background: #fff;
    box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
    border-radius: 8rpx;
    border: 1rpx solid #edf0f5;
    margin-bottom: 64rpx;
    margin: 0 32rpx;
    margin-top: 56rpx;
    .message {
      padding: 40rpx 32rpx;
    }
    .form {
      margin-top: 16rpx;
      .text {
        font-weight: 400;
        font-size: 28rpx;
        color: #575e65;
        .required {
          color: red;
        }
      }
    }
  }
  .binding {
    width: 686rpx;
    height: 180rpx;
    padding: 0 32rpx;
    margin-top: 56rpx;
    .title {
      font-weight: 500;
      font-size: 34rpx;
      color: #18191a;
    }
    .bindingBtn {
      margin-top: 24rpx;
      height: 108rpx;
      background: #ffffff;
      box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
      border-radius: 16rpx;
      border: 1rpx solid #edf0f5;
      display: flex;
      justify-content: space-between;
      line-height: 108rpx;
      padding-left: 32rpx;
      padding-right: 32rpx;
      .text {
        font-weight: 400;
        font-size: 30rpx;
        color: #575e65;
      }
      .btn {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 24rpx;
        color: #1386fb;
      }
    }
  }
  .nextBox.active {
    background: #1386fb;
  }

  .nextBox.active .text {
    color: #ffffff;
  }
}
.bottomBox {
  position: fixed;
  box-shadow: 0rpx -2rpx 4rpx 0rpx rgba(75, 80, 85, 0.06);
  background: #ffffff;
  margin-top: 58rpx;
  width: 100%;
  height: 172rpx;
  bottom: 0;
  left: 0;
  .box {
    padding: 0 32rpx;
    display: flex;
    .backBox {
      width: 218rpx;
      height: 88rpx;
      border: 2rpx solid #1b7de1;
      border-radius: 8rpx;
      margin-top: 24rpx;
      margin-right: 16rpx;
      .text {
        line-height: 88rpx;
        text-align: center;
        color: #1386fb;
        font-weight: 600;
        font-size: 26rpx;
      }
    }
    .nextBox {
      width: 100%;
      height: 94rpx;
      background: #dee2e8;
      border-radius: 8rpx;
      margin-top: 24rpx;
      &.active {
        background: #1386fb;
        .text {
          color: #ffffff;
        }
      }
      .text {
        line-height: 94rpx;
        text-align: center;
        color: #9da5ad;
        font-size: 26rpx;
        font-weight: 500;
      }
    }
  }
}
</style>
