<template>
	<view class="">
		<!-- 导航栏 -->
		<navbar title="开通支付" backGroundColor="#fff" />
		<!-- 步骤条 -->
		<StepBar :currentStep="1" />
	</view>
	<view class="content">
		<view class="changsha_bank" v-if="currentGangway == 'changsha_bank'">
			<view class="text">补充填写商家信息(必填)</view>
			<view class="text1">长沙银行商家必填信息</view>
			<view class="active" style="margin-top: 40rpx">
				<view class="text">
					经营状态
					<span class="required">*</span>
				</view>
				<view style="display: flex; flex-direction: column">
					<view style="display: flex">
						<view class="text2"
							:style="{ backgroundColor: formData.mchtManageStatus == 3 ? '#1386FB' : '#f0f2f5', color: formData.mchtManageStatus == 3 ? '#FFFFFF' : '#575e65' }"
							@click="selectOption('mchtManageStatus', 3)">
							<text class="txt">良好</text>
						</view>
						<view class="text2" style="margin-left: 16rpx"
							:style="{ backgroundColor: formData.mchtManageStatus == 2 ? '#1386FB' : '#f0f2f5', color: formData.mchtManageStatus == 2 ? '#FFFFFF' : '#575e65' }"
							@click="selectOption('mchtManageStatus', 2)">
							<text class="txt">一般</text>
						</view>
					</view>
					<view style="display: flex; margin-top: 16rpx">
						<view class="text2" style="width: 230rpx"
							:style="{ backgroundColor: formData.mchtManageStatus == 1 ? '#1386FB' : '#f0f2f5', color: formData.mchtManageStatus == 1 ? '#FFFFFF' : '#575e65' }"
							@click="selectOption('mchtManageStatus', 1)">
							<text class="txt">差 但会有改善</text>
						</view>
						<view class="text2" style="margin-left: 16rpx; width: 178rpx"
							:style="{ backgroundColor: formData.mchtManageStatus == 0 ? '#1386FB' : '#f0f2f5', color: formData.mchtManageStatus == 0 ? '#FFFFFF' : '#575e65' }"
							@click="selectOption('mchtManageStatus', 0)">
							<text class="txt">差 风险高</text>
						</view>
					</view>
				</view>
			</view>
			<up-line color="#D8D8D8"></up-line>
			<view class="active" style="margin-top: 24rpx">
				<view class="text">
					经营区域
					<span class="required">*</span>
				</view>
				<view style="display: flex; flex-direction: column">
					<view style="display: flex">
						<view class="text2" style="width: 220rpx"
							:style="{ backgroundColor: formData.manageArea == 3 ? '#1386FB' : '#f0f2f5', color: formData.manageArea == 3 ? '#FFFFFF' : '#575e65' }"
							@click="selectOption('manageArea', 3)">
							<text class="txt">商圈和商业街</text>
						</view>
					</view>
					<view style="display: flex; margin-top: 16rpx">
						<view class="text2" style="width: 298rpx"
							:style="{ backgroundColor: formData.manageArea == 2 ? '#1386FB' : '#f0f2f5', color: formData.manageArea == 2 ? '#FFFFFF' : '#575e65' }"
							@click="selectOption('manageArea', 2)">
							<text class="txt">临街店面、办公楼内</text>
						</view>
						<view class="text2" style="margin-left: 16rpx"
							:style="{ backgroundColor: formData.manageArea == 1 ? '#1386FB' : '#f0f2f5', color: formData.manageArea == 1 ? '#FFFFFF' : '#575e65' }"
							@click="selectOption('manageArea', 1)">
							<text class="txt">其他</text>
						</view>
					</view>
				</view>
			</view>
			<up-line color="#D8D8D8"></up-line>
			<view class="active" style="margin-top: 24rpx">
				<view class="text">
					经营面积
					<span class="required">*</span>
				</view>
				<view style="display: flex; flex-direction: column">
					<view style="display: flex">
						<view class="text2" style="width: 148rpx"
							:style="{ backgroundColor: formData.manageAcreage == 0 ? '#1386FB' : '#f0f2f5', color: formData.manageAcreage === 0 ? '#FFFFFF' : '#575e65' }"
							@click="selectOption('manageAcreage', 0)">
							<text class="txt">0-20㎡</text>
						</view>
						<view class="text2" style="margin-left: 16rpx; width: 164rpx"
							:style="{ backgroundColor: formData.manageAcreage == 1 ? '#1386FB' : '#f0f2f5', color: formData.manageAcreage == 1 ? '#FFFFFF' : '#575e65' }"
							@click="selectOption('manageAcreage', 1)">
							<text class="txt">20-50㎡</text>
						</view>
					</view>
					<view style="display: flex; margin-top: 16rpx">
						<view class="text2" style="width: 176rpx"
							:style="{ backgroundColor: formData.manageAcreage == 2 ? '#1386FB' : '#f0f2f5', color: formData.manageAcreage == 2 ? '#FFFFFF' : '#575e65' }"
							@click="selectOption('manageAcreage', 2)">
							<text class="txt">50㎡以上</text>
						</view>
					</view>
				</view>
			</view>
			<up-line color="#D8D8D8"></up-line>
			<view style="height: 32rpx"></view>
		</view>
		<view class="schemeSelection">
			<view class="title">上传营业执照</view>
			<p class="hint">请上传真实有效信息，以下信息将用于各类申请</p>
		</view>
		<view class="uploading" @longpress="url && openPopup('license')">
			<image v-if="url" class="charter" :src="baseURL + url" mode="aspectFit"></image>
			<image v-else class="charter" src="@/static/images/yingye.png" mode="aspectFit" @click="uploadImage"></image>
			<view class="btnBox" :class="{ uploaded: isUploaded, failed: uploadFailed }" @click="uploadImage">
				<view class="btn" v-if="isUploaded">上传成功</view>
				<view class="btn" v-else-if="uploadFailed">无法识别,请重新上传</view>
				<view class="btn" v-else>上传营业执照</view>
			</view>
		</view>
		<view class="checkbox">
			<up-checkbox :customStyle="{ marginBottom: '8px' }" shape="circle" label="没有营业执照" name="agree"
				:checked="aloneChecked" labelColor="#9DA5AD" labelSize="24rpx" size="16" activeColor="#f39237"
				@change="toggleAlert" :usedAlone="true"></up-checkbox>
			<up-alert v-if="showWarningAlert" :showIcon="true" :title="title" type="warning"
				:description="description"></up-alert>
		</view>
		<view class="require">
			<view class="title">拍摄要求</view>
			<view class="imgList">
				<view class="img1">
					<image src="@/static/images/biaozhun.png" mode="widthFix" style="width: 160rpx"></image>
					<view class="text">
						<image src="@/static/images/duihao.png" mode="widthFix" style="width: 32rpx"></image>
						<view class="word">标准</view>
					</view>
				</view>
				<view class="img2">
					<image src="@/static/images/queshi.png" mode="widthFix" style="width: 160rpx"></image>
					<view class="text">
						<image src="@/static/images/cuowu.png" mode="widthFix" style="width: 32rpx"></image>
						<view class="word">边角缺失</view>
					</view>
				</view>
				<view class="img3">
					<image src="@/static/images/mohu.png" mode="widthFix" style="width: 160rpx"></image>
					<view class="text">
						<image src="@/static/images/cuowu.png" mode="widthFix" style="width: 32rpx"></image>
						<view class="word">照片模糊</view>
					</view>
				</view>
				<view class="img4">
					<image src="@/static/images/fanguang.png" mode="widthFix" style="width: 160rpx"></image>
					<view class="text">
						<image src="@/static/images/cuowu.png" mode="widthFix" style="width: 32rpx"></image>
						<view class="word">反光强烈</view>
					</view>
				</view>
			</view>
		</view>
		<view class="messageBox">
			<view class="message">
				<view class="title">营业执照基本信息</view>
				<view class="form">
					<up-form labelPosition="left" :model="model1" :rules="rules" ref="form1" errorType="toast">
						<up-form-item prop="name" borderBottom ref="item1">
							<view class="text" style="width: 38%">
								营业执照名
								<span class="required">*</span>
							</view>
							<up-input border="none" placeholder="上传后自动识别或手动输入" v-model="picData.name" :disabled="aloneChecked" />
						</up-form-item>
						<up-form-item prop="registration_number" borderBottom ref="item1">
							<view class="text" style="width: 38%">
								营业执照号
								<span class="required">*</span>
							</view>
							<up-input border="none" placeholder="上传后自动识别或手动输入" v-model="picData.registration_number"
								:disabled="aloneChecked" />
						</up-form-item>
						<up-form-item prop="address" borderBottom ref="item1">
							<view class="text" style="width: 38%">
								营业执照地址
								<span class="required">*</span>
							</view>
							<up-input border="none" placeholder="上传后自动识别或手动输入" v-model="picData.address" :disabled="aloneChecked" />
						</up-form-item>
						<up-form-item prop="found_date" borderBottom ref="item1">
							<view class="text" style="width: 38%">
								开始日期
								<span class="required">*</span>
							</view>
							<up-input border="none" placeholder="上传后自动识别或手动输入" v-model="picData.found_date"
								:disabled="aloneChecked" />
						</up-form-item>
						<up-form-item prop="business_term" borderBottom ref="item1">
							<view class="text" style="width: 38%">
								有效期至
								<span class="required">*</span>
							</view>
							<up-input border="none" placeholder="上传后自动识别或手动输入" v-model="picData.business_term"
								:disabled="aloneChecked" />
						</up-form-item>
					</up-form>
				</view>
			</view>
		</view>
		<view style="height: 288rpx"></view>
		<view class="bottomBox">
			<view class="box">
				<view class="backBox" @click="back">
					<view class="text">上一步</view>
				</view>
				<view class="nextBox" :class="{ active: isFormValid }" @click="next">
					<view class="text">下一步</view>
				</view>
			</view>
		</view>
		<!-- 图片长按弹窗组件 -->
		<PoperImg :show="isPopupVisible" @close="closePopup" @delete="deleteImage" @reupload="reuploadImage" />
	</view>
</template>

<script setup>
	import {
		ref,
		reactive,
		inject,
		onMounted,
		onBeforeUnmount,
		computed
	} from 'vue';
	import navbar from '@/components/navbar/navbar.vue';
	import StepBar from '@/components/steps.vue';
	import PoperImg from '@/components/PoperImg.vue';
	import useUserStore from '@/store/user';
	import {
		getMerchantApi
	} from '@/api/merchant';
	import {
		saveMerchantBusinessLicenseApi
	} from '@/api/identityCard';

	/** 获取位置信息 */
	function otgetLocation() {
		uni.getLocation({
			type: 'gcj02', // 返回可以用于uni.openLocation的经纬度
			success: function(res) {
				/** 把位置信息存储到本地 */
				uni.setStorageSync('longitude', res.longitude);
				uni.setStorageSync('latitude', res.latitude);
			},
			fail: function(err) {
				console.log('获取位置信息失败：' + JSON.stringify(err));
			}
		});
	}

	const baseURL = inject('baseURL');
	const userStore = useUserStore();
	const aloneChecked = ref(false);
	const showAlert = ref(false);
	const showWarningAlert = ref(false);
	const title = ref('温馨提示');
	const description = ref('为了获取更多支付功能与支付额度，请提交您的营业执照');
	const url = ref('');
	const isUploaded = ref(false);
	const uploadFailed = ref(false);
	const isPopupVisible = ref(false);
	const currentImageType = ref('');
	const picData = reactive({
		name: '',
		registration_number: '',
		address: '',
		found_date: '',
		business_term: ''
	});
	const merchant_id = ref('');
	const formData = reactive({
		mchtManageStatus: null, // 商户经营状态
		manageArea: null, // 商户经营区域
		manageAcreage: null // 商户经营面积
	});
	const currentGangway = ref(''); // 长沙银行通道字段

	const isFormValid = computed(() => {
		// 仅当当前通道为长沙银行时，才校验经营状态、经营区域和经营面积
		const isChangshaBank = currentGangway.value === 'changsha_bank';
		const isBusinessInfoValid = isChangshaBank ? formData.mchtManageStatus !== null && formData.manageArea !==
			null && formData.manageAcreage !== null : true; // 非长沙银行时跳过这些校验

		// 如果长沙银行通道的经营信息无效，直接返回 false
		if (!isBusinessInfoValid) return false;

		// 检查表单的其他字段有效性
		return aloneChecked.value || (picData.name && picData.registration_number && picData.address && validateDate(
			picData.found_date) && validateDate(picData.business_term));
	});
	const validateDate = (date) => {
		const regex = /^(\d{4}-\d{1,2}-\d{1,2}|\d{4}年\d{1,2}月\d{1,2}日|长期)$/;
		if (!regex.test(date)) return false;

		if (date === '长期') return true;

		if (date.includes('年')) {
			date = date.replace(/(\d{4})年(\d{1,2})月(\d{1,2})日/, '$1-$2-$3');
		}

		const [year, month, day] = date.split('-').map(Number);
		const dateObj = new Date(year, month - 1, day);
		return dateObj.getFullYear() === year && dateObj.getMonth() + 1 === month && dateObj.getDate() === day;
	};

	const selectOption = (category, value) => {
		if (category === 'mchtManageStatus') {
			formData.mchtManageStatus = value;
		} else if (category === 'manageArea') {
			formData.manageArea = value;
		} else if (category === 'manageAcreage') {
			formData.manageAcreage = value;
		}
	};

	const fetchMerchantData = async (merchantId) => {
		const response = await getMerchantApi(merchantId);
		if (response.code === 200) {
			const data = response.data;
			// 更新图片及上传状态
			url.value = data.merchant_img_electronic_business_license;
			isUploaded.value = !!url.value;
			uploadFailed.value = false;
			// 更新是否有营业执照的选择状态
			if (data.is_business_license == 0) {
				aloneChecked.value = false;
				showWarningAlert.value = false;
			} else {
				aloneChecked.value = true;
				showAlert.value = true;
				showWarningAlert.value = true;
			}
			// 更新营业执照的基本信息
			picData.name = data.bse_lice_nm;
			picData.registration_number = data.bus_lic_no;
			picData.address = data.bus_lic_address;
			picData.found_date = data.bus_start_dt;
			picData.business_term = data.bus_exp_dt;
			// 更新用户之前的选择
			formData.mchtManageStatus = data.mchtManageStatus; // 如果有值，设置为对应选项；否则为null
			formData.manageArea = data.manageArea;
			formData.manageAcreage = data.manageAcreage;
			currentGangway.value = data.current_gangway;
		}
	};

	onBeforeUnmount(() => {
		console.log('用户即将离开当前页面');
		handleBeforeUnload();
	});

	onMounted(async () => {
		otgetLocation();
		merchant_id.value = uni.getStorageSync('merchant_id');
		if (merchant_id.value) {
			await fetchMerchantData(merchant_id.value);
		}
	});

	const openPopup = (type) => {
		currentImageType.value = type;
		isPopupVisible.value = true;
	};

	const handleBeforeUnload = async () => {
		const data = {
			id: merchant_id.value,
			is_business_license: aloneChecked.value ? 1 : 0,
			merchant_img_electronic_business_license: url.value,
			name: picData.name,
			registration_number: picData.registration_number,
			address: picData.address,
			found_date: picData.found_date,
			temp_save: 1,
			business_term: picData.business_term,
			mchtManageStatus: formData.mchtManageStatus,
			manageArea: formData.manageArea,
			manageAcreage: formData.manageAcreage
		};
		await saveMerchantBusinessLicenseApi(data);
	};

	const closePopup = () => {
		isPopupVisible.value = false;
		currentImageType.value = '';
	};

	const deleteImage = () => {
		if (currentImageType.value === 'license') {
			url.value = '';
			isUploaded.value = false;
			uploadFailed.value = false;
		}
		closePopup();
	};

	// 重新上传图片
	const reuploadImage = () => {
		if (currentImageType.value === 'license') {
			url.value = ''; // 清空图片地址
			isUploaded.value = false; // 重置上传成功状态
			uploadFailed.value = false; // 重置上传失败状态
			picData.name = ''; // 清空营业执照名
			picData.registration_number = ''; // 清空营业执照号
			picData.address = ''; // 清空营业执照地址
			picData.found_date = ''; // 清空营业执照开始日期
			picData.business_term = ''; // 清空营业执照有效期至
			uploadImage(); // 重新上传图片
		}
		closePopup();
	};

	const toggleAlert = (value) => {
		aloneChecked.value = value;
		if (aloneChecked.value) {
			showAlert.value = true;
			showWarningAlert.value = true;
			picData.name = '';
			picData.registration_number = '';
			picData.address = '';
			picData.found_date = '';
			picData.business_term = '';
			url.value = '';
			isUploaded.value = false;
			uploadFailed.value = false;
		} else {
			showAlert.value = isUploaded.value;
			showWarningAlert.value = false;
		}
	};

	const uploadImage = async () => {
		uni.chooseImage({
			sourceType: ['album', 'camera'],
			count: 1,
			success: (chooseImageRes) => {
				const tempFilePaths = chooseImageRes.tempFilePaths;
				uni.uploadFile({
					url: baseURL + 'platform_api/tenant_salesman.Incoming/uploadMerchantImg',
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						type: 'licensePic',
						id: merchant_id.value
					},
					header: {
						token: userStore.token
					},
					success: (res) => {
						const data = JSON.parse(res.data);
						if (data.code !== 200) {
							uni.utils.toast(data.msg);
							uploadFailed.value = true;
							return;
						}
						url.value = data.path;
						picData.name = data.data.name;
						picData.registration_number = data.data.registration_number;
						picData.address = data.data.address;
						picData.found_date = data.data.found_date;
						picData.business_term = data.data.business_term;
						showAlert.value = true;
						showWarningAlert.value = false;
						aloneChecked.value = false;
						isUploaded.value = true;
						uploadFailed.value = false;
					}
				});
			}
		});
	};

	// 下一步按钮
	const next = async () => {
		if (!aloneChecked.value && !isUploaded.value) {
			uni.showToast({
				title: '请上传营业执照或选择没有营业执照',
				icon: 'none'
			});
			return;
		}

		if (!aloneChecked.value) {
			if (!picData.name) {
				uni.showToast({
					title: '请填写营业执照名',
					icon: 'none'
				});
				return;
			}
			if (!picData.registration_number) {
				uni.showToast({
					title: '请填写营业执照号',
					icon: 'none'
				});
				return;
			}
			if (!picData.address) {
				uni.showToast({
					title: '请填写营业执照地址',
					icon: 'none'
				});
				return;
			}
			if (!validateDate(picData.found_date)) {
				uni.showToast({
					title: '请填写正确的开始日期格式，如xxxx-xx-xx或xxxx年xx月xx日',
					icon: 'none'
				});
				return;
			}
			if (!validateDate(picData.business_term)) {
				uni.showToast({
					title: '请填写正确的有效期至格式，如xxxx-xx-xx、xxxx年xx月xx日或长期',
					icon: 'none'
				});
				return;
			}
			if (picData.business_term !== '长期' && new Date(picData.found_date) > new Date(picData.business_term)) {
				uni.showToast({
					title: '有效期至日期应晚于开始日期',
					icon: 'none'
				});
				return;
			}
			if (picData.business_term !== '长期' && new Date(picData.business_term) < new Date()) {
				uni.showToast({
					title: '有效期至不能低于当前日期',
					icon: 'none'
				});
				return;
			}
		}

		// 仅当为长沙银行通道时，校验经营状态、经营区域和经营面积
		if (currentGangway.value === 'changsha_bank') {
			if (formData.mchtManageStatus === null) {
				uni.showToast({
					title: '请选择经营状态',
					icon: 'none'
				});
				return;
			}
			if (formData.manageArea === null) {
				uni.showToast({
					title: '请选择经营区域',
					icon: 'none'
				});
				return;
			}
			if (formData.manageAcreage === null) {
				uni.showToast({
					title: '请选择经营面积',
					icon: 'none'
				});
				return;
			}
		}

		const data = {
			id: merchant_id.value,
			is_business_license: aloneChecked.value ? 1 : 0,
			merchant_img_electronic_business_license: url.value,
			name: picData.name,
			registration_number: picData.registration_number,
			address: picData.address,
			found_date: picData.found_date,
			temp_save: 0,
			business_term: picData.business_term,
			longitude: uni.getStorageSync('longitude') || '',
			latitude: uni.getStorageSync('latitude') || '',
			mchtManageStatus: formData.mchtManageStatus,
			manageArea: formData.manageArea,
			manageAcreage: formData.manageAcreage
		};

		const res = await saveMerchantBusinessLicenseApi(data);
		if (res.code === 200) {
			// 将 save_page 存储为 2
			uni.setStorageSync('save_page', 2);
			uni.showToast({
				title: res.msg,
				icon: 'none'
			});
			uni.redirectTo({
				url: '/pages/corporateIdentityCard/corporateIdentityCard'
			});
		} else {
			uni.showToast({
				title: res.msg,
				icon: 'none'
			});
		}
	};

	const back = async () => {
		const data = {
			id: merchant_id.value,
			is_business_license: aloneChecked.value ? 1 : 0,
			merchant_img_electronic_business_license: url.value,
			name: picData.name,
			registration_number: picData.registration_number,
			address: picData.address,
			found_date: picData.found_date,
			temp_save: 1,
			business_term: picData.business_term,
			longitude: uni.getStorageSync('longitude') || '', // 获取经度，如果没有则为空
			latitude: uni.getStorageSync('latitude') || '', // 获取纬度，如果没有则为空
			mchtManageStatus: formData.mchtManageStatus, // 经营状态
			manageArea: formData.manageArea, // 经营区域
			manageAcreage: formData.manageAcreage // 经营面积
		};
		await saveMerchantBusinessLicenseApi(data);

		uni.redirectTo({
			url: '/pages/solutionSelection/solutionSelection'
		});
	};
</script>

<style lang="scss" scoped>
	::v-deep .u-alert__content__desc {
		color: #f39237;
		font-size: 24rpx;
		font-weight: 400;
	}

	::v-deep .u-alert__content__title {
		font-size: 28rpx;
		color: #f39237;
	}

	::v-deep .u-alert {
		border-radius: 8rpx;
	}

	::v-deep .uni-input-input {
		// margin-left: 64rpx;
	}

	::v-deep .uni-input-placeholder {
		// margin-left: 64rpx;
		font-size: 28rpx;
		font-weight: 400;
		color: #9da5ad;
	}

	::v-deep .u-form-item__body {
		border-bottom: 1rpx solid #ededed;
	}

	.content {
		width: 100%;
		height: 100%;
		padding-bottom: 280rpx;
		margin-bottom: 280rpx;
		padding-top: 164rpx;

		.changsha_bank {
			width: 686rpx;
			min-height: 200rpx;
			background: #ffffff;
			box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
			border-radius: 24rpx;
			border: 1rpx solid #edf0f5;
			margin-left: 32rpx;
			margin-top: 56rpx;
			box-sizing: border-box;
			padding: 0 32rpx;

			.required {
				color: red;
			}

			.text {
				font-weight: 600;
				font-size: 40rpx;
				color: #18191a;
				line-height: 56rpx;
				padding-top: 40rpx;
			}

			.text1 {
				font-weight: 400;
				font-size: 24rpx;
				color: #9da5ad;
				line-height: 40rpx;
				margin-top: 8rpx;
			}

			.active {
				width: 622rpx;
				height: 169rpx;
				display: flex;

				.text {
					font-weight: 400;
					font-size: 28rpx;
					color: #575e65;
					line-height: 44rpx;
				}
			}

			.text2 {
				width: 116rpx;
				height: 64rpx;
				border-radius: 8rpx;
				text-align: center;
				margin-left: 48rpx;

				.txt {
					font-weight: 400;
					font-size: 26rpx;
					line-height: 64rpx;
				}
			}
		}

		.steps {
			width: 100%;
			height: 164rpx;
			background-color: #ffffff;
		}

		.schemeSelection {
			padding: 0 32rpx;
			margin-top: 56rpx;

			.title {
				font-size: 44rpx;
				margin-bottom: 8rpx;
			}

			.hint {
				font-size: 24rpx;
				color: #9da5ad;
			}
		}

		.uploading {
			width: 686rpx;
			height: 512rpx;
			background-color: #ffffff;
			margin-left: 32rpx;
			margin-top: 32rpx;
			box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
			border: 1rpx solid #edf0f5;
			border-radius: 24rpx;

			.charter {
				width: 410rpx;
				height: 308rpx;
				margin: 0 138rpx;
				margin-top: 66rpx;
				background-color: #f9fafb;
			}

			.btnBox {
				width: 410rpx;
				height: 64rpx;
				background-color: #c1dffe;
				border-radius: 0rpx 0rpx 8rpx 8rpx;
				margin: 0 138rpx;

				&.uploaded {
					background-color: #aadb9a;

					/* 背景颜色 */
					.btn {
						color: #198703;
						/* 文字颜色改变 */
					}
				}

				&.failed {
					background-color: #e6b9b5;

					.btn {
						color: #b91810;
					}
				}

				.btn {
					line-height: 64rpx;
					text-align: center;
					font-size: 26rpx;
					color: #0056ad;
				}
			}
		}

		.checkbox {
			padding: 0 32rpx;
			margin-top: 26rpx;
		}

		.require {
			margin-top: 56rpx;
			padding: 0 32rpx;

			.title {
				font-weight: 500;
				font-size: 30rpx;
				color: #18191a;
			}

			.imgList {
				display: flex;
				justify-content: space-between;
				margin-top: 16rpx;
			}

			.text {
				display: flex;
				justify-content: center;

				.word {
					font-size: 24rpx;
					font-weight: 400;
					color: #575e65;
				}
			}
		}

		.messageBox {
			height: 588rpx;
			background: #fff;
			box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
			border-radius: 8rpx;
			border: 1rpx solid #edf0f5;
			margin-bottom: 64rpx;
			margin: 0 32rpx;
			margin-top: 56rpx;

			.message {
				padding: 40rpx 32rpx;
			}

			.form {
				margin-top: 16rpx;

				.text {
					font-weight: 400;
					font-size: 28rpx;
					color: #575e65;

					.required {
						color: red;
					}
				}
			}
		}

		.bottomBox {
			position: fixed;
			box-shadow: 0rpx -2rpx 4rpx 0rpx rgba(75, 80, 85, 0.06);
			background: #ffffff;
			margin-top: 58rpx;
			width: 100%;
			height: 172rpx;
			bottom: 0;
			left: 0;
			padding: 0 32rpx;

			.box {
				display: flex;

				.backBox {
					width: 218rpx;
					height: 88rpx;
					border: 2rpx solid #1b7de1;
					border-radius: 8rpx;
					margin-top: 24rpx;
					margin-right: 16rpx;

					.text {
						line-height: 88rpx;
						text-align: center;
						color: #1386fb;
						font-weight: 600;
						font-size: 26rpx;
					}
				}

				.nextBox {
					width: 452rpx;
					height: 94rpx;
					background: #dee2e8;
					border-radius: 8rpx;
					margin-top: 24rpx;

					&.active {
						background: #1386fb;

						.text {
							color: #ffffff;
						}
					}

					.text {
						line-height: 94rpx;
						text-align: center;
						color: #9da5ad;
						font-size: 26rpx;
						font-weight: 500;
					}
				}
			}
		}

		.nextBox.active {
			background: #1386fb;
		}

		.nextBox.active .text {
			color: #ffffff;
		}
	}
</style>