<template>
	<view class="page-merchant-container">
		<!-- 背景区域 -->
		<view class="page-merchant-box-bg"></view>

		<!-- 导航栏 -->
		<navbar title="商家详情" :backGroundColor="navBgColor" />

		<view class="page-merchant-box-paddding">
			<!-- 基本信息 -->
			<view class="page-merchant-box">
				<view class="basic-infor-box">
					<view class="merchant-txt1">{{merchantDetail.merchant_name}}</view>
					<view class="merchant-txt2 margin-sty1">{{merchantDetail.merchant_type}}</view>
					<view class="flex-sty2 margin-sty1">
						<view class="merchant-txt3">{{merchantDetail.s_legal_name}}</view>
						<view class="merchant-txt3 merchant-txt4">
							{{merchantDetail.merchant_phone && merchantDetail.merchant_phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')}}
						</view>
					</view>
					<view class="merchant-txt5 margin-sty">{{merchantDetail.merchant_address}}</view>
					<view class="merchant-txt5 margin-sty">入网时间: {{merchantDetail.merchant_join_time}}</view>
				</view>
			</view>

			<!-- 菜单区域 -->
			<!-- #ifdef APP-PLUS -->
				<up-sticky :offsetTop="stickyHeight">
			<!-- #endif -->
			<!-- #ifdef H5 -->
				<up-sticky>
			<!-- #endif --> 
				<view class="page-merchant-box flex-sty2 menu-list-box" :class="isTop ? 'menu-list-sticky' : 'menu-list' ">
					<view class="menu-list-item" v-for="item in menuList" :key="item.type" @click="menuHandle(item)">
						<view :class="item.type == type ? 'menu-list-txt1' : ''">{{item.name}}</view>
						<view class="menu-list-line" :class="item.type == type ? 'menu-list-line1' : ''"></view>
					</view>
				</view>
			</up-sticky>

			<!-- 交易记录 -->
			<view class="page-merchant-box">
				<view class="" v-if="type == 1">
					<!-- 交易记录 -->
					<recordItem v-for="item in traderecordList" :key="item.year" :item="item"/>
				</view>
			</view>
			<!-- 更多信息 -->
			<view class="page-merchant-box">
				<view class="" v-if="type == 2">
					<!-- 结算信息 -->
					<view class="settlement">
						<view class="merchant-txt6">结算信息</view>
						<view class="merchant-line margin-sty1"></view>
						<view class="flex-sty2 margin-sty2">
							<view class="merchant-txt7">活动报名</view>
							<view class="merchant-txt8">{{ merchantDetail.activity_name ? merchantDetail.activity_name : '无活动' }}</view>
						</view>
						<view class="flex-sty2 margin-sty1">
							<view class="merchant-txt7">结算方式</view>
							<view class="merchant-txt8">{{merchantDetail.settlement_period}}</view>
						</view>
						<view class="flex-sty2 margin-sty1">
							<view class="merchant-txt7">结算卡</view>
							<view class="merchant-txt8">{{merchantDetail.settlement_card_number}}</view>
						</view>
						<view class="flex-sty2 margin-sty1">
							<view class="merchant-txt7">开户行</view>
							<view class="merchant-txt8">{{merchantDetail.settlement_bank}}</view>
						</view>
						<view class="flex-sty2 margin-sty1">
							<view class="merchant-txt7">开户支行</view>
							<view class="merchant-txt8">{{merchantDetail.settlement_open_subbank}}</view>
						</view>
						<view class="flex-sty2 margin-sty1">
							<view class="merchant-txt7">账户类型</view>
							<view class="merchant-txt8">{{merchantDetail.settlement_account_type}}</view>
						</view>
						<view class="flex-sty2 margin-sty1">
							<view class="merchant-txt7">费率信息</view>
							<view class="merchant-txt8">{{merchantDetail.merchant_rate}}</view>
						</view>
						<view class="" v-if="merchantDetail.merchant_logo">
							<view class="merchant-txt6 margin-sty3">商家图片</view>
							<image class="merchant-img1 margin-sty1" :src="merchantDetail.merchant_logo" mode=""></image>
						</view>
						<view class="flex-sty2 margin-sty3" v-else>
							<view class="merchant-txt6">商家图片</view>
							<view class="merchant-txt8 merchant-txt10">无</view>
						</view>
					</view>
					<!-- 已绑定设备 -->
					<view class="device-box margin-sty2">
						<view class="flex-sty">
							<view class="merchant-txt6">已绑定设备</view>
							<view class="merchant-txt6 merchant-txt9" @click="bindDevice">绑定设备</view>
						</view>
						<view class="merchant-line margin-sty1"></view>
						<view class="" v-if="merchantDetail.merchant_device && merchantDetail.merchant_device.length > 0">
							<view class="merchant-device-item" v-for="item in merchantDetail.merchant_device" :key="item">
								<view class="merchant-device-txt1">{{item.device_name}}</view>
								<view class="merchant-device-txt2 margin-sty1" v-if="item.device_sn">设备SN号: {{item.device_sn}}</view>
								<view class="merchant-device-txt2 margin-sty" v-if="item.codes">收款码编号: {{item.codes}}</view>
								<view class="merchant-device-txt2 margin-sty">绑定时间: {{item.create_time}}</view>
								<view class="merchant-device-txt3" @click="unBindDeviceHandle(item)">解绑</view>
							</view>
						</view>
						<view class="device-empty" v-else>
							<image class="device-empty-img1" src="@/static/images/empty.png" mode=""></image>
							<view class="device-empty-txt1">商家还没有绑定设备</view>
							<view class="device-empty-txt2" @click="bindDevice">为商家绑定设备</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 绑定设备弹窗 -->
		<devicePopup :show="isShowBindDevice" @okHandel="bindDeviceHandle" @close="closeBindDevice"/>
	</view>
</template>

<script setup>
	import {
		ref,
		computed,
		onMounted,
		inject
	} from 'vue';
	import navbar from '@/components/navbar/navbar.vue';
	import recordItem from '@/pages/business/components/recordItem.vue'
	import devicePopup from '@/pages/business/components/devicePopup.vue'
	import { merchantDetailApi, unBindDeviceApi,delCodeApi,merchantTraderecordApi } from '@/api/business';
	import { bindDeviceApi } from '@/api/index';
	import {
		onLoad,
		onShow,
		onPageScroll
	} from '@dcloudio/uni-app';

	const menuList = ref([{
		name: '交易记录',
		type: 1
	}, {
		name: '更多信息',
		type: 2
	}])
	const type = ref(1) //默认选择交易记录
	const isTop = ref(false) //是否滚动到顶部
	const navBgColor = ref('transparent') //导航栏背景色
	const merchant_id = ref() //商家id
	const merchantDetail = ref({}) //商家详情
	const traderecordList = ref([]) //交易记录
	const isShowBindDevice = ref(false) //绑定设备弹窗
	const stickyHeight = ref(78) // 吸顶的距离

	function menuHandle(item) {
		type.value = item.type
	}
	
	/** 绑定设备按钮 */
	function bindDevice(){
		if(merchantDetail.value.store.length >1){
			jumpTo('/pages/business/selectStore?merchant_id='+ merchantDetail.value.merchant_id + '&store=' + JSON.stringify(merchantDetail.value.store))
		}else{
			isShowBindDevice.value = true
		}
	}
	
	/** 关闭绑定设备弹窗 */
	function closeBindDevice(){
		isShowBindDevice.value = false
	}
	
	/** 商家详情 */
	async function otmerchantDetailApi() {
		const res = await merchantDetailApi(merchant_id.value)
		if (res.code == 200) {
			merchantDetail.value = res.data
		} else {
			uni.utils.toast(res.msg)
		}
	}
	
	/** 商家交易记录 */
	async function otmerchantTraderecordApi(){
		const res = await merchantTraderecordApi(merchant_id.value)
		if (res.code == 200) {
			traderecordList.value = res.data
		} else {
			uni.utils.toast(res.msg)
		}
	}
	
	/** 绑定设备 */
	async function bindDeviceHandle(code) {
	  if (!code) return uni.utils.toast('请填写设备信息');
	  const data = {
	    sn: code,
	    store_id: merchantDetail.value.store[0].id,
	    merchant_id: merchantDetail.value.merchant_id
	  };
	  const res = await bindDeviceApi(data);
	  if (res.code == 200) {
	    uni.utils.toast(res.msg);
	    setTimeout(() => {
	      isShowBindDevice.value = false;
	      otmerchantDetailApi();
	    }, 1000);
	  } else {
	    uni.utils.toast(res.msg);
	  }
	}
	
	/** 解绑设备 */
	async function unBindDeviceHandle(item){
		let res
		if(item.device_sn){
			res = await unBindDeviceApi(item.device_sn,item.device_type)
		}else{
			res = await delCodeApi(item.codes,item.store_id)
		}
		if(res.code == 200){
			uni.utils.toast(res.msg)
			setTimeout(()=>{
				otmerchantDetailApi()
			},1000)
		}else{
			uni.utils.toast(res.msg)
		}
	}
	
	// 页面跳转
	function jumpTo (url) {
		uni.navigateTo({
			url: url
		})
	}
	
	onPageScroll(({scrollTop}) => {
		// 根据滚动位置设置导航栏背景色
		navBgColor.value = scrollTop <= 0 ? 'transparent' : '#fff'
		// 检查是否需要显示菜单栏
		const query = uni.createSelectorQuery();
		query.select('.menu-list-box').boundingClientRect((rect) => {
			/*#ifdef APP-PLUS*/
			if (rect && rect.top <= 80) {
				isTop.value = true
			} else {
				isTop.value = false
			}
			/*#endif*/
			/*#ifdef H5*/
			if (rect && rect.top <= 60) {
				isTop.value = true
			} else {
				isTop.value = false
			}
			/*#endif*/
		}).exec();
	})
	
	onShow(()=>{
		otmerchantDetailApi()
	})
	

	onLoad((options) => {
		merchant_id.value = options.merchant_id
		otmerchantTraderecordApi()
	})

	onMounted(() => {
		uni.getSystemInfo({
		  success(res) {
				// stickyHeight.value = res.statusBarHeight + 44
				// console.log('1414',stickyHeight.value)
		  }
		})
	})
</script>

<style lang="scss" scoped>
	.flex-sty {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.flex-sty1 {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.flex-sty2 {
		display: flex;
		align-items: center;
	}

	.margin-sty {
		margin-top: 8rpx;
	}

	.margin-sty1 {
		margin-top: 16rpx;
	}

	.margin-sty2 {
		margin-top: 24rpx;
	}

	.margin-sty3 {
		margin-top: 48rpx;
	}

	.page-merchant-container {
		width: 100%;

		.page-merchant-box-bg {
			height: 264rpx;
			background: linear-gradient(180deg, #FDD4AD 0%, #F0F2F5 100%);
			z-index: 1;
		}

		.page-merchant-box-paddding {
			box-sizing: border-box;
			padding: 24rpx 0;
		}

		.page-merchant-box {
			box-sizing: border-box;
			padding: 0 32rpx;
		}

		.basic-infor-box {
			margin-top: -264rpx;
			width: 100%;
			background: #FFFFFF;
			box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
			border-radius: 16rpx;
			box-sizing: border-box;
			padding: 32rpx;
		}
		
		.menu-list {
			margin-top: 16rpx;
			width: 100%;
			height: 86rpx;
			background: #F0F2F5;
		}
		
		.menu-list-sticky {
			width: 100%;
			height: 86rpx;
			background: #FFFFFF;
		}

		.menu-list-item {
			margin-top: 24rpx;
			margin-right: 32rpx;
			font-weight: 600;
			font-size: 32rpx;
			height: 62rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
		}

		.menu-list-txt1 {
			color: #1386FB;
		}

		.menu-list-line {
			margin-top: 12rpx;
			width: 48rpx;
			height: 6rpx;
			border-radius: 4rpx;
		}

		.menu-list-line1 {
			background: #1386FB;
		}

		.settlement {
			margin-top: 24rpx;
			width: 100%;
			height: 869rpx;
			background: #FFFFFF;
			border-radius: 16rpx;
			border: 1rpx solid #EDF0F5;
			box-sizing: border-box;
			padding: 32rpx;
		}

		.device-box {
			width: 100%;
			background: #FFFFFF;
			border-radius: 16rpx;
			border: 1rpx solid #EDF0F5;
			box-sizing: border-box;
			padding: 32rpx;

			.merchant-device-item {
				position: relative;
				padding: 24rpx 0;
				border-bottom: 1rpx solid #E8E8E8;
			}

			.merchant-device-item:last-child {
				border-bottom: none;
			}

			.merchant-device-txt1 {
				font-weight: 600;
				font-size: 32rpx;
				color: #18191A;
				line-height: 48rpx;
			}

			.merchant-device-txt2 {
				font-weight: 400;
				font-size: 26rpx;
				color: #979B9F;
				line-height: 40rpx;
			}

			.merchant-device-txt3 {
				position: absolute;
				right: 0;
				bottom: 24rpx;
				width: 96rpx;
				height: 56rpx;
				line-height: 56rpx;
				background: #FFFFFF;
				border-radius: 8rpx;
				border: 1rpx solid #1386FB;
				box-sizing: border-box;
				font-weight: 400;
				font-size: 26rpx;
				color: #1386FB;
				text-align: center;
			}

			.device-empty {
				display: flex;
				flex-direction: column;
				align-items: center;
			}

			.device-empty-img1 {
				margin-top: 50rpx;
				width: 230rpx;
				height: 172rpx;
			}

			.device-empty-txt1 {
				margin-top: 32rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: #9DA5AD;
				line-height: 40rpx;
			}

			.device-empty-txt2 {
				margin-top: 24rpx;
				width: 226rpx;
				height: 56rpx;
				line-height: 56rpx;
				background: #FFFFFF;
				border-radius: 8rpx;
				border: 1rpx solid #1386FB;
				font-weight: 400;
				font-size: 26rpx;
				color: #1386FB;
				text-align: center;
			}
		}

		.merchant-txt1 {
			font-weight: 600;
			font-size: 34rpx;
			color: #18191A;
			line-height: 52rpx;
		}

		.merchant-txt2 {
			width: 112rpx;
			height: 40rpx;
			line-height: 40rpx;
			background: #F39237;
			border-radius: 4rpx;
			font-weight: 400;
			font-size: 24rpx;
			color: #FFFFFF;
			text-align: center;
		}

		.merchant-txt3 {
			font-weight: 500;
			font-size: 28rpx;
			color: #575E65;
			line-height: 42rpx;
		}

		.merchant-txt4 {
			margin-left: 8rpx;
			color: #1386FB;
		}

		.merchant-txt5 {
			font-weight: 400;
			font-size: 26rpx;
			color: #979B9F;
			line-height: 40rpx;
		}

		.merchant-txt6 {
			font-weight: 600;
			font-size: 34rpx;
			color: #18191A;
			line-height: 48rpx;
		}

		.merchant-txt7 {
			margin-right: 16rpx;
			width: 112rpx;
			font-weight: 400;
			font-size: 28rpx;
			color: #979B9F;
			line-height: 44rpx;
		}

		.merchant-txt8 {
			font-weight: 500;
			font-size: 28rpx;
			color: #575E65;
			line-height: 44rpx;
		}

		.merchant-txt9 {
			font-size: 26rpx;
			color: #1386FB;
		}

		.merchant-txt10 {
			margin-left: 16rpx;
			line-height: 48rpx;
		}

		.merchant-line {
			width: 100%;
			height: 1rpx;
			background: #D8D8D8;
			opacity: 0.6;
		}

		.merchant-img1 {
			width: 304rpx;
			height: 200rpx;
			border-radius: 8rpx;
		}
	}
</style>