<template>
	<view class="page-store-container">
		<!-- 导航栏 -->
		<navbar title="选择门店" />
		<view class="store-txt1">请选择绑定门店</view>
		<view class="store-box">
			<view class="store-item" v-for="item in store" :key="item.id" @click="clickHandle(item)" :class="store_id == item.id?'store-item-border':''">
				<view class="store-txt2">{{item.store_name}}</view>
				<view class="flex-sty">
					<view class="store-txt3">{{item.store_contact}}</view>
					<view class="store-txt4">{{item.store_tel && item.store_tel.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')}}</view>
				</view>
				<view class="store-txt5">地址: {{item.store_address}}</view>
			</view>
		</view>
		<view class="btn-box">
			<view class="btn1" :class="store_id ? '' : 'btn2'" @click="nextHandle">下一步</view>
		</view>
		<!-- 绑定设备弹窗 -->
		<devicePopup :show="isShowBindDevice" @okHandel="bindDeviceHandle" @close="closeBindDevice"/>
	</view>
</template>

<script setup>
	import { ref } from 'vue';
	import navbar from '@/components/navbar/navbar.vue';
	import devicePopup from '@/pages/business/components/devicePopup.vue'
	import { bindDeviceApi } from '@/api/index';
	import {onLoad} from '@dcloudio/uni-app';
	
	const store = ref([])
	const store_id = ref()
	const merchant_id = ref()
	const isShowBindDevice = ref(false) //绑定设备弹窗

	/** 选择门店 */
	function clickHandle(item){
		store_id.value = item.id
	}
	
	/** 下一步按钮 */
	function nextHandle(){
		isShowBindDevice.value = true
	}
	
	/** 绑定设备 */
	async function bindDeviceHandle(code) {
	  if (!code) return uni.utils.toast('请填写设备信息');
	  const data = {
	    sn: code,
	    store_id: store_id.value,
	    merchant_id: merchant_id.value
	  };
	  const res = await bindDeviceApi(data);
	  if (res.code == 200) {
	    uni.utils.toast(res.msg);
	    setTimeout(() => {
	      isShowBindDevice.value = false;
	      uni.navigateBack()
	    }, 1000);
	  } else {
	    uni.utils.toast(res.msg);
	  }
	}
	
	/** 关闭绑定设备弹窗 */
	function closeBindDevice(){
		isShowBindDevice.value = false
	}
	
	onLoad((options)=>{
		if(options.store){
			store.value = JSON.parse(options.store)
		}
		if(options.merchant_id){
			merchant_id.value = options.merchant_id
		}
	})
</script>

<style lang="scss" scoped>
	.flex-sty {
		display: flex;
		align-items: center;
	}
	
	.page-store-container{
		box-sizing: border-box;
		padding: 32rpx;
		
		.store-box{
			margin-top: 16rpx;
			padding-bottom: 120rpx;
		}
		
		.store-item{
			width: 100%;
			height: 200rpx;
			margin-bottom: 24rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75,80,85,0.06);
			border-radius: 16rpx;
			box-sizing: border-box;
			padding: 32rpx;
			border: 2rpx solid #FFFFFF;
		}
		
		.store-item-border{
			border: 2rpx solid #1386FB;
		}
		
		.store-txt1{
			font-weight: 600;
			font-size: 34rpx;
			color: #18191A;
			line-height: 52rpx;
		}
		
		.store-txt2{
			width: 100%;
			font-weight: 500;
			font-size: 30rpx;
			color: #18191A;
			line-height: 44rpx;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
		
		.store-txt3{
			margin-top: 8rpx;
			margin-right: 16rpx;
			font-weight: 500;
			font-size: 26rpx;
			color: #979B9F;
			line-height: 40rpx;
		}
		
		.store-txt4{
			margin-top: 8rpx;
			font-weight: 400;
			font-size: 26rpx;
			color: #1386FB;
			line-height: 40rpx;
		}
		
		.store-txt5{
			width: 100%;
			margin-top: 4rpx;
			font-weight: 400;
			font-size: 26rpx;
			color: #979B9F;
			line-height: 40rpx;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
		
		.btn-box{
			position: fixed;
			left: 0;
			bottom: 0;
			width: 100%;
			height: 136rpx;
			background: #F0F2F5;
			box-sizing: border-box;
			padding: 0 32rpx;
			
			.btn1{
				width: 100%;
				height: 88rpx;
				margin-top: 24rpx;
				line-height: 88rpx;
				background: #1386FB;
				border-radius: 8rpx;
				text-align: center;
				font-weight: 600;
				font-size: 26rpx;
				color: #FFFFFF;
			}
			
			.btn2{
				background: #7ABCFF;
			}
		}
	}
	
</style>