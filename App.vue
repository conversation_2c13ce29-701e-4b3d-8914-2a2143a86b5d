<script>
import { provide } from 'vue';
import useVersionStore from '@/store/version'

export default {
	data(){
		return{
			
		}
	},
  onLaunch: function () {
    // /** 判断本地有没有token */
    const token = uni.getStorageSync('token');
    /** 没有token跳转登录页 */
    if (!token) {
     uni.reLaunch({
       url: '/pages/login/index'
     });
    } 
    console.log('App Launch');

		// 获取APP当前版本号
		/*#ifdef APP-PLUS*/
		const versionStore = useVersionStore()
		versionStore.checkUpdate()
		/*#endif*/
  },
  onShow: function () {
    console.log('App Show');
  },
  onHide: function () {
    console.log('App Hide');
  },
  setup() {
		provide('baseURL', 'https://dev.server.keyupay.com/');
    // provide('baseURL', 'https://server.keyupay.com/');
  },
	methods: {
		
	}
};
</script>

<style lang="scss">
/*每个页面公共css */
@import '@/uni_modules/uni-scss/index.scss';
@import '@/uni_modules/uview-plus/index.scss';

@import '@/style/common.scss';

/* #ifndef APP-NVUE */

// 设置整个项目的背景色
page {
  background-color: #F0F2F5;
}

/* #endif */
.example-info {
  font-size: 14px;
  color: #333;
  padding: 10px;
}
</style>