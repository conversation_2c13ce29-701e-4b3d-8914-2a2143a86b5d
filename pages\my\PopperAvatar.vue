<template>
	<view class="" style="width: 100%;">
		<up-popup :show="show">
			<view class="popup3">
				<image @click="close" style="width: 56rpx;height: 56rpx;margin-top: 130rpx;margin-left: 32rpx;"
					src="@/static/images/close.png" mode=""></image>
				<image v-if="userStore.userInfo.salesman_avatar" style="width: 100%;height: 750rpx;margin-top: 100rpx;" :src="baseURL + userStore.userInfo.salesman_avatar"></image>
				<image v-else style="width: 100%;height: 750rpx;margin-top: 100rpx;" :src="noImg"></image>
				<view class="btn-box">
					<view class="btn1" @click="isShow = true">
						<image style="width: 44rpx;height: 44rpx;" src="@/static/images/pic.png" mode=""></image>
						<view class="btn1-txt1">更换头像</view>
					</view>
				</view>
			</view>
		</up-popup>
		
		<up-popup :show="isShow" :round="10" mode="bottom">
			<view class="popup4">
				<view class="flex-sty">
					<view class="" style="width: 44rpx;height: 44rpx;"> </view>
					<view class="popup4-txt1">更换头像</view>
					<image @click="isShow = false" style="width: 44rpx;height: 44rpx;" src="@/static/images/close.png" mode=""></image>
				</view>
				<view @click="imageUploadCamera" class="popup4-txt2 margin-sty border-bot" >拍摄</view>
				<view @click="imageUploadAlbum" class="popup4-txt2">手机相册里选择</view>
			</view>
		</up-popup>
	</view>
</template>

<script lang="ts" setup>
	import { ref, reactive, computed, onMounted,inject } from 'vue';
	import useUserStore from '@/store/user';
	import { updateInfoApi } from '@/api/user';
	import noImg from '@/static/images/noAvatar.png'

	const emit = defineEmits(['closePopperAvatar', 'otupdateInfoApi'])

	const baseURL = inject('baseURL');
	const userStore = useUserStore();
	const props = defineProps({
		show: {
			type: Boolean,
			default: false
		}
	})
	
	/** 更换头像弹窗 */
	let isShow = ref(false)

	/** 相机图片上传 */
	function imageUploadCamera() {
		uni.chooseImage({
			sizeType: ['compressed'],
			sourceType: ['camera'], //从相机选择
			success: (chooseImageRes) => {
				const tempFilePaths = chooseImageRes.tempFilePaths;
				uni.uploadFile({
					url: baseURL + 'platform_api/tenant_salesman.user/fileImg',
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						'user': 'test'
					},
					header: {
						token: userStore.token
					},
					success: (res) => {
						if (JSON.parse(res.data).code !== 200) return uni.utils.toast('上传失败!')
						userStore.userInfo.salesman_avatar = JSON.parse(res.data).data.local_img
						otupdateInfoApi()
						isShow.value = false
					}
				});
			}
		});
	}
	
	/** 相册图片上传 */
	function imageUploadAlbum() {
		uni.chooseImage({
			sizeType: ['compressed'],
			sourceType: ['album'], //从相册选择
			success: (chooseImageRes) => {
				const tempFilePaths = chooseImageRes.tempFilePaths;
				uni.uploadFile({
					url: baseURL + 'platform_api/tenant_salesman.user/fileImg',
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						'user': 'test'
					},
					header: {
						token: userStore.token
					},
					success: (res) => {
						if (JSON.parse(res.data).code !== 200) return uni.utils.toast('上传失败!')
						userStore.userInfo.salesman_avatar = JSON.parse(res.data).data.local_img
						otupdateInfoApi()
						isShow.value = false
					}
				});
			}
		});
	}
	
	/** 修改个人资料接口 修改头像 */
		async function otupdateInfoApi() {
			const params = {
				id: userStore.userInfo.id,
				salesman_avatar: userStore.userInfo.salesman_avatar,
			}
			const res = await updateInfoApi(params)
			if (res.code == 200) {
				userStore.otgetUserInfoApi().then(() => {
					uni.utils.toast(res.msg)
					setTimeout(()=>{
						/** 关闭弹窗 */
						emit('closePopperAvatar', false)
					},1000)
				})
			} else {
				userStore.otgetUserInfoApi().then(() => {
					uni.utils.toast(res.msg)
				})
			}
		}

	function close() {
		emit('closePopperAvatar', false)
	}
</script>

<style lang="scss" scoped>
	.flex-sty {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.flex-sty1 {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.flex-sty2 {
		display: flex;
		align-items: center;
	}

	.flex-sty3 {
		display: flex;
		align-items: center;
		justify-content: flex-end;
	}

	.popup3 {
		width: 100%;
		height: 100vh;
		background-color: #000;

		.btn-box {
			margin-top: 96rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.btn1 {
				width: 358rpx;
				height: 80rpx;
				background: #2D3135;
				border-radius: 8rpx;
				opacity: 0.65;
				display: flex;
				align-items: center;
				justify-content: center;

				.btn1-txt1 {
					margin-left: 16rpx;
					font-weight: 400;
					font-size: 26rpx;
					color: #FFFFFF;
					font-style: normal;
				}
			}
		}
	}

	.popup4 {
		width: 100%;
		height: 412rpx;
		background: #FFFFFF;
		border-radius: 24rpx 24rpx 0rpx 0rpx;
		box-sizing: border-box;
		padding: 40rpx 32rpx;

		.popup4-txt1 {
			font-weight: 600;
			font-size: 34rpx;
			color: $uni-base-color;
			font-style: normal;
		}

		.margin-sty {
			margin-top: 24rpx;
		}
		
		.border-bot{
			box-sizing: border-box;
			border-bottom: 1rpx solid #DEE2E8;
		}

		.popup4-txt2 {
			width: 100%;
			height: 96rpx;
			line-height: 96rpx;
			text-align: center;
			font-weight: 500;
			font-size: 32rpx;
			color: $uni-main-color;
			font-style: normal;
		}
	}
</style>