<!-- 单选框 -->
<template>
	<view class="checkbox-single" @click="click">
		<view class="icon-rect" :class="{'active': isTrue}">
			<view class="active-icon" v-show="isTrue"></view>
		</view>
		<text class="text-container">{{ props.text }}</text>
	</view>
</template>

<script lang="ts" setup>
	/**
	 * 单选框
	 * @description 单选框
	 * @property {String | Number | Boolean}		modelValue	 选中、非选中状态
	 * @property {String}			text	右侧文字
	 * @property {Object}		valueType		默认抛出的值。默认：true：1，false: 0
	 * @event {Function(param)} update:modelValue  选中状态变更, param：当前选中的值
	 * @example <checkbox-single v-model="checkValue" text="停售" />
	 */
	import { computed } from 'vue'
	const props = defineProps({
		modelValue: {
			type: [String, Number, Boolean],
			default: Number
		},
		text: {
			type: String,
			defaulta: ''
		},
		/**默认抛出的值*/
		valueType: {
			type: Object,
			default() {
				return {
					trueValue: 1,
					falseValue: 0
				}
			}
		}
	})
	
	const emit = defineEmits(['update:modelValue'])
	
	const isTrue = computed(()=> {
		if(props.modelValue == props.valueType.trueValue) return true
		return false
	})
	
	const emitValue = computed(()=> {
		if(props.modelValue == props.valueType.trueValue) return props.valueType.falseValue
		if(props.modelValue == props.valueType.falseValue) return props.valueType.trueValue
		return ''
	})
	
	function click() {
		emit('update:modelValue', emitValue.value)
	}
</script>

<style lang="scss" scoped>
	.checkbox-single {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		width: fit-content;
		.icon-rect {
			width: 6.5rpx;
			height: 6.5rpx;
			border: 1px solid white;
			position: relative;
			border-radius: 1.5rpx;
			&.active {
				background-color: $uni-primary;
				border-color: transparent;
			}
			.active-icon {
				position: absolute;
				border-right-width: 1px;
				border-right-color: #fff;
				border-right-style: solid;
				border-bottom-width: 1px;
				border-bottom-color: #fff;
				border-bottom-style: solid;
				transform-origin: center;
				transform: rotate(40deg);
				top: 0.7rpx;
				left: 1.95rpx;
				height: 3rpx;
				width: 1.8rpx;
			}
		}
		
		.text-container {
			margin-left: 3rpx;
		}
		
	}
</style>