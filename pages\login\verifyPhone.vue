<template>
	<view class="page-login-container">
		<!-- 导航栏 -->
		<navbar title="" />
		<view class="login-txt1 margin-sty">验证手机号</view>
		<view class="login-txt3 margin-sty">手机号</view>
		<up-input placeholder="请输入您的手机号" v-model="mobile" border="none" clearable></up-input>
		<view class="login-txt3 margin-sty1">验证码</view>
		<up-input placeholder="请输入验证码" v-model="code" border="none">
			<template #suffix>
				<view class="login-txt4" v-if="showBtn" @click="sendCode">发送验证码</view>
				<view class="login-txt5" v-else><text class="login-txt6">{{seconds}}s</text>后重新发送</view>
			</template>
		</up-input>
		<view class="page-btn-box">
			<view class="btn-box" :class="showLoginBtn?'btn-box1':'btn-box'" @click="okHandel">确认</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, computed } from 'vue'
	import navbar from '@/components/navbar/navbar.vue'
	import { sendsmsApi,checksmsApi } from '@/api/user'
	import { onLoad } from '@dcloudio/uni-app'

	const showBtn = ref(true)
	const seconds = ref(59)
	const mobile = ref('')
	const code = ref('')

	let showLoginBtn = computed(() => {
		if (mobile.value && code.value) {
			return true
		} else {
			return false
		}
	})

	//发送验证码
	function sendCode() {
		if(!mobile.value) return uni.utils.toast('请输入手机号')
		if (!/^1[3456789]\d{9}$/.test(mobile.value)) return uni.utils.toast('手机号格式不正确');
		otsendsmsApi()
	}
	
	//发送验证码接口
	async function otsendsmsApi(){
		const data = {
			mobile: mobile.value,
			type: 1,
			scene: 22
		}
		const res = await sendsmsApi(data)
		if(res.code == 200){
			uni.utils.toast('验证码已发送')
			showBtn.value = false
			countdown()
		}else{
			uni.utils.toast(res.msg)
		}
	}

	// 倒计时函数
	function countdown() {
		let timer = setInterval(() => {
			if (seconds.value > 1) {
				seconds.value--;
			} else {
				// 倒计时结束
				clearInterval(timer);
				showBtn.value = true;
				seconds.value = 59; // 重置为初始值
			}
		}, 1000);
	}
	
	//校验手机号
	async function otchecksmsApi(){
		const data = {
			mobile: mobile.value,
			code: code.value,
			type: 1,
			scene: 22
		}
		const res = await checksmsApi(data)
		if(res.code == 200){
			uni.utils.toast(res.msg)
			setTimeout(()=>{
				jumpTo(`/pages/login/setNewPassword?phone=${mobile.value}&tmpd=${res.data.tmpd}`)
			},1000)
		}else{
			uni.utils.toast(res.msg)
		}
	}

	//确认按钮
	function okHandel() {
		if (showLoginBtn.value) {
			otchecksmsApi()
		}
	}

	/** 页面跳转 */
	function jumpTo(url) {
		uni.navigateTo({
			url: url
		});
	}
	
	onLoad((options) => {
		if (options.mobile) {
			mobile.value = options.mobile
		}
	});
</script>

<style lang="scss" scoped>
	::v-deep .u-input {
		height: 96rpx !important;
		background: #F0F2F5 !important;
		border-radius: 16rpx !important;
		box-sizing: border-box !important;
		padding: 0 32rpx !important;
	}

	::v-deep .uni-input-input {
		font-weight: 500 !important;
		font-size: 32rpx !important;
		color: #18191A !important;
	}

	::v-deep .uni-input-placeholder {
		font-weight: 400 !important;
		font-size: 32rpx !important;
		color: #9DA5AD !important;
	}

	.margin-sty {
		margin-top: 48rpx;
	}
	
	.margin-sty1{
		margin-top: 32rpx;
	}

	.page-login-container {
		width: 100%;
		height: 100%;
		background: #F0F2F5;
		color: #18191A;
		font-style: normal;
		box-sizing: border-box;
		padding: 0 32rpx;

		.login-txt1 {
			font-weight: 500;
			font-size: 44rpx;
			line-height: 60rpx;
			text-align: center;
		}

		.login-txt3 {
			margin-bottom: 16rpx;
			font-weight: 600;
			font-size: 28rpx;
			color: #18191A;
			line-height: 44rpx;
		}

		.login-txt4 {
			font-weight: 500;
			font-size: 26rpx;
			color: #1386FB;
		}

		.login-txt5 {
			font-weight: 500;
			font-size: 26rpx;
			color: #9DA5AD;
		}

		.login-txt6 {
			font-weight: 400;
			font-size: 26rpx;
			color: #1386FB;
		}
		
		.page-btn-box{
			width: 100%;
			height: 88rpx;
			position: fixed;
			left: 0;
			bottom: 64rpx;
			box-sizing: border-box;
			padding: 0 32rpx;

			.btn-box {
				width: 100%;
				height: 88rpx;
				line-height: 88rpx;
				background: #DEE2E8;
				border-radius: 8rpx;
				text-align: center;
				font-weight: 500;
				font-size: 26rpx;
				color: #9DA5AD;
			}
			
			.btn-box1 {
				background: #1386FB;
				color: #FFFFFF;
			}
		}
	}
</style>