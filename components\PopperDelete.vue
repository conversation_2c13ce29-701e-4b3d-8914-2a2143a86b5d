<!-- 确认删除弹框 -->
<template>
	<PopperContainer :title="props.title" :parentWidth="props.parentWidth" :show="isShow" @close="close">
		<view class="list-container">
			<image class="icon" src="@/static/images/icon-error.svg" mode="aspectFit"></image>
			<view class="text-container">
				<text>删除后无法恢复，确认删除<text v-show="tooltipText" class="tooltext">{{ tooltipText }}</text>吗？</text>
			</view>
		</view>
		<template v-slot:button>
			<up-button v-slot:button type="error" text="确认" @click="comfirm"></up-button>
			<up-button v-slot:button type="primary" text="取消" @click="close"></up-button>
		</template>
	</PopperContainer>
</template>

<script lang="ts" setup>
	/* 使用方法：
		import PopperDelete from '@/components/PopperDelete.vue'
		const popperDelete = ref<InstanceType<typeof PopperDelete> | null>(null)
		popperDelete.value.show()
			.then(()=> {
				
			})
		*/
	import PopperContainer from '@/components/PopperContainer.vue'
	import { ref } from 'vue'
	defineExpose({ show })
	const emit = defineEmits(['update:modelValue'])
	const props = defineProps({
		parentWidth: {
			type: String,
			default: '100%'
		},
		title: {
			type: String,
			default: '确认删除'
		}
	})
	const isShow = ref(false)
	const tooltipText = ref('')
	const resolveFn = ref(null)
	const rejectFn = ref(null)

	function close() {
		rejectFn.value()
		reset()
		isShow.value = false
	}

	function comfirm() {
		resolveFn.value()
		reset()
		isShow.value = false
	}
	
	function reset() {
		tooltipText.value = ''
		resolveFn.value = rejectFn.value = null
	}

	/** 打开弹框的方法
	*	@param {string} text 删除项的名字 
	*/
	function show(text : string = '') {
		tooltipText.value = text
		isShow.value = true
		return new Promise((resolve, reject) => {
			resolveFn.value = resolve
			rejectFn.value = reject
		})
	}
	
</script>

<style lang="scss" scoped>
	.list-container {
		width: 120rpx;
		display: flex;
		align-items: center;
		
		.icon {
			width: 14rpx;
			height: 14rpx;
			flex: none;
		}
		.text-container {
			margin-left: $uni-spacing-base;
			line-height: $uni-font-size-base * 1.5;
			.tooltext {
				font-weight: bold;
				margin: 0 2rpx;
			}
		}
	}
	
</style>