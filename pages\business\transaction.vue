<template>
	<view class="page-transaction-container">
		<!-- 导航栏 -->
		<navbar title="交易明细" />
		<view class="" v-if="traderecordList.length>0">
			<recordItem v-for="item in traderecordList" :key="item.year" :item="item"/>
		</view>
		<view class="flex-sty" v-else>
			<image class="empty-img1" src="@/static/images/empty.png" mode=""></image>
			<view class="empty-txt1">暂无交易数据</view>
		</view>
	</view>
</template>

<script setup>
	import {ref,onMounted} from 'vue';
	import navbar from '@/components/navbar/navbar.vue';
	import recordItem from '@/pages/business/components/recordItem.vue'
	import { tradeDetailApi } from '@/api/business';
	
	const traderecordList = ref([]) // 交易明细
	
	async function ottradeDetailApi(){
		const res = await tradeDetailApi()
		if(res.code == 200){
			traderecordList.value = res.data
		}else{
			uni.utils.toast(res.msg)
		}
	}
	
	onMounted(()=>{
		ottradeDetailApi()
	})
	
</script>

<style lang="scss" scoped>
	.flex-sty {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}
	
	.page-transaction-container{
		box-sizing: border-box;
		padding: 8rpx 32rpx 24rpx;
		
		.empty-img1 {
			margin-top: 200rpx;
			width: 230rpx;
			height: 172rpx;
		}
		
		.empty-txt1 {
			margin-top: 32rpx;
			font-weight: 400;
			font-size: 26rpx;
			color: #9DA5AD;
			line-height: 40rpx;
		}
	}
</style>