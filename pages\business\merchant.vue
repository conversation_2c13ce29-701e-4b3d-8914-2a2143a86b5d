<template>
	<scroll-view scroll-y class="container" @scroll="handleScroll" @scrolltolower="onReachBottom">
		<!-- 背景区域 -->
		<view class="bg-gradient"></view>

		<!-- 导航栏 -->
		<navbar title="商家列表" :backGroundColor="navBgColor" />

		<!-- 内容区域 -->
		<view class="merchant-page" style="padding-top:0;margin-top: 32rpx;">
			<!-- 搜索框 -->
			<view class="search-box" @click="jumpTo('/pages/business/search')">
				<image src="@/static/images/search-icon.png" class="search-icon" mode="aspectFit"></image>
				<input :disabled="true" type="text" class="search-input" placeholder="输入商家名称或联系人姓名电话"
					placeholder-class="placeholder" />
				<text class="search-btn">搜索</text>
			</view>

			<!-- 标签栏 -->
			<view class="tab-bar">
				<view class="tab-item" :class="{ active: currentTab == 'processing' }" @click="switchTab('processing')">
					进件中({{ processingCount }})
				</view>
				<view class="tab-item" :class="{ active: currentTab == 'all' }" @click="switchTab('all')">
					全部商家
				</view>
			</view>

			<!-- 商家列表 -->
			<view class="merchant-list">
				<view v-for="(merchant, index) in merchantList" :key="merchant.merchant_id" class="merchant-card">
					<view class="merchant-item">
						<view class="merchant-info">
							<view class="name-row">
								<text v-if="merchant.to_do_items_type >= 0 || merchant.to_do_items_type == -1"
									class="status-tag">进件中</text>
								<text class="name">{{ truncateName(merchant.bse_lice_nm) }}</text>
							</view>
							<text class="contact-name">{{ merchant.s_legal_name || '未设置' }}</text>
							<text class="contact-phone">{{ formatPhone(merchant.s_legal_phone) }}</text>
						</view>
						<view class="action" @click="handleMerchantClick(merchant)">
							<text class="action-text">{{ merchant.to_do_items_type >= 0 || merchant.to_do_items_type == -1 ? '去进件' :
								'商家详情' }}</text>
							<image src="@/static/images/right-icon.png" class="right-icon" mode="aspectFit"></image>
						</view>
					</view>
				</view>
				<!-- 加载更多 -->
				<up-loadmore :status="loadMoreStatus" @loadmore="loadMore" />
			</view>
		</view>
	</scroll-view>
</template>


<script setup>
import { ref, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import navbar from '@/components/navbar/navbar.vue'
import { statisticsApi } from '@/api/business'

// 导航栏背景色
const navBgColor = ref('transparent')

// 处理滚动事件
function handleScroll (e) {
	const scrollTop = e.detail.scrollTop
	navBgColor.value = scrollTop <= 0 ? 'transparent' : '#ffffff'
}

// 返回上一页
function goBack () {
	uni.navigateBack()
}

// 页面跳转
function jumpTo (url) {
	uni.navigateTo({ url })
}

// 当前选中的标签
const currentTab = ref('processing')
const processingCount = ref(0)
const merchantList = ref([])
const processingList = ref([])

// 分页相关
const page = ref(1)
const loadMoreStatus = ref('loadmore')

// 切换标签
async function switchTab (tab) {
	currentTab.value = tab
	page.value = 1
	loadMoreStatus.value = 'loadmore'
	merchantList.value = []

	if (tab == 'processing') {
		// 切换到进件中时，直接使用已缓存的数据
		// merchantList.value = processingList.value
		await loadMerchantList()
	} else {
		// 切换到全部时，重新请求全部数据
		await loadMerchantList()
	}
}

// 加载商家列表
async function loadMerchantList () {
	try {
		loadMoreStatus.value = 'loading'
		const params = {
			page: page.value,
			limit: 10,
			type: currentTab.value == 'processing' ? 2 : 1,
			keywords: ''
		}
		const res = await statisticsApi(params)
		if (res.code == 200) {
			const list = res.data.list

			if (currentTab.value == 'processing') {
				// 进件中列表
				if (page.value == 1) {
					processingList.value = list
					merchantList.value = list
					// 使用接口返回的总数
					processingCount.value = res.data.count
				} else {
					processingList.value.push(...list)
					merchantList.value = processingList.value
				}
			} else {
				// 全部商家列表
				if (page.value == 1) {
					merchantList.value = list
				} else {
					merchantList.value.push(...list)
				}
			}

			// 判断是否加载完成
			if (list.length < 10) {
				loadMoreStatus.value = 'nomore'
			} else {
				loadMoreStatus.value = 'loadmore'
				page.value++
			}
		}
	} catch (error) {
		console.error('获取商家列表失败:', error)
		uni.showToast({
			title: '获取数据失败',
			icon: 'none'
		})
		loadMoreStatus.value = 'loadmore'
	}
}

// 加载更多
function loadMore () {
	if (loadMoreStatus.value == 'loadmore') {
		loadMerchantList()
	}
}

// 触底加载
function onReachBottom () {
	if (loadMoreStatus.value == 'loadmore') {
		loadMerchantList()
	}
}

// 处理商家名称，超过11个字符的加上省略号
function truncateName (name) {
	if (!name) return '未命名商家'
	return name.length > 11 ? name.slice(0, 11) + '...' : name
}

// 格式化电话号码
function formatPhone (phone) {
	if (!phone) return ''
	return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')
}

// 处理商家点击
function handleMerchantClick (merchant) {
	const merchant_id = merchant.id
	uni.setStorageSync('merchant_id', merchant_id)

	if (merchant.to_do_items_type == 4) {
		uni.navigateTo({
			url: '/pages/accomplish/accomplish?id=1'
		})
		return
	} else if (merchant.to_do_items_type == 5) {
		uni.navigateTo({
			url: '/pages/accomplish/accomplish?id=1'
		})
		return
	} else if (merchant.to_do_items_type == -1) {
		uni.navigateTo({
			url: '/pages/accomplish/accomplish?id=1'
		})
		return
	}

	if (merchant.to_do_items_type >= 0) {
		// 进件中状态，根据进度跳转
		switch (merchant.save_page) {
			case 1:
				uni.navigateTo({
					url: '/pages/solutionSelection/solutionSelection?merchant_id=' + merchant_id
				})
				break
			case 2:
				uni.navigateTo({
					url: '/pages/identityCard/identityCard?merchant_id=' + merchant_id
				})
				break
			case 3:
				uni.navigateTo({
					url: '/pages/corporateIdentityCard/corporateIdentityCard?merchant_id=' + merchant_id
				})
				break
			case 4:
				uni.navigateTo({
					url: '/pages/shopScene/shopScene?merchant_id=' + merchant_id
				})
				break
			case 5:
				uni.navigateTo({
					url: '/pages/debitCard/debitCard?merchant_id=' + merchant_id
				})
				break
			default:
				// 如果没有进度，默认跳转到第一步
				uni.navigateTo({
					url: '/pages/solutionSelection/solutionSelection?merchant_id=' + merchant_id
				})
		}
	} else {
		// to_do_items_type == 0 表示已完成，跳转到详情页
		jumpTo('/pages/business/merchantDetail?merchant_id=' + merchant.merchant_id)
	}
}

onShow(() => {
	loadMerchantList()
})

</script>

<style lang="scss" scoped>
.container {
	height: 100vh;
	background: #F0F2F5;
}

.bg-gradient {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 272rpx;
	background: linear-gradient(180deg, #B3D8FF 0%, #F0F2F5 100%);
	z-index: 1;
}

.merchant-page {
	position: relative;
	z-index: 2;
	padding: 32rpx;
}

.search-box {
	margin-bottom: 40rpx;
	display: flex;
	align-items: center;
	background: #FFFFFF;
	border-radius: 16rpx;
	padding: 16rpx 24rpx;

	.search-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 16rpx;
	}

	.search-input {
		flex: 1;
		font-size: 28rpx;
		color: #18191A;
	}

	.placeholder {
		color: #979B9F;
	}

	.search-btn {
		font-weight: 400;
		font-size: 28rpx;
		color: #1386FB;
		line-height: 40rpx;
	}
}

.tab-bar {
	margin-bottom: 24rpx;
	display: flex;

	.tab-item {
		font-weight: 600;
		font-size: 32rpx;
		color: #979B9F;
		line-height: 44rpx;
		margin-right: 48rpx;
		position: relative;

		&.active {
			font-weight: 600;
			color: #1386FB;

			&::after {
				content: '';
				position: absolute;
				bottom: -16rpx;
				left: 50%;
				transform: translateX(-50%);
				width: 48rpx;
				height: 6rpx;
				background: #1386FB;
				border-radius: 4rpx;
			}
		}
	}
}

.merchant-list {
	margin-top: 40rpx;

	.merchant-card {
		background: #FFFFFF;
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.06);
		margin-bottom: 24rpx; // 卡片间距

		&:last-child {
			margin-bottom: 0;
		}
	}

	.merchant-item {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;

		.merchant-info {
			display: flex;
			flex-direction: column;

			.name-row {
				display: flex;
				align-items: center;

				.status-tag {
					display: inline-flex;
					align-items: center;
					font-size: 24rpx;
					color: #FFFFFF;
					background: #F39237;
					padding: 4rpx 12rpx;
					border-radius: 6rpx;
					margin-right: 10rpx;
					line-height: 36rpx;
					font-weight: 400;
					height: 36rpx;
				}

				.name {
					font-size: 30rpx;
					color: #18191A;
					font-weight: 400;
					line-height: 44rpx;
				}
			}

			.contact-name {
				font-size: 26rpx;
				color: #979B9F;
				font-weight: 400;
				line-height: 40rpx;
				margin-top: 8rpx;
			}

			.contact-phone {
				font-size: 26rpx;
				color: #979B9F;
				font-weight: 400;
				line-height: 40rpx;
				margin-top: 4rpx;
			}
		}

		.action {
			display: flex;
			align-items: center;

			.action-text {
				font-size: 24rpx;
				color: #979B9F;
				font-weight: 400;
				line-height: 36rpx;
			}

			.right-icon {
				width: 44rpx;
				height: 44rpx;
				margin-left: 8rpx;
			}
		}
	}
}
</style>