<template>
	<view class="team-item flex-sty">
		<view class="team-item-l flex-sty1">
			<view class="flex-sty4">
				<view class="team-txt1">{{props.item.name}}</view>
				<image style="width: 32rpx;height: 32rpx;margin-left: 8rpx;" src="@/static/images/icon1.png" mode=""></image>
				<view class="team-txt7">{{props.item.salesman_position}}</view>
			</view>
			
			<view class="">
				<view class="team-item-m flex-sty3" @click="makePhone(props.item.tel)">
					<image style="width: 28rpx;height: 28rpx;" src="@/static/images/call.png" mode=""></image>
					<view class="team-txt2">{{props.item.tel.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')}}</view>
				</view>
				<view class="flex-sty" style="margin-top: 16rpx;">
					<view class="team-txt3">本月开户</view>
					<view class="team-txt4">{{props.item.count_merchant}}</view>
					<!-- <view class="line1"></view> -->
					<view class="team-txt3">本月目标</view>
					<view class="team-txt4">{{props.item.salesman_monthly_tasks}}</view>
				</view>
			</view>
		</view>
		<view class="team-item-r flex-sty1">
			<view class="flex-sty2">
				<image v-if="props.item.salesman_avatar" style="width:84rpx;height:84rpx;border-radius: 50%;" :src="baseURL + props.item.salesman_avatar" mode=""></image>
				<image v-else style="width:84rpx;height:84rpx;border-radius: 50%;" :src="noImg" mode=""></image>
			</view>
			<view class="">
				<view class="team-txt5">本月完成度</view>
				<view v-if="props.item.proportion < 100" class="team-txt6">{{props.item.proportion}}%</view>
				<view v-else class="team-txt6" style="color:#36C60E">{{props.item.proportion}}%</view>
				<up-line-progress v-if="props.item.proportion < 100" height="8" :percentage="props.item.proportion" activeColor="#1386FB" :showText="false"></up-line-progress>
				<up-line-progress v-else height="8" :percentage="props.item.proportion" activeColor="#36C60E" :showText="false"></up-line-progress>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, reactive, computed,inject } from 'vue';
	import noImg from '@/static/images/noAvatar.png'

	const props = defineProps({
		item: {
			type: Object,
			default: {}
		}
	})

	const baseURL = inject('baseURL');

	/** 拨打电话 */
	function makePhone(value:any){
		uni.makePhoneCall({
			phoneNumber: value
		});
	}
	
</script>

<style lang="scss" scoped>
	.flex-sty{
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.flex-sty1{
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}
	
	.flex-sty2{
		display: flex;
		align-items: center;
		justify-content: flex-end;
	}
	
	.flex-sty3{
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.flex-sty4{
		display: flex;
		align-items: center;
	}
	
	.team-item{
		width: 100%;
		height: 260rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75,80,85,0.04);
		border-radius: 16rpx;
		border: 1rpx solid #EDF0F5;
		margin-bottom: 24rpx;
		box-sizing: border-box;
		padding: 32rpx;
		
		.team-item-l{
			width: 60%;
			height: 100%;
			
			.team-item-m{
				width: 260rpx;
				height: 56rpx;
				background: #E5F2FF;
				border-radius: 28rpx;
			}
			
			.team-txt1{
				max-width: 60%;
				height: 48rpx;
				font-weight: 600;
				font-size: 32rpx;
				color: $uni-main-color;
				line-height: 48rpx;
				font-style: normal;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
			
			.team-txt2{
				margin-left: 14rpx;
				height: 40rpx;
				font-family: MiSans, MiSans;
				font-weight: 500;
				font-size: 24rpx;
				color: $uni-primary;
				line-height: 40rpx;
				font-style: normal;
			}
			
			.team-txt3{
				font-weight: 500;
				font-size: 26rpx;
				color: $uni-main-color;
			}
			
			.team-txt4{
				width: 60rpx;
				font-weight: 500;
				font-size: 24rpx;
				color: $uni-primary;
				font-style: normal;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
			
			.team-txt7{
				margin-left: 8rpx;
				height: 40rpx;
				font-weight: 500;
				font-size: 24rpx;
				color: $uni-secondary-color;
				line-height: 40rpx;
				font-style: normal;
				text-align: right;
			}
			
			.line1{
				width: 1rpx;
				height: 8rpx;
				background: $uni-secondary-color;
			}
		}
		
		.team-item-r{
			width: 36%;
			height: 100%;
			
			.team-txt5{
				height: 40rpx;
				font-weight: 500;
				font-size: 22rpx;
				color: $uni-secondary-color;
				line-height: 40rpx;
				font-style: normal;
				text-align: right;
			}
			
			.team-txt6{
				height: 44rpx;
				font-weight: 600;
				font-size: 28rpx;
				color: $uni-primary;
				line-height: 44rpx;
				font-style: normal;
				text-align: right;
			}
		}
	}
</style>