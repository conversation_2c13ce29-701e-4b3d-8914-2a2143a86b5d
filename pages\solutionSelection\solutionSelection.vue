<template>
	<view class="">
		<!-- 导航栏 -->
		<navbar title="开通支付" backGroundColor="#fff" />
		<!-- 步骤条 -->
		<StepBar :currentStep="0" />
	</view>
	<view class="content">
		<view class="schemeSelection">
			<view class="title">支付方案选择</view>
			<view class="u-page__item">
				<up-collapse accordion :border="false">
					<up-collapse-item title="通道选择" disabled :value="selectedChannelLabel || ''"
						@click="openChannel"></up-collapse-item>
					<up-collapse-item v-if="selectedChannelLabel !== '长沙银行'" title="设备押金" v-show="selectedChannelLabel == '长沙银行'"
						disabled :value="''" @click="openDeposit"></up-collapse-item>
					<up-collapse-item title="活动报名" disabled :value="selectedActivityLabel || ''"
						@click="openActivity"></up-collapse-item>
					<up-collapse-item v-if="selectedChannelLabel !== '长沙银行'" title="所属行业" disabled @click="openIndustry"
						:value="industry || ''"></up-collapse-item>
					<up-collapse-item title="结算方式" disabled :value="selectedSettlementTypeLabel || ''"
						@click="openSettlement"></up-collapse-item>
					<up-collapse-item title="费率模板" disabled :value="selectedRateLabel ? `${selectedRateLabel}` : ''"
						@click="openRate"></up-collapse-item>
				</up-collapse>
			</view>
		</view>

		<view class="" style="width: 100%; height: 226rpx" />

		<view class="bottomBox">
			<view class="box">
				<view class="confirm" :style="{ backgroundColor: '#1386fb' }" @click="confirm">
					<view class="text">确定</view>
				</view>
			</view>
		</view>

		<up-popup mode="bottom" :show="industryActive" @close="closeIndustry">
			<view class="popup">
				<view class="titleBox">
					<view class="">
						<text class="title">所属行业</text>
					</view>
					<image src="@/static/images/del.svg" mode="widthFix" style="width: 32rpx; margin-left: 230rpx"
						@click="closeIndustry"></image>
				</view>
				<view class="tab">
					<view class="tab-item" @click="resetIndustrySelection" :class="{ active: !selectedIndustry.level1 }">
						{{ selectedIndustry.label1 || '请选择' }}
					</view>
					<view class="tab-item" v-if="selectedIndustry.level1" :class="{ active: selectedIndustry.level1 }">请选择</view>
				</view>
				<view class="list">
					<view v-if="!selectedIndustry.level1">
						<view class="listEvery" v-for="(item, index) in industryLevels" :key="item.value"
							@click="selectIndustryLevel1(index)">
							<view class="every" :class="{ selected: item.value === selectedIndustry.level1 }">{{ item.label }}</view>
						</view>
					</view>
					<view v-else>
						<view class="listEvery" v-for="(item, index) in industryLevels_level2" :key="item.value"
							@click="selectIndustryLevel2(index)">
							<view class="every" :class="{ selected: item.value === selectedIndustry.level2 }">{{ item.label }}</view>
						</view>
					</view>
				</view>
			</view>
		</up-popup>

		<up-popup mode="bottom" :show="channelActive" @close="closeChannel">
			<view class="popup">
				<view class="titleBox">
					<view class="">
						<text class="title">通道选择</text>
					</view>
					<image src="@/static/images/del.svg" mode="widthFix" style="width: 32rpx; margin-left: 230rpx"
						@click="closeChannel"></image>
				</view>
				<view class="list">
					<view class="listEvery" v-for="(item, index) in channelList" :key="item.id" @click="selectChannel(item)">
						<view class="every" :class="{ selected: item.id === selectedChannel }">{{ item.label }}</view>
					</view>
				</view>
			</view>
		</up-popup>

		<up-popup mode="bottom" :show="isDepositQrcodeShow" @close="closeDeposit">
			<view class="popup">
				<view class="titleBox">
					<view class="">
						<text class="title">收取押金</text>
					</view>
					<image src="@/static/images/del.svg" mode="widthFix" style="width: 32rpx; margin-left: 230rpx"
						@click="closeDeposit"></image>
				</view>
				<view class="list">
					<img style="padding: 15px 15px 15px 15px" :src="qrurl" mode="" />
				</view>
			</view>
		</up-popup>

		<up-popup mode="bottom" :show="activityActive" @close="closeActivity">
			<view class="popup">
				<view class="titleBox">
					<view class="">
						<text class="title">活动报名</text>
					</view>
					<image src="@/static/images/del.svg" mode="widthFix" style="width: 32rpx; margin-left: 230rpx"
						@click="closeActivity"></image>
				</view>
				<view class="list">
					<view class="listEvery" v-for="(item, index) in activityList" :key="item.value" @click="selectActivity(item)">
						<view class="every" :class="{ selected: item.value === selectedActivityValue }">{{ item.label }}</view>
					</view>
				</view>
			</view>
		</up-popup>

		<up-popup mode="bottom" :show="rateActive" @close="closeRate">
			<view class="popup">
				<view class="titleBox">
					<view class="">
						<text class="title">费率模板</text>
					</view>
					<image src="@/static/images/del.svg" mode="widthFix" style="width: 32rpx; margin-left: 230rpx"
						@click="closeRate"></image>
				</view>
				<view class="list">
					<view class="listEvery" v-for="(item, index) in rateList" :key="item.id" @click="selectRate(item)"
						style="line-height: normal; margin-top: 10rpx; height: 78rpx">
						<view class="every" :class="{ selected: item.id === selectedRate }">
							{{ item.label }}
							<p class="text">D0: {{ item.d0_term }}, D1: {{ item.d1_term }}, T0: {{ item.t0_term }}, T1:
								{{ item.t1_term }}
							</p>
						</view>
					</view>
				</view>
			</view>
		</up-popup>

		<up-popup mode="bottom" :show="settlementActive" @close="closeSettlement">
			<view class="popup">
				<view class="titleBox">
					<view class="">
						<text class="title">结算方式</text>
					</view>
					<image src="@/static/images/del.svg" mode="widthFix" style="width: 32rpx; margin-left: 230rpx"
						@click="closeSettlement"></image>
				</view>
				<view class="list">
					<view class="listEvery" v-for="(item, index) in settlementTypeList" :key="item.value"
						@click="selectSettlement(item)">
						<view class="every" :class="{ selected: item.value === selectedSettlementTypeLabel }">{{ item.label }}
						</view>
					</view>
				</view>
			</view>
		</up-popup>
	</view>
</template>
<script setup lang="ts">
	import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
	import navbar from '@/components/navbar/navbar.vue';
	import StepBar from '@/components/steps.vue';
	import { mapChannels, getRateTemplate, updateMerchantInfo, getActivity } from '@/api/solutionSelection';
	import { easypay_mcc } from '@/utils/easypay_mcc.js';
	import { wangpu_mcc } from '@/utils/wangpu_mcc.js';
	import { getMerchantApi } from '@/api/merchant';

	/** 获取位置信息 */
	function otgetLocation() {
		uni.getLocation({
			type: 'gcj02', // 返回可以用于uni.openLocation的经纬度
			success: function (res) {
				/** 把位置信息存储到本地 */
				uni.setStorageSync('longitude', res.longitude);
				uni.setStorageSync('latitude', res.latitude);
			}
		});
	}

	const industryActive = ref(false); // 行业选择状态
	const channelActive = ref(false); // 通道选择状态
	const activityActive = ref(false); // 活动选择状态
	const rateActive = ref(false); // 费率选择状态
	const settlementActive = ref(false); // 结算方式选择状态
	const industry = ref(''); // 行业选择值
	let merchant_id = ref(''); // 商户添加页面传过来的id

	// 行业数据
	const industryLevels = ref([]);
	const industryLevels_level2 = ref([]);
	const channelList = ref([]); // 通道
	const activityList = ref([]); // 活动
	const rateList = ref([]); // 费率模板
	const settlementTypeList = ref([]); // 结算方式
	const isDepositQrcodeShow = ref(false); // 是否显示设备押金付款码
	const qrurl = ref(''); // 押金二维码地址

	const selectedChannel = ref('');
	const selectedChannelLabel = ref('');
	const selectedChannelValue = ref('');
	const selectedRateLabel = ref('');
	const selectedRateRate = ref('');
	const selectedActivityLabel = ref('');
	const selectedActivityValue = ref('');
	const selectedRate = ref('');
	const selectedSettlementTypeLabel = ref('');

	const fetchChannels = async (current_gangway : any) => {
		const res = await mapChannels(merchant_id.value);
		if (res.code === 200) {
			channelList.value = res.data.map((item : any) => ({
				id: item.gangway_config,
				label: item.gangway_name,
				value: item.gangway_config,
				qrurl: item.qrurl,
				checked: false
			}));
			channelList.value.map((item) => {
				if (item.value == 'changsha_bank') {
					qrurl.value = item.qrurl;
				}
				if (item.value == current_gangway) {
					selectedChannel.value = item.id; // 设置默认选中第一个通道
					selectedChannelLabel.value = item.label;
					selectedChannelValue.value = item.value;
					item.checked = true;
				}
			});
		} else {
			uni.showToast({ title: res.msg, icon: 'none' });
		}
	};

	const fetchActivity = async (current_gangway : any) => {
		const res = await getActivity(current_gangway);
		if (res.code === 200) {
			activityList.value = res.data.map((item : any) => ({
				value: item.id,
				label: item.name,
				content: item.content,
				checked: false
			}));
			if (selectedActivityValue.value == '') {
				selectedActivityLabel.value = '';
			} else {
				activityList.value.map((item) => {
					if (item.value == selectedActivityValue.value) {
						selectedActivityLabel.value = item.label;
						item.checked = true;
					}
				});
			}
		} else {
			uni.showToast({ title: res.msg, icon: 'none' });
		}
	};

	const fetchRateTemplate = async (bank_name) => {
		const res = await getRateTemplate(bank_name);
		if (res.code === 200) {
			rateList.value = res.data.map((item) => ({
				id: item.id,
				label: item.name,
				d0_term: item.d0_term,
				d1_term: item.d1_term,
				t0_term: item.t0_term,
				t1_term: item.t1_term,
				unionscan_term: item.unionscan_term,
				checked: false
			}));
		} else {
			uni.showToast({ title: res.msg, icon: 'none' });
		}
	};

	onBeforeUnmount(async () => {
		handleBeforeUnload();
	});

	onMounted(async () => {
		otgetLocation();
		merchant_id.value = uni.getStorageSync('merchant_id');
		const response = await getMerchantApi(merchant_id.value);
		if (response.code === 200) {
			const data = response.data;
			if (data.current_gangway) {
				selectedChannel.value = data.current_gangway;
				selectedChannelValue.value = data.current_gangway;
				selectedSettlementTypeLabel.value = data.settlement_period;
				selectedActivityValue.value = data.activity_id;
				// 自动调用 fetchRateTemplate 获取费率模板，使用当前通道的 label 作为 bank_name
				const bank_name = selectedChannelValue.value;
				if (bank_name) {
					await fetchRateTemplate(bank_name);
				}
				if (selectedChannel.value == 'wangpu' || selectedChannel.value == 'changsha_bank') {
					await fetchActivity(data.current_gangway);
					await fetchSettlemnet(data.current_gangway);
					await fetchChannels(data.current_gangway);
					if (data.merMcc) {
						industryLevels.value = reactive(wangpu_mcc);
						let arrData = industryLevels.value.find((item) => item.value == data.merMcc[0]);
						let arrData2 = arrData.children.find((item) => item.value == data.merMcc[1]);
						industryLevels_level2.value = arrData.children;
						selectedIndustry.level1 = arrData.value;
						selectedIndustry.label1 = arrData.label;
						selectedIndustry.level2 = arrData2.value;
						selectedIndustry.label2 = arrData2.label;
						industry.value = `${selectedIndustry.label1} - ${selectedIndustry.label2}`;
					} else {
						await fetchChannels(data.current_gangway);
						await fetchActivity(data.current_gangway);
					}
				} else {
					await fetchSettlemnet(data.current_gangway);
					await fetchChannels(data.current_gangway);
					await fetchActivity(data.current_gangway);
					if (data.merType) {
						industryLevels.value = reactive(easypay_mcc);
						let eData = industryLevels.value.find((item) => item.value == data.merType);
						selectedIndustry.level1 = eData.value;
						selectedIndustry.label1 = eData.label;
						industry.value = `${eData.label}`;
					} else {
						await fetchActivity(data.current_gangway);
						await fetchChannels(data.current_gangway);
					}
				}
				selectedRate.value = data.rate_template_id;
				rateList.value.map((item) => {
					if (item.id == selectedRate.value) {
						selectedRateLabel.value = item.label;
						item.checked = true;
					}
				});
			} else {
				await fetchChannels(data.current_gangway);
				await fetchActivity(data.current_gangway);
			}
		}
		await fetchSettlemnet('changsha_bank');
		await fetchChannels('');
	});

	const handleBeforeUnload = async () => {
		const form = {
			current_gangway: selectedChannelValue.value,
			rate_template_id: selectedRate.value,
			id: merchant_id.value,
			mcc1: selectedIndustry.level1,
			type: selectedSettlementTypeLabel.value,
			activity_id: selectedActivityValue.value,
			temp_save: 1,
			mcc2: selectedIndustry.level2
		};
		await updateMerchantInfo(form);
	};

	const fetchSettlemnet = async (current_gangway : any) => {
		if (current_gangway == 'easypay') {
			settlementTypeList.value = [
				{
					value: 'D0',
					label: 'D0',
					checked: false
				},
				{
					value: 'D1',
					label: 'D1',
					checked: false
				},
				{
					value: 'T1',
					label: 'T1',
					checked: false
				}
			];
		} else {
			settlementTypeList.value = [
				{
					value: 'D0',
					label: 'D0',
					checked: false
				},
				{
					value: 'D1',
					label: 'D1',
					checked: false
				}
			];
		}
		if (current_gangway == 'changsha_bank') {
			settlementTypeList.value = [
				{
					value: 'D1',
					label: 'D1',
					checked: false
				},
			];
		} else {
			settlementTypeList.value = [
				{
					value: 'D0',
					label: 'D0',
					checked: false
				},
				{
					value: 'D1',
					label: 'D1',
					checked: false
				}
			];
		}
	};

	const openDeposit = () => {
		isDepositQrcodeShow.value = true;
	};

	const closeDeposit = () => {
		isDepositQrcodeShow.value = false;
	};

	const openChannel = () => {
		channelActive.value = true;
	};

	const closeChannel = () => {
		channelActive.value = false;
	};

	const selectChannel = (item) => {
		selectedChannel.value = item.id;
		selectedChannelLabel.value = item.label;
		selectedChannelValue.value = item.value;

		// 将选择的银行通道名称保存到 bank_name 字段
		const bank_name = selectedChannelLabel.value;

		// 根据选择的通道值设置结算方式列表
		if (selectedChannelValue.value === 'easypay') {
			settlementTypeList.value = [
				{ value: 'D0', label: 'D0', checked: false },
				{ value: 'D1', label: 'D1', checked: false },
				{ value: 'T1', label: 'T1', checked: false }
			];
		} else {
			settlementTypeList.value = [
				{ value: 'D0', label: 'D0', checked: false },
				{ value: 'D1', label: 'D1', checked: false }
			];
		}

		// 判断是否为长沙银行通道
		if (selectedChannelValue.value === 'changsha_bank') {
			settlementTypeList.value = [{ value: 'D1', label: 'D1', checked: false }];
			activityActive.value = false; // 隐藏活动报名
		}

		// 调用 fetchRateTemplate 函数，并传递 bank_name 参数
		fetchRateTemplate(bank_name);

		// 清空其他内容的值
		selectedActivityLabel.value = '';
		selectedActivityValue.value = '';
		selectedRateLabel.value = '';
		selectedRateRate.value = '';
		selectedRate.value = '';
		selectedSettlementTypeLabel.value = '';
		selectedIndustry.level1 = '';
		selectedIndustry.level2 = '';
		selectedIndustry.label1 = '';
		selectedIndustry.label2 = '';
		industry.value = '';
		closeChannel();
	};

	const openActivity = () => {
		activityActive.value = true;
	};

	const closeActivity = () => {
		activityActive.value = false;
	};

	const selectActivity = (item : any) => {
		selectedActivityValue.value = item.value;
		selectedActivityLabel.value = item.label;
		closeActivity();
	};

	const openRate = () => {
		rateActive.value = true;
	};

	const closeRate = () => {
		rateActive.value = false;
	};

	const selectRate = (item : any) => {
		selectedRate.value = item.id;
		selectedRateLabel.value = item.label;
		selectedRateRate.value = `${item.d0_term},${item.d1_term}`;
		closeRate();
	};

	const openSettlement = () => {
		settlementActive.value = true;
	};

	const closeSettlement = () => {
		settlementActive.value = false;
	};

	const selectSettlement = (item : any) => {
		selectedSettlementTypeLabel.value = item.label;
		closeSettlement();
	};

	// 所属行业打开后做点什么...
	function openIndustry() {
		if (selectedChannel.value == 'wangpu' || selectedChannel.value == 'changsha_bank') {
			industryLevels.value = reactive(wangpu_mcc);
		} else {
			industryLevels.value = reactive(easypay_mcc);
		}
		if (industryLevels.value.length === 1 && !industryLevels.value[0].children) {
			selectedIndustry.level1 = industryLevels.value[0].value;
			selectedIndustry.label1 = industryLevels.value[0].label;
			industry.value = selectedIndustry.label1;
			industryActive.value = false;
		} else {
			industryActive.value = true;
			// 重置选择状态
			selectedIndustry.level1 = '';
			selectedIndustry.level2 = '';
			selectedIndustry.label1 = '';
			selectedIndustry.label2 = '';
			industry.value = '';
		}
	}

	// 所属行业关闭后做点什么...
	function closeIndustry() {
		industryActive.value = false;
	}

	const selectedIndustry = reactive({
		level1: '',
		level2: '',
		label1: '',
		label2: ''
	});

	const selectIndustryLevel1 = (item : any) => {
		selectedIndustry.level1 = industryLevels.value[item].value;
		selectedIndustry.label1 = industryLevels.value[item].label;
		industry.value = selectedIndustry.label1; // 更新 industry 显示标签
		if (industryLevels.value[item].children && industryLevels.value[item].children.length > 0) {
			industryLevels_level2.value = industryLevels.value[item].children;
		} else {
			industryActive.value = false;
		}
	};

	const selectIndustryLevel2 = (item : any) => {
		selectedIndustry.level2 = industryLevels_level2.value[item].value;
		selectedIndustry.label2 = industryLevels_level2.value[item].label;
		industry.value = `${selectedIndustry.label1} - ${selectedIndustry.label2}`;
		closeIndustry();
	};

	// 返回上一级行业选择
	const resetIndustrySelection = () => {
		selectedIndustry.level1 = '';
		selectedIndustry.label1 = '';
		industryLevels_level2.value = [];
	};

	// 确定按钮
	const confirm = async () => {
		// 检查是否选择了通道
		if (!selectedChannelValue.value) {
			uni.showToast({
				title: '请选择通道',
				icon: 'none'
			});
			return;
		}
		// 如果选择的不是长沙银行通道，则校验活动报名是否选择
		if (selectedChannelLabel.value !== '长沙银行' && selectedActivityValue.value === '') {
			uni.showToast({
				title: '请选择活动',
				icon: 'none'
			});
			return;
		}
		// 检查是否选择了所属行业
		if (selectedChannelLabel.value !== '长沙银行' && !selectedIndustry.level1) {
			uni.showToast({
				title: '请选择所属行业',
				icon: 'none'
			});
			return;
		}
		// 检查是否选择了结算方式
		if (!selectedSettlementTypeLabel.value) {
			uni.showToast({
				title: '请选择结算方式',
				icon: 'none'
			});
			return;
		}
		// 检查是否选择了费率模板
		if (!selectedRate.value) {
			uni.showToast({
				title: '请选择费率模板',
				icon: 'none'
			});
			return;
		}
		const form = {
			current_gangway: selectedChannelValue.value,
			rate_template_id: selectedRate.value,
			id: merchant_id.value,
			activity_id: selectedActivityValue.value,
			mcc1: selectedIndustry.level1,
			type: selectedSettlementTypeLabel.value,
			temp_save: 0,
			mcc2: selectedIndustry.level2,
			longitude: uni.getStorageSync('longitude') || '', // 获取经度，如果没有则为空
			latitude: uni.getStorageSync('latitude') || '' // 获取纬度，如果没有则为空
		};

		const res = await updateMerchantInfo(form);
		if (res.code === 200) {
			// 将 save_page 存储为 1
			uni.setStorageSync('save_page', 1);
			uni.showToast({ title: res.msg, icon: 'none' });
			uni.redirectTo({
				url: '/pages/identityCard/identityCard'
			});
		} else {
			uni.showToast({ title: res.msg, icon: 'none' });
		}
	};
</script>

<style lang="scss" scoped>
	::v-deep .u-steps-item__wrapper__circle__text {
		line-height: normal;
	}

	::v-deep .u-icon__icon {
		color: #8f959c !important;
	}

	::v-deep .u-cell__body {
		background-color: #ffffff;
		height: 108rpx;
		box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
		border-radius: 16rpx;
		border: 1rpx solid #edf0f5;
	}

	::v-deep .u-collapse-item {
		&:nth-child(n + 2) {
			margin-top: 12px;
		}
	}

	::v-deep .u-collapse-item__content__text {
		background-color: #e5e8ed;
		box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
		border-radius: 0 0 16rpx 16rpx;
		line-height: 148rpx !important;
	}

	.content {
		width: 100%;
		height: 100%;
		padding-top: 164rpx;

		.popup {
			height: 1120rpx;
			background-color: #ffffff;
			border-radius: 24rpx 24rpx 0rpx 0rpx !important;

			.titleBox {
				display: flex;
				margin-left: 304rpx;
				margin-top: 40rpx;

				.text-left {
					float: left;
				}
			}

			.tab {
				display: flex;
				justify-content: flex-start;
				border-bottom: 1rpx solid #edf0f5;

				.tab-item {
					padding: 20rpx 30rpx;
					font-size: 28rpx;
					color: #575e65;

					&.active {
						color: #1386fb;
						border-bottom: 6rpx solid #1386fb;
					}
				}
			}

			.list {
				height: calc(100% - 92rpx);
				overflow-y: scroll;
				padding: 0 32rpx;

				.listEvery {
					height: 92rpx;
					margin-top: 10rpx;
					line-height: 92rpx;
					padding-bottom: 10rpx;
					border-bottom: 1rpx solid #edf0f5;

					.every {
						font-size: 28rpx;
						color: #18191a;
						padding: 0 32rpx;

						.text {
							font-weight: 400;
							font-size: 24rpx;
							color: #9da5ad;
							margin-top: 8rpx;
						}

						&.selected {
							background-color: #e5f2ff;
							border-radius: 8rpx;
						}
					}
				}

				.listEvery:last-child {
					padding-bottom: 92rpx; // 为最后一个元素增加额外的底部内边距
					border-bottom: none;
				}
			}
		}

		.steps {
			width: 100%;
			height: 164rpx;
			background-color: #ffffff;
		}

		.schemeSelection {
			padding: 0 32rpx;
			margin-top: 56rpx;
			margin-bottom: 280rpx;

			.title {
				font-size: 44rpx;
				margin-bottom: 32rpx;
			}

			::v-deep .u-cell__title {
				uni-text {
					color: #18191a;
					font-weight: 500;
					font-size: 30rpx;
				}
			}

			::v-deep uni-text {
				font-weight: 600;
				font-size: 28rpx;
				color: #1386fb;
			}

			.radio-item-container {
				border-radius: 10px;
				background-color: white;

				&:nth-child(n + 2) {
					margin-top: 12px;
				}

				&.active {
					background-color: #1386fb;
					color: #fff;

					.radioList {
						color: #fff;
					}
				}

				.label-container {
					display: flex;
					margin-left: 14rpx;
					align-items: center;

					.radioList {
						font-size: 26rpx;
						font-weight: 500;

						.text {
							font-size: 24rpx;
							font-weight: 400;
							color: #9da5ad;
						}
					}
				}
			}
		}

		.industryInvolved {
			margin-top: 56rpx;

			.title {
				font-size: 34rpx;
			}

			.select {
				background-color: #fff;
				height: 108rpx;
				box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(75, 80, 85, 0.04);
				border-radius: 16rpx;
				border: 1rpx solid #edf0f5;
				margin-top: 12rpx;

				.text {
					display: flex;
					line-height: 108rpx;
					padding: 0 32rpx;
					justify-content: space-between;
				}

				.left {
					font-size: 30rpx;
					color: $uni-secondary-color;
				}
			}
		}

		.bottomBox {
			position: fixed;
			box-shadow: 0rpx -2rpx 4rpx 0rpx rgba(75, 80, 85, 0.06);
			background: #ffffff;
			margin-top: 58rpx;
			width: 100%;
			height: 172rpx;
			bottom: 0;
			left: 0;
			padding: 0 32rpx;
			z-index: 999;

			.box {
				display: flex;

				.confirm {
					width: 686rpx;
					height: 88rpx;
					background-color: #b3d8ff;
					border-radius: 8rpx;
					margin-top: 24rpx;
					margin-right: 16rpx;

					.text {
						line-height: 88rpx;
						text-align: center;
						color: #ffffff;
						font-weight: 500;
						font-size: 26rpx;
					}
				}
			}
		}
	}

	// 单选按钮选中时的背景色
	.selected {
		background-color: #e5f2ff !important;
		font-weight: 600;
		font-size: 30rpx;
		color: #1386fb;
	}

	.white-text {
		color: #ffffff !important;
	}
</style>